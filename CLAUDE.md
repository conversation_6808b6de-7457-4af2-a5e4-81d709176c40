# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Start development server (dashboard package)
yarn start
yarn dev

# Type checking
yarn type-check
yarn type-check:watch
```

### Building
```bash
# Build all packages
yarn build

# Build individual packages
yarn build:shared
yarn build:dashboard

# Development builds
yarn build:dev
yarn build:clean
```

### Testing
```bash
# Unit tests (Vitest)
yarn test
yarn test:watch
yarn test:coverage

# End-to-end tests (Playwright)
yarn test:e2e
yarn test:e2e:ui

# Component development (Storybook)
yarn storybook
yarn build-storybook
```

### Code Quality
```bash
# ESLint
yarn lint

# Code health analysis
yarn health
yarn health:analyze
yarn health:scripts
yarn health:cleanup
yarn health:cleanup:dry
```

### Maintenance
```bash
# Clean artifacts
yarn clean
yarn clean:deep

# Manage dependencies
yarn check-versions
yarn fix-versions

# Diagnostics
yarn diagnostics
```

### Deployment
```bash
# Deploy to Google Apps Script
yarn deploy
yarn deploy:all

# Deploy to GitHub Pages
yarn deploy:gh-pages
```

## Architecture

This is a **monorepo** for a React-based trading dashboard designed for Google Apps Script integration with a Formula 1 racing theme.

### Package Structure
- **`packages/shared`** - Reusable components, hooks, theme system, and utilities
- **`packages/dashboard`** - React application with feature-specific components
- **Core package** (referenced but not present in structure) - Google Apps Script integration layer

### Dependency Flow
```
shared → dashboard
```

### Key Architectural Patterns

**Atomic Design**
- Components organized as atoms → molecules → organisms → templates
- Located in `packages/shared/src/components/`

**Feature-Based Organization**
- Features isolated in `packages/dashboard/src/features/`
- Each feature contains: components, hooks, types, state, API services
- Examples: `trade-analysis`, `trade-journal`, `daily-guide`, `performance-dashboard`

**Path Aliases**
- `@adhd-trading-dashboard/shared` → `packages/shared/src`
- `@adhd-trading-dashboard/dashboard` → `packages/dashboard/src`

### Technical Stack
- **Framework**: React 18 + TypeScript
- **Styling**: styled-components with F1 racing theme
- **Build**: Vite (dev) + Webpack (production)
- **Testing**: Vitest (unit) + Playwright (E2E) + Storybook (components)
- **Package Management**: Yarn workspaces

### Special Considerations

**Google Apps Script Integration**
- 250KB file size limit requires code splitting
- Progressive loading for performance
- Server-side communication layer

**Theme System**
- F1 racing-inspired design language
- Consistent styling across components
- Performance-focused animations

**State Management**
- Context-based architecture
- Feature-specific state isolation
- Type-safe API layer with retry logic

### Code Health System
The repository includes comprehensive code analysis tools:
- Automated refactoring and cleanup (`yarn health:cleanup`)
- Code complexity analysis (`yarn health:analyze`)
- Script organization and deduplication (`yarn health:scripts`)
- Always use Yarn commands for monorepo compatibility

### Build Configuration
- TypeScript with strict mode enabled
- Project references for optimal build performance
- Shared configuration across packages
- Bundle analysis available via `yarn analyze`

### Development Workflow
1. Install dependencies: `yarn install`
2. Start development: `yarn start`
3. Run tests: `yarn test`
4. Type check: `yarn type-check`
5. Code health check: `yarn health:analyze`
6. Build: `yarn build`

The codebase follows a disciplined monorepo architecture with clear separation of concerns, comprehensive tooling, and production-ready patterns optimized for Google Apps Script deployment.