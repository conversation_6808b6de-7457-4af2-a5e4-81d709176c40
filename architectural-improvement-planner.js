#!/usr/bin/env node

/**
 * React Architectural Improvement Planner
 *
 * Creates detailed refactoring plans with dependency tracking for architectural improvements
 * Designed to run after codebase-analyzer.js to provide actionable restructuring guidance
 *
 * Usage:
 * node architectural-improvement-planner.js [directory]
 */

import fs from 'fs';
import path from 'path';

class ArchitecturalImprovementPlanner {
  constructor(rootDir = '.') {
    this.rootDir = path.resolve(rootDir);
    this.currentAnalysis = null;
    this.improvementPlan = {
      folderRestructure: new Map(),
      componentSplits: [],
      dependencyMigrations: [],
      hookExtractions: [],
      serviceExtractions: [],
      testingPlan: [],
      migrationSteps: [],
      breakingChanges: [],
      rollbackPlan: [],
    };
    this.dependencyGraph = new Map();
    this.importMap = new Map();
  }

  async analyze() {
    console.log(`🏗️  ARCHITECTURAL IMPROVEMENT PLANNER`);
    console.log(`📁 Analyzing: ${this.rootDir}`);
    console.log('='.repeat(80));

    // Re-scan the codebase for current state
    await this.scanCodebase();

    // Build dependency graph
    this.buildDependencyGraph();

    // Create improvement plans
    this.createFolderRestructurePlan();
    this.createComponentSplitPlan();
    this.createHookExtractionPlan();
    this.createServiceExtractionPlan();
    this.createTestingPlan();
    this.createMigrationPlan();

    // Generate comprehensive report
    this.generateImprovementReport();
  }

  async scanCodebase() {
    this.files = [];
    this.components = [];
    this.hooks = [];
    this.services = [];

    await this.scanDirectory(this.rootDir);
    console.log(`📊 Scanned ${this.files.length} files`);
  }

  async scanDirectory(dir, depth = 0) {
    if (depth > 10) return;

    let items;
    try {
      items = fs.readdirSync(dir);
    } catch (error) {
      return;
    }

    const dirName = path.basename(dir);
    if (this.shouldSkipDirectory(dirName)) return;

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        await this.scanDirectory(fullPath, depth + 1);
      } else if (stat.isFile()) {
        await this.analyzeFileForImprovement(fullPath);
      }
    }
  }

  shouldSkipDirectory(dirName) {
    const skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage'];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  async analyzeFileForImprovement(filePath) {
    const fileName = path.basename(filePath);
    const ext = path.extname(fileName);

    if (!['.js', '.jsx', '.ts', '.tsx'].includes(ext)) return;

    let content = '';
    try {
      content = fs.readFileSync(filePath, 'utf8');
    } catch {
      return;
    }

    const fileInfo = {
      name: fileName,
      path: path.relative(this.rootDir, filePath),
      fullPath: filePath,
      content,
      size: content.length,
      lines: content.split('\n').length,
      imports: this.extractImports(content),
      exports: this.extractExports(content),
      dependencies: this.extractDependencies(content),
      responsibilities: this.analyzeResponsibilities(content),
      complexity: this.calculateComplexity(content),
      isComponent: this.isReactComponent(fileName, content),
      isHook: this.isHook(fileName, content),
      isService: this.isService(fileName, content),
      hasTests: this.hasCorrespondingTests(filePath),
      couplingIssues: this.identifyCouplingIssues(content, filePath),
    };

    this.files.push(fileInfo);

    if (fileInfo.isComponent) this.components.push(fileInfo);
    if (fileInfo.isHook) this.hooks.push(fileInfo);
    if (fileInfo.isService) this.services.push(fileInfo);

    // Build import map for dependency tracking
    this.importMap.set(fileInfo.path, fileInfo.imports);
  }

  extractImports(content) {
    const imports = [];
    const patterns = [
      /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g,
      /import\s+['"]([^'"]+)['"]/g,
      /require\(['"]([^'"]+)['"]\)/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        imports.push({
          module: match[1],
          isRelative: match[1].startsWith('.'),
          isExternal: !match[1].startsWith('.') && !match[1].startsWith('/'),
          line: content.substring(0, match.index).split('\n').length,
        });
      }
    });

    return imports;
  }

  extractExports(content) {
    const exports = [];
    const patterns = [
      /export\s+default\s+(function\s+)?(\w+)/g,
      /export\s+const\s+(\w+)/g,
      /export\s+function\s+(\w+)/g,
      /export\s+class\s+(\w+)/g,
      /export\s*{\s*([^}]+)\s*}/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const exportName = match[2] || match[1];
        if (exportName) {
          exports.push({
            name: exportName,
            type: match[0].includes('default') ? 'default' : 'named',
            line: content.substring(0, match.index).split('\n').length,
          });
        }
      }
    });

    return exports;
  }

  extractDependencies(content) {
    const deps = {
      react: [],
      stateManagement: [],
      external: [],
      internal: [],
    };

    // React dependencies
    if (content.includes('useState')) deps.react.push('useState');
    if (content.includes('useEffect')) deps.react.push('useEffect');
    if (content.includes('useContext')) deps.react.push('useContext');
    if (content.includes('useReducer')) deps.react.push('useReducer');
    if (content.includes('useMemo')) deps.react.push('useMemo');
    if (content.includes('useCallback')) deps.react.push('useCallback');

    // State management
    if (content.includes('useDispatch') || content.includes('useSelector')) {
      deps.stateManagement.push('Redux');
    }
    if (content.includes('zustand')) deps.stateManagement.push('Zustand');
    if (content.includes('recoil')) deps.stateManagement.push('Recoil');

    return deps;
  }

  analyzeResponsibilities(content) {
    const responsibilities = [];

    if (content.includes('useState') || content.includes('useReducer')) {
      responsibilities.push('State Management');
    }
    if (content.includes('useEffect')) {
      responsibilities.push('Side Effects');
    }
    if (content.includes('fetch') || content.includes('axios')) {
      responsibilities.push('Data Fetching');
    }
    if (content.includes('onClick') || content.includes('onSubmit')) {
      responsibilities.push('Event Handling');
    }
    if (content.includes('return (') && content.includes('</')) {
      responsibilities.push('UI Rendering');
    }
    if (content.includes('validate')) {
      responsibilities.push('Validation');
    }
    if (content.includes('localStorage') || content.includes('sessionStorage')) {
      responsibilities.push('Data Persistence');
    }

    return responsibilities;
  }

  calculateComplexity(content) {
    const patterns = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g,
      /\?.*:/g,
      /useEffect/g,
      /useState/g,
    ];

    let complexity = 1;
    patterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) complexity += matches.length;
    });

    return complexity;
  }

  isReactComponent(fileName, content) {
    return (
      (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) &&
      (content.includes('export default') || content.includes('export const')) &&
      (content.includes('return (') || content.includes('jsx') || content.includes('</'))
    );
  }

  isHook(fileName, content) {
    return (
      fileName.startsWith('use') ||
      content.includes('export const use') ||
      content.includes('export default use')
    );
  }

  isService(fileName, content) {
    return (
      fileName.toLowerCase().includes('service') ||
      fileName.toLowerCase().includes('api') ||
      fileName.toLowerCase().includes('client')
    );
  }

  hasCorrespondingTests(filePath) {
    const dir = path.dirname(filePath);
    const baseName = path.basename(filePath, path.extname(filePath));

    const testPaths = [
      path.join(dir, `${baseName}.test.js`),
      path.join(dir, `${baseName}.test.tsx`),
      path.join(dir, `${baseName}.spec.js`),
      path.join(dir, `${baseName}.spec.tsx`),
      path.join(dir, '__tests__', `${baseName}.test.js`),
    ];

    return testPaths.some((testPath) => fs.existsSync(testPath));
  }

  identifyCouplingIssues(content, filePath) {
    const issues = [];

    // Deep relative imports
    const deepImports = content.match(/import.*?['"]\.\.\/\.\.\/.*?['"]/g);
    if (deepImports && deepImports.length > 0) {
      issues.push({
        type: 'deep-relative-imports',
        count: deepImports.length,
        examples: deepImports.slice(0, 3),
      });
    }

    // Too many imports
    const importCount = (content.match(/import.*?from/g) || []).length;
    if (importCount > 15) {
      issues.push({
        type: 'too-many-imports',
        count: importCount,
      });
    }

    return issues;
  }

  buildDependencyGraph() {
    console.log('🔗 Building dependency graph...');

    this.files.forEach((file) => {
      const dependencies = [];

      file.imports.forEach((imp) => {
        if (imp.isRelative) {
          // Resolve relative path
          const resolvedPath = this.resolveRelativePath(file.path, imp.module);
          dependencies.push({
            path: resolvedPath,
            type: 'relative',
            module: imp.module,
          });
        }
      });

      this.dependencyGraph.set(file.path, dependencies);
    });
  }

  resolveRelativePath(fromPath, relativePath) {
    const fromDir = path.dirname(fromPath);
    const resolvedPath = path.normalize(path.join(fromDir, relativePath));

    // Try different extensions
    const extensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx'];
    for (const ext of extensions) {
      const fullPath = resolvedPath + ext;
      if (this.files.some((f) => f.path === fullPath)) {
        return fullPath;
      }
    }

    return resolvedPath;
  }

  createFolderRestructurePlan() {
    console.log('📁 Creating folder restructure plan...');

    // Proposed new structure based on modern React patterns
    const proposedStructure = {
      'src/components/ui': {
        // Reusable UI components
        description: 'Basic, reusable UI components (buttons, inputs, etc.)',
        candidates: this.components.filter(
          (c) =>
            c.responsibilities.length <= 2 &&
            c.lines < 100 &&
            !c.name.toLowerCase().includes('page')
        ),
      },
      'src/components/feature': {
        // Feature-specific components
        description: 'Components tied to specific business features',
        candidates: this.components.filter(
          (c) =>
            c.responsibilities.includes('State Management') ||
            c.responsibilities.includes('Data Fetching')
        ),
      },
      'src/components/layout': {
        // Layout components
        description: 'App layout and navigation components',
        candidates: this.components.filter(
          (c) =>
            c.name.toLowerCase().includes('layout') ||
            c.name.toLowerCase().includes('nav') ||
            c.name.toLowerCase().includes('header') ||
            c.name.toLowerCase().includes('sidebar')
        ),
      },
      'src/pages': {
        // Page components
        description: 'Top-level page components',
        candidates: this.components.filter(
          (c) => c.name.toLowerCase().includes('page') || c.path.toLowerCase().includes('page')
        ),
      },
      'src/hooks': {
        // Custom hooks
        description: 'Reusable custom hooks',
        candidates: this.hooks,
      },
      'src/services': {
        // API and external services
        description: 'API clients and external service integrations',
        candidates: this.services,
      },
      'src/utils': {
        // Utility functions
        description: 'Pure utility functions and helpers',
        candidates: this.files.filter(
          (f) =>
            f.name.toLowerCase().includes('util') ||
            f.name.toLowerCase().includes('helper') ||
            (!f.isComponent && !f.isHook && !f.isService && f.responsibilities.length === 0)
        ),
      },
      'src/types': {
        // TypeScript types
        description: 'Shared TypeScript type definitions',
        candidates: this.files.filter((f) => f.name.includes('types') || f.name.endsWith('.d.ts')),
      },
    };

    this.improvementPlan.folderRestructure = proposedStructure;
  }

  createComponentSplitPlan() {
    console.log('✂️  Creating component split plan...');

    const largeComponents = this.components.filter((c) => c.lines > 200 || c.complexity > 20);

    largeComponents.forEach((component) => {
      const splitPlan = {
        originalFile: component.path,
        reason: component.lines > 300 ? 'Too large' : 'Too complex',
        lines: component.lines,
        complexity: component.complexity,
        responsibilities: component.responsibilities,
        proposedSplits: [],
        dependencies: this.dependencyGraph.get(component.path) || [],
        affectedFiles: this.findFilesImporting(component.path),
      };

      // Suggest splits based on responsibilities
      if (
        component.responsibilities.includes('State Management') &&
        component.responsibilities.includes('UI Rendering')
      ) {
        splitPlan.proposedSplits.push({
          name: `${component.name.replace('.tsx', '').replace('.jsx', '')}Container`,
          purpose: 'Handle state management and business logic',
          extractedResponsibilities: ['State Management', 'Data Fetching', 'Side Effects'],
          newPath: `src/components/feature/${component.name
            .replace('.tsx', '')
            .replace('.jsx', '')}Container.tsx`,
        });

        splitPlan.proposedSplits.push({
          name: `${component.name.replace('.tsx', '').replace('.jsx', '')}View`,
          purpose: 'Handle pure UI rendering',
          extractedResponsibilities: ['UI Rendering'],
          newPath: `src/components/ui/${component.name
            .replace('.tsx', '')
            .replace('.jsx', '')}View.tsx`,
        });
      }

      // Extract custom hook if component has complex state logic
      if (component.responsibilities.includes('State Management') && component.complexity > 15) {
        splitPlan.proposedSplits.push({
          name: `use${component.name.replace('.tsx', '').replace('.jsx', '')}`,
          purpose: 'Extract state management logic',
          extractedResponsibilities: ['State Management', 'Side Effects'],
          newPath: `src/hooks/use${component.name.replace('.tsx', '').replace('.jsx', '')}.ts`,
        });
      }

      this.improvementPlan.componentSplits.push(splitPlan);
    });
  }

  createHookExtractionPlan() {
    console.log('🪝 Creating hook extraction plan...');

    // Find components that could benefit from custom hooks
    this.components.forEach((component) => {
      if (
        component.responsibilities.includes('State Management') &&
        component.responsibilities.includes('Data Fetching') &&
        component.complexity > 10
      ) {
        this.improvementPlan.hookExtractions.push({
          sourceComponent: component.path,
          proposedHookName: `use${component.name.replace('.tsx', '').replace('.jsx', '')}Data`,
          extractedLogic: ['State Management', 'Data Fetching', 'Side Effects'],
          benefits: ['Reusability', 'Testability', 'Separation of Concerns'],
          newPath: `src/hooks/use${component.name.replace('.tsx', '').replace('.jsx', '')}Data.ts`,
          affectedFiles: this.findFilesImporting(component.path),
        });
      }
    });
  }

  createServiceExtractionPlan() {
    console.log('🔧 Creating service extraction plan...');

    // Find API calls scattered in components
    this.components.forEach((component) => {
      if (component.responsibilities.includes('Data Fetching')) {
        const apiCalls = this.extractApiCalls(component.content);

        if (apiCalls.length > 0) {
          this.improvementPlan.serviceExtractions.push({
            sourceComponent: component.path,
            apiCalls,
            proposedServiceName: `${component.name.replace('.tsx', '').replace('.jsx', '')}Service`,
            newPath: `src/services/${component.name
              .replace('.tsx', '')
              .replace('.jsx', '')}Service.ts`,
            benefits: ['Centralized API logic', 'Better testing', 'Reusability'],
          });
        }
      }
    });
  }

  extractApiCalls(content) {
    const apiCalls = [];
    const patterns = [
      /fetch\(['"`]([^'"`]+)['"`]/g,
      /axios\.(get|post|put|delete|patch)\(['"`]([^'"`]+)['"`]/g,
      /api\.\w+\(['"`]([^'"`]+)['"`]/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        apiCalls.push({
          url: match[1] || match[2],
          method: match[1] || 'GET',
          line: content.substring(0, match.index).split('\n').length,
        });
      }
    });

    return apiCalls;
  }

  createTestingPlan() {
    console.log('🧪 Creating testing plan...');

    const untestedFiles = this.files.filter((f) => !f.hasTests && (f.isComponent || f.isHook));

    // Prioritize testing based on complexity and importance
    const testingPriorities = {
      critical: untestedFiles.filter((f) => f.complexity > 20 || f.responsibilities.length > 3),
      high: untestedFiles.filter((f) => f.complexity > 10 && f.complexity <= 20),
      medium: untestedFiles.filter((f) => f.complexity <= 10),
    };

    this.improvementPlan.testingPlan = {
      summary: {
        totalUntested: untestedFiles.length,
        critical: testingPriorities.critical.length,
        high: testingPriorities.high.length,
        medium: testingPriorities.medium.length,
      },
      priorities: testingPriorities,
      recommendedTestTypes: this.recommendTestTypes(),
    };
  }

  recommendTestTypes() {
    return {
      'Unit Tests': 'For individual components and hooks',
      'Integration Tests': 'For component interactions and data flow',
      'E2E Tests': 'For critical user journeys',
      'Performance Tests': 'For components with high complexity',
    };
  }

  createMigrationPlan() {
    console.log('🚀 Creating migration plan...');

    const steps = [];

    // Step 1: Create new folder structure
    steps.push({
      phase: 1,
      title: 'Create New Folder Structure',
      description: 'Set up the new architectural folders',
      actions: Object.keys(this.improvementPlan.folderRestructure).map(
        (folder) => `mkdir -p ${folder}`
      ),
      risk: 'Low',
      rollback: 'Remove created folders',
      estimatedTime: '30 minutes',
    });

    // Step 2: Extract and move utility functions first (lowest risk)
    steps.push({
      phase: 2,
      title: 'Move Utility Functions',
      description: 'Move pure utility functions to new structure',
      actions: this.generateMoveActions(
        this.improvementPlan.folderRestructure['src/utils']?.candidates || []
      ),
      dependencies: this.calculateMoveDependencies('src/utils'),
      risk: 'Low',
      rollback: 'Move files back to original locations',
      estimatedTime: '2-4 hours',
    });

    // Step 3: Extract custom hooks
    steps.push({
      phase: 3,
      title: 'Extract and Move Custom Hooks',
      description: 'Create custom hooks and move existing ones',
      actions: this.generateHookExtractionActions(),
      dependencies: this.calculateHookDependencies(),
      risk: 'Medium',
      rollback: 'Revert hook extractions and move files back',
      estimatedTime: '1-2 days',
    });

    // Step 4: Split large components
    steps.push({
      phase: 4,
      title: 'Split Large Components',
      description: 'Break down overly complex components',
      actions: this.generateComponentSplitActions(),
      dependencies: this.calculateComponentSplitDependencies(),
      risk: 'High',
      rollback: 'Merge split components back together',
      estimatedTime: '3-5 days',
    });

    // Step 5: Move components to new structure
    steps.push({
      phase: 5,
      title: 'Reorganize Components',
      description: 'Move components to their appropriate folders',
      actions: this.generateComponentMoveActions(),
      dependencies: this.calculateComponentMoveDependencies(),
      risk: 'Medium',
      rollback: 'Move components back to original structure',
      estimatedTime: '1-2 days',
    });

    // Step 6: Add tests
    steps.push({
      phase: 6,
      title: 'Add Missing Tests',
      description: 'Create tests for critical components',
      actions: this.generateTestCreationActions(),
      dependencies: [],
      risk: 'Low',
      rollback: 'Remove created test files',
      estimatedTime: '2-3 days',
    });

    this.improvementPlan.migrationSteps = steps;
  }

  generateMoveActions(candidates) {
    return candidates.map((file) => ({
      action: 'move',
      from: file.path,
      to: `src/utils/${file.name}`,
      updateImports: this.findFilesImporting(file.path),
    }));
  }

  generateHookExtractionActions() {
    return this.improvementPlan.hookExtractions.map((extraction) => ({
      action: 'extract-hook',
      sourceFile: extraction.sourceComponent,
      newHookFile: extraction.newPath,
      hookName: extraction.proposedHookName,
      extractedLogic: extraction.extractedLogic,
      updateFiles: extraction.affectedFiles,
    }));
  }

  generateComponentSplitActions() {
    return this.improvementPlan.componentSplits.map((split) => ({
      action: 'split-component',
      originalFile: split.originalFile,
      newFiles: split.proposedSplits.map((s) => s.newPath),
      updateImports: split.affectedFiles,
    }));
  }

  generateComponentMoveActions() {
    const actions = [];

    Object.entries(this.improvementPlan.folderRestructure).forEach(([folder, config]) => {
      if (config.candidates) {
        config.candidates.forEach((component) => {
          if (component.isComponent) {
            actions.push({
              action: 'move',
              from: component.path,
              to: `${folder}/${component.name}`,
              updateImports: this.findFilesImporting(component.path),
            });
          }
        });
      }
    });

    return actions;
  }

  generateTestCreationActions() {
    return this.improvementPlan.testingPlan.priorities.critical.map((file) => ({
      action: 'create-test',
      targetFile: file.path,
      testFile: file.path.replace(/\.(tsx?|jsx?)$/, '.test.$1'),
      testType: file.isComponent ? 'component' : 'hook',
    }));
  }

  calculateMoveDependencies(targetFolder) {
    // Calculate which imports need updating when files move
    const dependencies = [];
    // Implementation would track all import statements that need updating
    return dependencies;
  }

  calculateHookDependencies() {
    return this.improvementPlan.hookExtractions.map((extraction) => ({
      hookFile: extraction.newPath,
      dependentFiles: extraction.affectedFiles,
      importsToUpdate: extraction.affectedFiles.map((file) => ({
        file,
        oldImport: extraction.sourceComponent,
        newImport: extraction.newPath,
      })),
    }));
  }

  calculateComponentSplitDependencies() {
    return this.improvementPlan.componentSplits.map((split) => ({
      originalFile: split.originalFile,
      newFiles: split.proposedSplits.map((s) => s.newPath),
      affectedFiles: split.affectedFiles,
      importsToUpdate: split.affectedFiles.map((file) => ({
        file,
        changes: split.proposedSplits.map((s) => ({
          newImport: s.newPath,
          exportedName: s.name,
        })),
      })),
    }));
  }

  calculateComponentMoveDependencies() {
    const dependencies = [];

    this.files.forEach((file) => {
      const deps = this.dependencyGraph.get(file.path) || [];
      deps.forEach((dep) => {
        // Find if the dependency is being moved
        const targetFile = this.findTargetLocation(dep.path);
        if (targetFile && targetFile !== dep.path) {
          dependencies.push({
            file: file.path,
            oldImport: dep.module,
            newImport: this.calculateNewImportPath(file.path, targetFile),
          });
        }
      });
    });

    return dependencies;
  }

  findTargetLocation(filePath) {
    // Find where a file will be moved to in the new structure
    const file = this.files.find((f) => f.path === filePath);
    if (!file) return null;

    // Check each folder's candidates
    for (const [folder, config] of Object.entries(this.improvementPlan.folderRestructure)) {
      if (config.candidates && config.candidates.some((c) => c.path === filePath)) {
        return `${folder}/${file.name}`;
      }
    }

    return null;
  }

  calculateNewImportPath(fromPath, toPath) {
    const relativePath = path.relative(path.dirname(fromPath), toPath);
    return relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  }

  findFilesImporting(targetPath) {
    const importingFiles = [];

    this.files.forEach((file) => {
      file.imports.forEach((imp) => {
        if (imp.isRelative) {
          const resolvedPath = this.resolveRelativePath(file.path, imp.module);
          if (resolvedPath === targetPath) {
            importingFiles.push({
              file: file.path,
              importStatement: imp.module,
              line: imp.line,
            });
          }
        }
      });
    });

    return importingFiles;
  }

  generateImprovementReport() {
    console.log('\n🎯 ARCHITECTURAL IMPROVEMENT REPORT');
    console.log('='.repeat(80));

    // Executive Summary
    console.log('\n📋 EXECUTIVE SUMMARY');
    console.log(`   • ${this.improvementPlan.componentSplits.length} components need splitting`);
    console.log(
      `   • ${this.improvementPlan.hookExtractions.length} custom hooks can be extracted`
    );
    console.log(
      `   • ${this.improvementPlan.serviceExtractions.length} service layers can be created`
    );
    console.log(`   • ${this.improvementPlan.testingPlan.summary.totalUntested} files need tests`);
    console.log(`   • ${this.improvementPlan.migrationSteps.length} migration phases planned`);

    // Folder Restructure Plan
    console.log('\n📁 PROPOSED FOLDER STRUCTURE');
    Object.entries(this.improvementPlan.folderRestructure).forEach(([folder, config]) => {
      console.log(`\n${folder}/`);
      console.log(`   Purpose: ${config.description}`);
      console.log(`   Files to move: ${config.candidates ? config.candidates.length : 0}`);
      if (config.candidates && config.candidates.length > 0) {
        console.log(
          `   Examples: ${config.candidates
            .slice(0, 3)
            .map((c) => c.name)
            .join(', ')}`
        );
      }
    });

    // Component Split Analysis
    console.log('\n✂️  COMPONENT SPLIT RECOMMENDATIONS');
    this.improvementPlan.componentSplits.slice(0, 5).forEach((split, i) => {
      console.log(`\n${i + 1}. ${split.originalFile}`);
      console.log(
        `   Reason: ${split.reason} (${split.lines} lines, complexity ${split.complexity})`
      );
      console.log(`   Current responsibilities: ${split.responsibilities.join(', ')}`);
      console.log(`   Proposed splits:`);
      split.proposedSplits.forEach((proposedSplit) => {
        console.log(`     → ${proposedSplit.name}: ${proposedSplit.purpose}`);
        console.log(`       New path: ${proposedSplit.newPath}`);
      });
      console.log(`   Files that import this: ${split.affectedFiles.length}`);
    });

    // Hook Extraction Plan
    console.log('\n🪝 CUSTOM HOOK EXTRACTION OPPORTUNITIES');
    this.improvementPlan.hookExtractions.slice(0, 5).forEach((extraction, i) => {
      console.log(`\n${i + 1}. Extract ${extraction.proposedHookName}`);
      console.log(`   From: ${extraction.sourceComponent}`);
      console.log(`   New location: ${extraction.newPath}`);
      console.log(`   Logic to extract: ${extraction.extractedLogic.join(', ')}`);
      console.log(`   Benefits: ${extraction.benefits.join(', ')}`);
    });

    // Testing Plan
    console.log('\n🧪 TESTING STRATEGY');
    console.log(`Total untested files: ${this.improvementPlan.testingPlan.summary.totalUntested}`);
    console.log(`Critical priority: ${this.improvementPlan.testingPlan.summary.critical} files`);
    console.log(`High priority: ${this.improvementPlan.testingPlan.summary.high} files`);
    console.log(`Medium priority: ${this.improvementPlan.testingPlan.summary.medium} files`);

    if (this.improvementPlan.testingPlan.priorities.critical.length > 0) {
      console.log('\nCritical files needing tests:');
      this.improvementPlan.testingPlan.priorities.critical.slice(0, 5).forEach((file) => {
        console.log(`   ${file.path} (complexity: ${file.complexity})`);
      });
    }

    // Migration Plan
    console.log('\n🚀 MIGRATION ROADMAP');
    this.improvementPlan.migrationSteps.forEach((step) => {
      console.log(`\nPhase ${step.phase}: ${step.title}`);
      console.log(`   Description: ${step.description}`);
      console.log(`   Risk Level: ${step.risk}`);
      console.log(`   Estimated Time: ${step.estimatedTime}`);
      console.log(`   Actions: ${step.actions.length} items`);
      if (step.dependencies && step.dependencies.length > 0) {
        console.log(`   Dependencies: ${step.dependencies.length} import updates needed`);
      }
    });

    // Breaking Changes Warning
    console.log('\n⚠️  BREAKING CHANGES ANALYSIS');
    const totalImportUpdates = this.calculateTotalImportUpdates();
    console.log(`   • ${totalImportUpdates} import statements will need updating`);
    console.log(`   • Recommended approach: Use automated refactoring tools`);
    console.log(`   • Suggested tools: jscodeshift, ts-morph, or IDE refactoring`);

    // Implementation Scripts
    console.log('\n🛠️  IMPLEMENTATION HELPERS');
    console.log('\nTo start the migration, run these commands:');
    console.log('```bash');
    console.log('# Phase 1: Create folder structure');
    Object.keys(this.improvementPlan.folderRestructure).forEach((folder) => {
      console.log(`mkdir -p ${folder}`);
    });
    console.log('\n# Phase 2: Move utility files (safest first)');
    const utilMoves = this.improvementPlan.folderRestructure['src/utils']?.candidates || [];
    utilMoves.slice(0, 3).forEach((file) => {
      console.log(`git mv ${file.path} src/utils/${file.name}`);
    });
    console.log('```');

    // Next Steps
    console.log('\n🎯 IMMEDIATE NEXT STEPS');
    console.log('   1. Review and approve the proposed folder structure');
    console.log('   2. Start with Phase 1 (low-risk folder creation)');
    console.log('   3. Create a feature branch for the migration');
    console.log('   4. Begin with utility file moves (lowest risk)');
    console.log('   5. Set up automated testing for each phase');
    console.log('   6. Consider using codemods for import updates');

    // Rollback Strategy
    console.log('\n🔄 ROLLBACK STRATEGY');
    console.log('   • Each phase is designed to be independently reversible');
    console.log('   • Keep detailed logs of all file moves and changes');
    console.log('   • Use git branches for each migration phase');
    console.log('   • Test thoroughly before proceeding to next phase');
  }

  calculateTotalImportUpdates() {
    let total = 0;
    this.improvementPlan.migrationSteps.forEach((step) => {
      if (step.dependencies) {
        total += step.dependencies.length;
      }
      if (step.actions) {
        total += step.actions.filter((action) => action.updateImports).length;
      }
    });
    return total;
  }
}

// CLI usage
const targetDir = process.argv[2] || '.';
const planner = new ArchitecturalImprovementPlanner(targetDir);
planner.analyze();
