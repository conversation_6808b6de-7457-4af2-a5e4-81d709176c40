#!/usr/bin/env node

/**
 * Advanced Import Update Automation Script
 * Handles complex import transformations and dependency updates
 * Can be used standalone or as part of migration phases
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ImportUpdater {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    this.mappings = new Map();
    this.processedFiles = new Set();
    this.errors = [];
    this.stats = {
      filesProcessed: 0,
      importsUpdated: 0,
      errorsFound: 0
    };
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[Import Updater${this.dryRun ? ' - DRY RUN' : ''}]`;
    
    if (level === 'verbose' && !this.verbose) return;
    
    console.log(`${prefix} ${message}`);
  }

  /**
   * Add a mapping for import path transformation
   */
  addMapping(oldPath, newPath, options = {}) {
    this.mappings.set(oldPath, {
      newPath,
      preserveExtension: options.preserveExtension || false,
      isDirectory: options.isDirectory || false,
      transformFunction: options.transformFunction || null
    });
  }

  /**
   * Load mappings from a JSON file
   */
  loadMappingsFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const mappings = JSON.parse(content);
      
      for (const [oldPath, config] of Object.entries(mappings)) {
        if (typeof config === 'string') {
          this.addMapping(oldPath, config);
        } else {
          this.addMapping(oldPath, config.newPath, config.options || {});
        }
      }
      
      this.log(`Loaded ${Object.keys(mappings).length} mappings from ${filePath}`);
    } catch (error) {
      this.log(`Failed to load mappings from ${filePath}: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Generate mappings from phase outputs
   */
  generateMappingsFromPhases() {
    const phaseMappings = [
      // Phase 2: Utility files
      this.generateUtilityMappings(),
      // Phase 3: Hooks
      this.generateHookMappings(),
      // Phase 4: Split components
      this.generateComponentSplitMappings(),
      // Phase 5: Component reorganization
      this.generateReorganizationMappings()
    ];

    for (const mappings of phaseMappings) {
      for (const [oldPath, newPath] of mappings) {
        this.addMapping(oldPath, newPath);
      }
    }

    this.log(`Generated ${this.mappings.size} mappings from phase outputs`);
  }

  generateUtilityMappings() {
    return new Map([
      ['.eslintrc.js', 'src/utils/config/.eslintrc.js'],
      ['__mocks__/fileMock.js', 'src/utils/mocks/fileMock.js'],
      ['__mocks__/styleMock.js', 'src/utils/mocks/styleMock.js'],
      ['babel.config.js', 'src/utils/config/babel.config.js'],
      ['codebase-analyzer.js', 'src/utils/analysis/codebase-analyzer.js'],
      ['architectural-improvement-planner.js', 'src/utils/analysis/architectural-improvement-planner.js']
    ]);
  }

  generateHookMappings() {
    return new Map([
      ['packages/dashboard/src/features/daily-guide/components/DailyGuideContext.tsx', 'src/hooks/useDailyGuide.ts'],
      ['useTradingDashboardData', 'src/hooks/useTradingDashboardData.ts']
    ]);
  }

  generateComponentSplitMappings() {
    return new Map([
      ['packages/dashboard/src/components/molecules/ProfitLossCell.tsx', 'src/components/ui/ProfitLossCellCore.tsx'],
      ['packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx', 'src/components/feature/TradingPlanContainer.tsx']
    ]);
  }

  generateReorganizationMappings() {
    return new Map([
      ['packages/dashboard/src/App.tsx', 'src/components/ui/App.tsx'],
      ['packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx', 'src/components/feature/TradingDashboard.tsx'],
      ['packages/dashboard/src/features/daily-guide/DailyGuide.tsx', 'src/pages/DailyGuide.tsx'],
      ['packages/dashboard/src/features/settings/Settings.tsx', 'src/pages/Settings.tsx']
    ]);
  }

  /**
   * Find all source files that might contain imports
   */
  findSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx', '.vue', '.svelte'];
    const ignorePaths = [
      'node_modules', 
      '.git', 
      'dist', 
      'build', 
      'coverage',
      '.next',
      '.nuxt',
      'migration-scripts',
      'migration-backups'
    ];
    
    const findFiles = (dir) => {
      const files = [];
      
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          
          // Skip symlinks and special files
          let stat;
          try {
            stat = fs.statSync(fullPath);
          } catch (error) {
            continue;
          }
          
          if (stat.isDirectory() && !ignorePaths.includes(item) && !item.startsWith('.')) {
            files.push(...findFiles(fullPath));
          } else if (stat.isFile() && extensions.includes(path.extname(item))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        this.log(`Cannot read directory ${dir}: ${error.message}`, 'verbose');
      }
      
      return files;
    };
    
    return findFiles(process.cwd());
  }

  /**
   * Extract all import statements from file content
   */
  extractImports(content) {
    const imports = [];
    
    // Various import patterns
    const patterns = [
      // ES6 imports: import X from 'path', import { X } from 'path', import * as X from 'path'
      /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g,
      // CommonJS require: require('path'), const X = require('path')
      /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      // Dynamic imports: import('path')
      /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      // TypeScript triple-slash directives: /// <reference path="..." />
      /\/\/\/\s*<reference\s+path\s*=\s*['"`]([^'"`]+)['"`]/g,
      // CSS/SCSS imports in JS/TS files
      /import\s+['"`]([^'"`]+\.s?css)['"`]/g
    ];

    let lineNumber = 0;
    const lines = content.split('\n');
    
    for (const line of lines) {
      lineNumber++;
      
      for (const pattern of patterns) {
        pattern.lastIndex = 0; // Reset regex state
        let match;
        
        while ((match = pattern.exec(line)) !== null) {
          imports.push({
            fullMatch: match[0],
            path: match[1],
            lineNumber,
            line: line.trim(),
            startIndex: match.index,
            endIndex: match.index + match[0].length
          });
        }
      }
    }
    
    return imports;
  }

  /**
   * Transform import path using mappings
   */
  transformImportPath(importPath, currentFilePath) {
    // Try exact match first
    if (this.mappings.has(importPath)) {
      const config = this.mappings.get(importPath);
      const newPath = this.calculateRelativePath(currentFilePath, config.newPath);
      return config.transformFunction ? config.transformFunction(newPath) : newPath;
    }

    // Try partial matches for directory mappings
    for (const [oldPath, config] of this.mappings) {
      if (config.isDirectory && importPath.startsWith(oldPath)) {
        const relativePart = importPath.substring(oldPath.length);
        const newFullPath = config.newPath + relativePart;
        const newPath = this.calculateRelativePath(currentFilePath, newFullPath);
        return config.transformFunction ? config.transformFunction(newPath) : newPath;
      }
    }

    // Try fuzzy matching for moved files
    for (const [oldPath, config] of this.mappings) {
      const oldBaseName = path.basename(oldPath, path.extname(oldPath));
      const importBaseName = path.basename(importPath, path.extname(importPath));
      
      if (oldBaseName === importBaseName) {
        const newPath = this.calculateRelativePath(currentFilePath, config.newPath);
        return config.transformFunction ? config.transformFunction(newPath) : newPath;
      }
    }

    return null; // No transformation found
  }

  /**
   * Calculate relative import path between two files
   */
  calculateRelativePath(fromFile, toFile) {
    const fromDir = path.dirname(fromFile);
    const relativePath = path.relative(fromDir, toFile);
    
    // Remove file extension for imports (except for assets)
    const ext = path.extname(relativePath);
    const isAsset = ['.css', '.scss', '.less', '.svg', '.png', '.jpg', '.gif'].includes(ext);
    
    let finalPath = isAsset ? relativePath : relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Ensure relative paths start with './' or '../'
    if (!finalPath.startsWith('.')) {
      finalPath = './' + finalPath;
    }
    
    // Normalize path separators
    return finalPath.replace(/\\/g, '/');
  }

  /**
   * Update imports in a single file
   */
  updateImportsInFile(filePath) {
    if (this.processedFiles.has(filePath)) {
      return false; // Already processed
    }

    this.processedFiles.add(filePath);

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = this.extractImports(content);
      
      if (imports.length === 0) {
        return false; // No imports to process
      }

      let updatedContent = content;
      let updatesCount = 0;
      
      // Process imports in reverse order to maintain correct positions
      const sortedImports = imports.sort((a, b) => b.startIndex - a.startIndex);
      
      for (const importInfo of sortedImports) {
        const newPath = this.transformImportPath(importInfo.path, filePath);
        
        if (newPath && newPath !== importInfo.path) {
          // Replace the import path in the content
          const beforeImport = updatedContent.substring(0, importInfo.startIndex);
          const afterImport = updatedContent.substring(importInfo.endIndex);
          const newImportStatement = importInfo.fullMatch.replace(importInfo.path, newPath);
          
          updatedContent = beforeImport + newImportStatement + afterImport;
          updatesCount++;
          
          this.log(`Updated import in ${path.relative(process.cwd(), filePath)}: ${importInfo.path} → ${newPath}`, 'verbose');
        }
      }

      if (updatesCount > 0) {
        if (!this.dryRun) {
          fs.writeFileSync(filePath, updatedContent);
        }
        
        this.stats.importsUpdated += updatesCount;
        this.log(`Updated ${updatesCount} imports in ${path.relative(process.cwd(), filePath)}`);
        return true;
      }

      return false;
    } catch (error) {
      const errorMsg = `Failed to update imports in ${filePath}: ${error.message}`;
      this.log(errorMsg, 'error');
      this.errors.push({ file: filePath, error: errorMsg });
      this.stats.errorsFound++;
      return false;
    }
  }

  /**
   * Process all source files
   */
  async processAllFiles() {
    this.log('🔍 Finding source files...');
    const sourceFiles = this.findSourceFiles();
    this.log(`Found ${sourceFiles.length} source files to process`);

    this.log('🔄 Processing import statements...');
    let processedCount = 0;
    let updatedCount = 0;

    for (const filePath of sourceFiles) {
      this.stats.filesProcessed++;
      
      if (this.updateImportsInFile(filePath)) {
        updatedCount++;
      }
      
      processedCount++;
      
      // Progress reporting
      if (processedCount % 100 === 0) {
        this.log(`Progress: ${processedCount}/${sourceFiles.length} files processed`);
      }
    }

    this.log(`✅ Processed ${processedCount} files, updated ${updatedCount} files`);
  }

  /**
   * Validate that all new import paths exist
   */
  validateImports() {
    this.log('🔍 Validating updated import paths...');
    
    let validationErrors = 0;
    
    for (const [oldPath, config] of this.mappings) {
      const newPathExists = fs.existsSync(path.join(process.cwd(), config.newPath));
      
      if (!newPathExists) {
        this.log(`⚠️  New path does not exist: ${config.newPath}`, 'error');
        validationErrors++;
      }
    }

    if (validationErrors > 0) {
      this.log(`❌ Found ${validationErrors} validation errors`);
    } else {
      this.log('✅ All import paths validated successfully');
    }

    return validationErrors === 0;
  }

  /**
   * Generate a comprehensive report
   */
  generateReport() {
    const reportPath = path.join(process.cwd(), 'import-update-report.md');
    
    const reportContent = `# Import Update Report

Generated: ${new Date().toISOString()}

## Summary

- **Files Processed**: ${this.stats.filesProcessed}
- **Imports Updated**: ${this.stats.importsUpdated}
- **Errors Found**: ${this.stats.errorsFound}
- **Mappings Applied**: ${this.mappings.size}

## Import Mappings

${Array.from(this.mappings.entries()).map(([oldPath, config]) => 
  `- \`${oldPath}\` → \`${config.newPath}\``
).join('\n')}

## Errors

${this.errors.length > 0 ? 
  this.errors.map(error => `- **${error.file}**: ${error.error}`).join('\n') : 
  'No errors encountered'
}

## Recommendations

1. Run tests to verify all imports are working correctly
2. Check for any dynamic imports that might not have been caught
3. Verify that build processes are still working
4. Update any IDE/editor configurations that might reference old paths
5. Consider updating any documentation that references the old file structure
`;

    if (!this.dryRun) {
      try {
        fs.writeFileSync(reportPath, reportContent);
        this.log(`✅ Generated report: ${reportPath}`);
      } catch (error) {
        this.log(`❌ Failed to generate report: ${error.message}`);
      }
    } else {
      this.log('📊 [DRY RUN] Would generate import update report');
    }
  }

  /**
   * Main execution method
   */
  async run(options = {}) {
    this.log(`🚀 Starting Import Update Process ${this.dryRun ? '(DRY RUN)' : ''}`);
    
    try {
      // Load or generate mappings
      if (options.mappingsFile) {
        this.loadMappingsFromFile(options.mappingsFile);
      } else {
        this.generateMappingsFromPhases();
      }

      if (this.mappings.size === 0) {
        this.log('⚠️  No mappings configured. Nothing to update.');
        return;
      }

      // Validate mappings before processing
      if (!this.dryRun) {
        this.validateImports();
      }

      // Process all files
      await this.processAllFiles();

      // Generate report
      this.generateReport();

      this.log(`✅ Import update completed successfully! ${this.dryRun ? '(DRY RUN)' : ''}`);
      
      if (this.errors.length > 0) {
        this.log(`⚠️  Completed with ${this.errors.length} errors. Check the report for details.`);
      }

    } catch (error) {
      this.log(`❌ Import update failed: ${error.message}`);
      throw error;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose'),
    mappingsFile: args.find(arg => arg.startsWith('--mappings='))?.split('=')[1]
  };
  
  const updater = new ImportUpdater(options);
  updater.run(options).catch(error => {
    console.error('❌ Import updater failed:', error.message);
    process.exit(1);
  });
}

module.exports = ImportUpdater;