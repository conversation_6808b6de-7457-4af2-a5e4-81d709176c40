#!/usr/bin/env node

/**
 * Phase 6: Add Missing Tests (Bonus Phase)
 * Generate comprehensive tests for critical components
 * Estimated time: 2-3 days
 */

const fs = require('fs');
const path = require('path');

class Phase6TestGenerator {
  constructor(dryRun = false) {
    this.dryRun = dryRun;
    this.criticalFiles = [
      {
        file: 'src/hooks/useTradingDashboardData.ts',
        type: 'hook',
        priority: 'critical',
        complexity: 'high'
      },
      {
        file: 'src/hooks/useDailyGuide.ts',
        type: 'hook',
        priority: 'critical',
        complexity: 'high'
      },
      {
        file: 'src/components/ui/ProfitLossCellCore.tsx',
        type: 'component',
        priority: 'critical',
        complexity: 'medium'
      },
      {
        file: 'src/components/feature/TradingPlanContainer.tsx',
        type: 'component',
        priority: 'critical',
        complexity: 'high'
      },
      {
        file: 'src/utils/calculations/ProfitLossCalculator.ts',
        type: 'utility',
        priority: 'critical',
        complexity: 'medium'
      }
    ];
    this.generatedTests = [];
  }

  log(message) {
    console.log(`[Phase 6] ${message}`);
  }

  generateHookTests(hookFile) {
    const hookName = path.basename(hookFile, '.ts');
    
    return `import { renderHook, act } from '@testing-library/react';
import { ${hookName} } from '../${hookName}';

describe('${hookName}', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => ${hookName}());
      
      expect(result.current.data).toBeDefined();
      expect(result.current.actions).toBeDefined();
      expect(result.current.data.isLoading).toBe(false);
      expect(result.current.data.error).toBeNull();
    });
  });

  describe('loading states', () => {
    it('should handle loading state correctly', async () => {
      const { result } = renderHook(() => ${hookName}());
      
      await act(async () => {
        await result.current.actions.loadTrades?.();
      });

      // Verify loading state was set and then cleared
      expect(result.current.data.isLoading).toBe(false);
    });

    it('should handle error states', async () => {
      // Mock API failure
      const mockError = new Error('API Error');
      jest.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => ${hookName}());
      
      // Simulate error condition
      await act(async () => {
        try {
          await result.current.actions.loadTrades?.();
        } catch (error) {
          // Expected to catch error
        }
      });

      // Should handle errors gracefully
      expect(result.current.data.isLoading).toBe(false);
    });
  });

  describe('data management', () => {
    it('should update data correctly', async () => {
      const { result } = renderHook(() => ${hookName}());
      
      const testData = { test: 'data' };
      
      await act(async () => {
        if (result.current.actions.updateFilters) {
          result.current.actions.updateFilters(testData);
        }
      });

      // Verify data was updated
      expect(result.current.data).toBeDefined();
    });

    it('should clear error when requested', async () => {
      const { result } = renderHook(() => ${hookName}());
      
      await act(async () => {
        if (result.current.actions.clearError) {
          result.current.actions.clearError();
        }
      });

      expect(result.current.data.error).toBeNull();
    });
  });

  describe('cleanup', () => {
    it('should clean up properly on unmount', () => {
      const { unmount } = renderHook(() => ${hookName}());
      
      expect(() => unmount()).not.toThrow();
    });
  });
});`;
  }

  generateComponentTests(componentFile) {
    const componentName = path.basename(componentFile, path.extname(componentFile));
    
    return `import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ${componentName} } from '../${componentName}';

// Mock dependencies if needed
jest.mock('../../hooks/useTradingPlan', () => ({
  useTradingPlan: () => ({
    plan: {
      symbol: 'AAPL',
      direction: 'long',
      entryPrice: 150,
      stopLoss: 145,
      takeProfit: 160,
      quantity: 100,
      reasoning: 'Test reasoning',
      keyLevels: [150, 155, 160],
      riskReward: 2
    },
    isValid: true,
    errors: {},
    actions: {
      updatePlan: jest.fn(),
      validatePlan: jest.fn(() => true),
      resetPlan: jest.fn(),
      calculateRisk: jest.fn(() => 500)
    }
  })
}));

describe('${componentName}', () => {
  const defaultProps = {
    // Add default props here based on component interface
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render without crashing', () => {
      render(<${componentName} {...defaultProps} />);
      expect(screen.getByRole('generic')).toBeInTheDocument();
    });

    it('should render with required props', () => {
      const props = {
        ...defaultProps,
        // Add specific props for testing
      };
      
      render(<${componentName} {...props} />);
      expect(screen.getByRole('generic')).toBeInTheDocument();
    });

    it('should handle empty/null props gracefully', () => {
      const props = {
        ...defaultProps,
        data: null
      };
      
      expect(() => render(<${componentName} {...props} />)).not.toThrow();
    });
  });

  describe('user interactions', () => {
    it('should handle click events', async () => {
      const onClickMock = jest.fn();
      const props = {
        ...defaultProps,
        onClick: onClickMock
      };
      
      render(<${componentName} {...props} />);
      
      const clickableElement = screen.getByRole('button', { name: /save/i });
      fireEvent.click(clickableElement);
      
      await waitFor(() => {
        expect(onClickMock).toHaveBeenCalledTimes(1);
      });
    });

    it('should handle form submissions', async () => {
      const onSubmitMock = jest.fn();
      const props = {
        ...defaultProps,
        onSubmit: onSubmitMock
      };
      
      render(<${componentName} {...props} />);
      
      const form = screen.getByRole('form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(onSubmitMock).toHaveBeenCalled();
      });
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<${componentName} {...defaultProps} />);
      
      // Check for ARIA labels, roles, etc.
      const element = screen.getByRole('generic');
      expect(element).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      render(<${componentName} {...defaultProps} />);
      
      const focusableElements = screen.getAllByRole('button');
      expect(focusableElements.length).toBeGreaterThan(0);
    });
  });

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      const props = {
        ...defaultProps,
        onError: jest.fn()
      };
      
      render(<${componentName} {...props} />);
      
      // Simulate error condition
      // Add specific error simulation based on component
    });

    it('should display error messages', () => {
      const props = {
        ...defaultProps,
        error: 'Test error message'
      };
      
      render(<${componentName} {...props} />);
      
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });
});`;
  }

  generateUtilityTests(utilityFile) {
    const utilityName = path.basename(utilityFile, '.ts');
    
    return `import {
  calculateProfitLoss,
  formatProfitLoss,
  calculatePortfolioProfitLoss
} from '../${utilityName}';

describe('${utilityName}', () => {
  describe('calculateProfitLoss', () => {
    it('should calculate profit for long positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 110,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (110 - 100) * 10
      expect(result.percentage).toBe(10); // 10% gain
      expect(result.isPositive).toBe(true);
    });

    it('should calculate loss for long positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 90,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (100 - 90) * 10
      expect(result.percentage).toBe(10); // 10% loss
      expect(result.isPositive).toBe(false);
    });

    it('should calculate profit for short positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 90,
        quantity: 10,
        direction: 'short' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (100 - 90) * 10
      expect(result.percentage).toBe(10); // 10% gain
      expect(result.isPositive).toBe(true);
    });

    it('should calculate loss for short positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 110,
        quantity: 10,
        direction: 'short' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (110 - 100) * 10
      expect(result.percentage).toBe(10); // 10% loss
      expect(result.isPositive).toBe(false);
    });

    it('should handle zero profit/loss', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 100,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true); // Zero is considered positive
    });

    it('should handle fractional shares', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 105,
        quantity: 1.5,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(7.5); // (105 - 100) * 1.5
      expect(result.percentage).toBe(5); // 5% gain
      expect(result.isPositive).toBe(true);
    });
  });

  describe('formatProfitLoss', () => {
    it('should format positive values correctly', () => {
      const result = formatProfitLoss(123.45, true);
      expect(result).toBe('+$123.45');
    });

    it('should format negative values correctly', () => {
      const result = formatProfitLoss(123.45, false);
      expect(result).toBe('-$123.45');
    });

    it('should handle zero values', () => {
      const result = formatProfitLoss(0, true);
      expect(result).toBe('+$0.00');
    });

    it('should round to 2 decimal places', () => {
      const result = formatProfitLoss(123.456789, true);
      expect(result).toBe('+$123.46');
    });
  });

  describe('calculatePortfolioProfitLoss', () => {
    it('should calculate total portfolio P&L correctly', () => {
      const trades = [
        {
          entryPrice: 100,
          currentPrice: 110,
          quantity: 10,
          direction: 'long' as const
        },
        {
          entryPrice: 200,
          currentPrice: 180,
          quantity: 5,
          direction: 'long' as const
        }
      ];
      
      const result = calculatePortfolioProfitLoss(trades);
      
      // Trade 1: +100, Trade 2: -100, Total: 0
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true);
    });

    it('should handle empty portfolio', () => {
      const result = calculatePortfolioProfitLoss([]);
      
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true);
    });

    it('should handle mixed long and short positions', () => {
      const trades = [
        {
          entryPrice: 100,
          currentPrice: 110,
          quantity: 10,
          direction: 'long' as const
        },
        {
          entryPrice: 200,
          currentPrice: 210,
          quantity: 5,
          direction: 'short' as const
        }
      ];
      
      const result = calculatePortfolioProfitLoss(trades);
      
      // Trade 1: +100, Trade 2: -50, Total: +50
      expect(result.absoluteValue).toBe(50);
      expect(result.isPositive).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle very large numbers', () => {
      const trade = {
        entryPrice: 1000000,
        currentPrice: 1100000,
        quantity: 1000,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100000000);
      expect(result.percentage).toBe(10);
      expect(result.isPositive).toBe(true);
    });

    it('should handle very small numbers', () => {
      const trade = {
        entryPrice: 0.001,
        currentPrice: 0.0011,
        quantity: 1000,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBeCloseTo(0.1);
      expect(result.percentage).toBeCloseTo(10);
      expect(result.isPositive).toBe(true);
    });
  });
});`;
  }

  createTestFile(testContent, testPath) {
    if (this.dryRun) {
      this.log(`📝 [DRY RUN] Would create test: ${testPath}`);
      return;
    }

    try {
      // Ensure test directory exists
      const testDir = path.dirname(testPath);
      fs.mkdirSync(testDir, { recursive: true });

      fs.writeFileSync(testPath, testContent);
      this.log(`✅ Created test: ${path.relative(process.cwd(), testPath)}`);
      
      this.generatedTests.push({
        path: testPath,
        type: 'unit-test'
      });
    } catch (error) {
      this.log(`❌ Failed to create test ${testPath}: ${error.message}`);
      throw error;
    }
  }

  createJestConfig() {
    const configPath = path.join(process.cwd(), 'jest.config.js');
    
    if (fs.existsSync(configPath)) {
      this.log('⚠️  Jest config already exists, skipping creation');
      return;
    }

    const config = `module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test-utils/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test-utils/**',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/__tests__/**'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}'
  ],
  transform: {
    '^.+\\\\.(ts|tsx)$': 'ts-jest'
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node']
};`;

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would create Jest config');
      return;
    }

    try {
      fs.writeFileSync(configPath, config);
      this.log('✅ Created Jest configuration');
    } catch (error) {
      this.log(`❌ Failed to create Jest config: ${error.message}`);
    }
  }

  createTestSetup() {
    const setupDir = path.join(process.cwd(), 'src/test-utils');
    const setupPath = path.join(setupDir, 'setupTests.ts');
    
    if (fs.existsSync(setupPath)) {
      this.log('⚠️  Test setup already exists, skipping creation');
      return;
    }

    const setupContent = `import '@testing-library/jest-dom';

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeEach(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};`;

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would create test setup');
      return;
    }

    try {
      fs.mkdirSync(setupDir, { recursive: true });
      fs.writeFileSync(setupPath, setupContent);
      this.log('✅ Created test setup file');
    } catch (error) {
      this.log(`❌ Failed to create test setup: ${error.message}`);
    }
  }

  createTestUtilities() {
    const utilsDir = path.join(process.cwd(), 'src/test-utils');
    const utilsPath = path.join(utilsDir, 'testUtils.tsx');
    
    const utilsContent = `import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';

// Create a custom render function that includes providers
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div>
      {/* Add any providers here (Router, Theme, etc.) */}
      {children}
    </div>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Helper functions for common test scenarios
export const mockConsole = () => {
  const originalConsole = { ...console };
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
  
  return () => {
    Object.assign(console, originalConsole);
  };
};

export const createMockTrade = (overrides = {}) => ({
  id: '1',
  symbol: 'AAPL',
  entryPrice: 150,
  currentPrice: 155,
  quantity: 100,
  direction: 'long',
  ...overrides
});

export const createMockTradingPlan = (overrides = {}) => ({
  symbol: 'AAPL',
  direction: 'long',
  entryPrice: 150,
  stopLoss: 145,
  takeProfit: 160,
  quantity: 100,
  reasoning: 'Test reasoning',
  keyLevels: [150, 155, 160],
  riskReward: 2,
  ...overrides
});`;

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would create test utilities');
      return;
    }

    try {
      fs.mkdirSync(utilsDir, { recursive: true });
      fs.writeFileSync(utilsPath, utilsContent);
      this.log('✅ Created test utilities');
    } catch (error) {
      this.log(`❌ Failed to create test utilities: ${error.message}`);
    }
  }

  updatePackageJsonTestScript() {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
      this.log('⚠️  package.json not found, skipping test script update');
      return;
    }

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would update package.json test scripts');
      return;
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // Add test scripts if they don't exist
      if (!packageJson.scripts) {
        packageJson.scripts = {};
      }

      if (!packageJson.scripts.test) {
        packageJson.scripts.test = 'jest';
      }

      if (!packageJson.scripts['test:watch']) {
        packageJson.scripts['test:watch'] = 'jest --watch';
      }

      if (!packageJson.scripts['test:coverage']) {
        packageJson.scripts['test:coverage'] = 'jest --coverage';
      }

      if (!packageJson.scripts['test:ci']) {
        packageJson.scripts['test:ci'] = 'jest --ci --coverage --watchAll=false';
      }

      // Add test dependencies if they don't exist
      if (!packageJson.devDependencies) {
        packageJson.devDependencies = {};
      }

      const testDependencies = {
        '@testing-library/react': '^13.4.0',
        '@testing-library/jest-dom': '^5.16.5',
        '@testing-library/user-event': '^14.4.3',
        'jest': '^29.3.1',
        'jest-environment-jsdom': '^29.3.1',
        'ts-jest': '^29.0.3'
      };

      for (const [dep, version] of Object.entries(testDependencies)) {
        if (!packageJson.devDependencies[dep]) {
          packageJson.devDependencies[dep] = version;
        }
      }

      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      this.log('✅ Updated package.json with test scripts and dependencies');
    } catch (error) {
      this.log(`❌ Failed to update package.json: ${error.message}`);
    }
  }

  generateTestReport() {
    if (this.dryRun) {
      this.log('📊 [DRY RUN] Would generate test generation report');
      return;
    }

    const reportPath = path.join(process.cwd(), 'phase6-test-report.md');
    const reportContent = `# Phase 6: Test Generation Report

Generated: ${new Date().toISOString()}

## Summary

- **Tests Generated**: ${this.generatedTests.length}
- **Critical Files Covered**: ${this.criticalFiles.length}
- **Test Framework**: Jest + React Testing Library

## Generated Tests

${this.generatedTests.map(test => `
- **${path.relative(process.cwd(), test.path)}**
  - Type: ${test.type}
  - Coverage: Unit tests with mocking
`).join('\n')}

## Test Coverage

${this.criticalFiles.map(file => `
### ${file.file}
- **Type**: ${file.type}
- **Priority**: ${file.priority}
- **Complexity**: ${file.complexity}
- **Test Status**: ✅ Generated
`).join('\n')}

## Test Configuration

- **Jest Config**: jest.config.js
- **Setup File**: src/test-utils/setupTests.ts
- **Test Utilities**: src/test-utils/testUtils.tsx
- **Coverage Threshold**: 70% for all metrics

## Running Tests

\`\`\`bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run tests for CI
npm run test:ci
\`\`\`

## Next Steps

1. Install test dependencies: \`npm install\`
2. Run tests to verify they pass: \`npm test\`
3. Adjust test cases based on actual component APIs
4. Add integration tests for complex workflows
5. Set up CI/CD pipeline to run tests automatically
6. Consider adding visual regression tests with Storybook
`;

    try {
      fs.writeFileSync(reportPath, reportContent);
      this.log('✅ Generated test report');
    } catch (error) {
      this.log(`❌ Failed to generate test report: ${error.message}`);
    }
  }

  async run() {
    this.log(`🚀 Starting Phase 6: Add Missing Tests ${this.dryRun ? '(DRY RUN)' : ''}`);
    
    try {
      // Create test infrastructure
      this.log('🔧 Setting up test infrastructure...');
      this.createJestConfig();
      this.createTestSetup();
      this.createTestUtilities();
      this.updatePackageJsonTestScript();

      // Generate tests for critical files
      this.log('🧪 Generating tests for critical files...');
      for (const fileConfig of this.criticalFiles) {
        const filePath = path.join(process.cwd(), fileConfig.file);
        
        if (!fs.existsSync(filePath)) {
          this.log(`⚠️  File does not exist: ${fileConfig.file}`);
          continue;
        }

        let testContent;
        let testPath;

        switch (fileConfig.type) {
          case 'hook':
            testContent = this.generateHookTests(fileConfig.file);
            testPath = path.join(path.dirname(filePath), '__tests__', `${path.basename(fileConfig.file, '.ts')}.test.ts`);
            break;
          
          case 'component':
            testContent = this.generateComponentTests(fileConfig.file);
            testPath = path.join(path.dirname(filePath), '__tests__', `${path.basename(fileConfig.file, path.extname(fileConfig.file))}.test.tsx`);
            break;
          
          case 'utility':
            testContent = this.generateUtilityTests(fileConfig.file);
            testPath = path.join(path.dirname(filePath), '__tests__', `${path.basename(fileConfig.file, '.ts')}.test.ts`);
            break;
          
          default:
            this.log(`⚠️  Unknown file type: ${fileConfig.type}`);
            continue;
        }

        this.createTestFile(testContent, testPath);
      }

      // Generate report
      this.log('📊 Generating test report...');
      this.generateTestReport();

      this.log(`✅ Phase 6 completed successfully! ${this.dryRun ? '(DRY RUN)' : ''}`);
      
      if (!this.dryRun) {
        this.log('📋 Next steps:');
        this.log('  1. Install test dependencies: npm install');
        this.log('  2. Run tests to verify they pass: npm test');
        this.log('  3. Adjust test cases based on actual APIs');
        this.log(`  4. Generated ${this.generatedTests.length} test files`);
        this.log('  5. Review phase6-test-report.md for details');
      }

    } catch (error) {
      this.log(`❌ Phase 6 failed: ${error.message}`);
      throw error;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  const phase6 = new Phase6TestGenerator(dryRun);
  phase6.run().catch(error => {
    console.error('❌ Phase 6 failed:', error.message);
    process.exit(1);
  });
}

module.exports = Phase6TestGenerator;