#!/usr/bin/env node

/**
 * Phase 2: Move Utility Functions
 * Move pure utility functions to new structure
 * Estimated time: 2-4 hours
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class Phase2UtilityMover {
  constructor(dryRun = false) {
    this.dryRun = dryRun;
    this.utilityFiles = [
      // Config files
      { from: '.eslintrc.js', to: 'src/utils/config/.eslintrc.js', category: 'config' },
      
      // Mock files
      { from: '__mocks__/fileMock.js', to: 'src/utils/mocks/fileMock.js', category: 'mocks' },
      { from: '__mocks__/styleMock.js', to: 'src/utils/mocks/styleMock.js', category: 'mocks' },
      
      // Build and config utilities
      { from: 'babel.config.js', to: 'src/utils/config/babel.config.js', category: 'config' },
      { from: 'vitest.config.ts', to: 'src/utils/config/vitest.config.ts', category: 'config' },
      { from: 'vitest.setup.ts', to: 'src/utils/config/vitest.setup.ts', category: 'config' },
      { from: 'playwright.config.ts', to: 'src/utils/config/playwright.config.ts', category: 'config' },
      { from: 'tsconfig.json', to: 'src/utils/config/tsconfig.json', category: 'config' },
      
      // Analysis and health tools
      { from: 'codebase-analyzer.js', to: 'src/utils/analysis/codebase-analyzer.js', category: 'analysis' },
      { from: 'architectural-improvement-planner.js', to: 'src/utils/analysis/architectural-improvement-planner.js', category: 'analysis' },
      { from: 'ast-parser.js', to: 'src/utils/analysis/ast-parser.js', category: 'analysis' },
      { from: 'blank-page-diagnostic.js', to: 'src/utils/analysis/blank-page-diagnostic.js', category: 'analysis' },
      { from: 'health-diagnostic.js', to: 'src/utils/analysis/health-diagnostic.js', category: 'analysis' },
      { from: 'ts-issue-scanner.js', to: 'src/utils/analysis/ts-issue-scanner.js', category: 'analysis' },
      { from: 'schema-validator.js', to: 'src/utils/validation/schema-validator.js', category: 'validation' },
      
      // Scripts
      { from: 'cli.js', to: 'src/utils/scripts/cli.js', category: 'scripts' },
      { from: 'server.js', to: 'src/utils/scripts/server.js', category: 'scripts' },
      { from: 'setup-code-health.js', to: 'src/utils/scripts/setup-code-health.js', category: 'scripts' }
    ];
    
    this.importMappings = new Map();
  }

  log(message) {
    console.log(`[Phase 2] ${message}`);
  }

  createUtilitySubfolders() {
    const subfolders = [
      'src/utils/config',
      'src/utils/mocks', 
      'src/utils/analysis',
      'src/utils/validation',
      'src/utils/scripts',
      'src/utils/formatting',
      'src/utils/calculations',
      'src/utils/helpers'
    ];

    for (const folder of subfolders) {
      const fullPath = path.join(process.cwd(), folder);
      
      if (fs.existsSync(fullPath)) {
        this.log(`📁 Subfolder already exists: ${folder}`);
        continue;
      }

      if (this.dryRun) {
        this.log(`🏗️  [DRY RUN] Would create subfolder: ${folder}`);
        continue;
      }

      try {
        fs.mkdirSync(fullPath, { recursive: true });
        this.log(`✅ Created subfolder: ${folder}`);
      } catch (error) {
        this.log(`❌ Failed to create subfolder ${folder}: ${error.message}`);
        throw error;
      }
    }
  }

  moveFile(fileConfig) {
    const fromPath = path.join(process.cwd(), fileConfig.from);
    const toPath = path.join(process.cwd(), fileConfig.to);

    if (!fs.existsSync(fromPath)) {
      this.log(`⚠️  Source file does not exist: ${fileConfig.from}`);
      return;
    }

    if (fs.existsSync(toPath)) {
      this.log(`⚠️  Destination file already exists: ${fileConfig.to}`);
      return;
    }

    if (this.dryRun) {
      this.log(`📦 [DRY RUN] Would move: ${fileConfig.from} → ${fileConfig.to}`);
      return;
    }

    try {
      // Ensure destination directory exists
      const toDir = path.dirname(toPath);
      fs.mkdirSync(toDir, { recursive: true });

      // Use git mv for proper tracking
      execSync(`git mv "${fromPath}" "${toPath}"`);
      this.log(`✅ Moved: ${fileConfig.from} → ${fileConfig.to}`);

      // Store mapping for import updates
      this.importMappings.set(fileConfig.from, fileConfig.to);
    } catch (error) {
      this.log(`❌ Failed to move ${fileConfig.from}: ${error.message}`);
      throw error;
    }
  }

  updateImports() {
    this.log('🔄 Updating import statements...');

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would update imports for moved files');
      return;
    }

    // Find all TypeScript and JavaScript files that might need import updates
    const sourceFiles = this.findSourceFiles();
    
    for (const filePath of sourceFiles) {
      this.updateImportsInFile(filePath);
    }
  }

  findSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const ignorePaths = ['node_modules', '.git', 'dist', 'build'];
    
    const findFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !ignorePaths.includes(item)) {
          files.push(...findFiles(fullPath));
        } else if (stat.isFile() && extensions.includes(path.extname(item))) {
          files.push(fullPath);
        }
      }
      
      return files;
    };
    
    return findFiles(process.cwd());
  }

  updateImportsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let updatedContent = content;
      let hasChanges = false;

      // Update imports for moved files
      for (const [oldPath, newPath] of this.importMappings) {
        const oldImportPath = this.getRelativeImportPath(filePath, oldPath);
        const newImportPath = this.getRelativeImportPath(filePath, newPath);

        // Match various import patterns
        const importPatterns = [
          new RegExp(`import\\s+.*\\s+from\\s+['"\`]${this.escapeRegex(oldImportPath)}['"\`]`, 'g'),
          new RegExp(`require\\(['"\`]${this.escapeRegex(oldImportPath)}['"\`]\\)`, 'g'),
          new RegExp(`import\\(['"\`]${this.escapeRegex(oldImportPath)}['"\`]\\)`, 'g')
        ];

        for (const pattern of importPatterns) {
          const newContent = updatedContent.replace(pattern, (match) => {
            return match.replace(oldImportPath, newImportPath);
          });

          if (newContent !== updatedContent) {
            updatedContent = newContent;
            hasChanges = true;
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, updatedContent);
        this.log(`🔄 Updated imports in: ${path.relative(process.cwd(), filePath)}`);
      }
    } catch (error) {
      this.log(`⚠️  Failed to update imports in ${filePath}: ${error.message}`);
    }
  }

  getRelativeImportPath(fromFile, toFile) {
    const fromDir = path.dirname(fromFile);
    const relativePath = path.relative(fromDir, toFile);
    
    // Remove file extension for imports
    const withoutExt = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Ensure relative paths start with './' or '../'
    if (!withoutExt.startsWith('.')) {
      return './' + withoutExt;
    }
    
    return withoutExt;
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  createCategoryIndexFiles() {
    const categories = {
      config: 'Configuration files and build setup',
      mocks: 'Mock files for testing',
      analysis: 'Code analysis and health diagnostic tools',
      validation: 'Data validation utilities',
      scripts: 'Build and development scripts',
      formatting: 'Data formatting utilities',
      calculations: 'Mathematical and trading calculations',
      helpers: 'General helper functions'
    };

    for (const [category, description] of Object.entries(categories)) {
      const indexPath = path.join(process.cwd(), `src/utils/${category}/index.ts`);
      
      if (fs.existsSync(indexPath)) {
        this.log(`📄 Category index already exists: src/utils/${category}/index.ts`);
        continue;
      }

      if (this.dryRun) {
        this.log(`📝 [DRY RUN] Would create category index: src/utils/${category}/index.ts`);
        continue;
      }

      const content = `/**
 * ${description}
 */

// Export all utilities in this category
// Files will be added here as they are moved
`;

      try {
        fs.writeFileSync(indexPath, content);
        this.log(`✅ Created category index: src/utils/${category}/index.ts`);
      } catch (error) {
        this.log(`❌ Failed to create category index ${category}: ${error.message}`);
        throw error;
      }
    }
  }

  updateMainUtilsIndex() {
    const indexPath = path.join(process.cwd(), 'src/utils/index.ts');
    
    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would update main utils index file');
      return;
    }

    const content = `/**
 * Utility functions exports
 * Organized by category for better maintainability
 */

// Configuration utilities
export * from './config';

// Testing utilities
export * from './mocks';

// Analysis and diagnostic tools
export * from './analysis';

// Validation utilities
export * from './validation';

// Scripts and automation
export * from './scripts';

// Data formatting
export * from './formatting';

// Calculations
export * from './calculations';

// General helpers
export * from './helpers';
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log('✅ Updated main utils index file');
    } catch (error) {
      this.log(`❌ Failed to update main utils index: ${error.message}`);
      throw error;
    }
  }

  validateMoves() {
    this.log('🔍 Validating file moves...');
    
    let errors = 0;
    
    for (const fileConfig of this.utilityFiles) {
      const fromPath = path.join(process.cwd(), fileConfig.from);
      const toPath = path.join(process.cwd(), fileConfig.to);
      
      if (fs.existsSync(fromPath) && fs.existsSync(toPath)) {
        this.log(`❌ Both source and destination exist: ${fileConfig.from}`);
        errors++;
      } else if (!fs.existsSync(fromPath) && !fs.existsSync(toPath)) {
        this.log(`⚠️  Neither source nor destination exist: ${fileConfig.from}`);
      } else if (fs.existsSync(toPath)) {
        this.log(`✅ File successfully moved: ${fileConfig.from} → ${fileConfig.to}`);
      }
    }

    if (errors > 0) {
      throw new Error(`Validation failed with ${errors} errors`);
    }

    this.log('✅ File move validation passed');
  }

  async run() {
    this.log(`🚀 Starting Phase 2: Move Utility Functions ${this.dryRun ? '(DRY RUN)' : ''}`);
    
    try {
      // Create utility subfolders
      this.log('📁 Creating utility subfolders...');
      this.createUtilitySubfolders();

      // Move utility files
      this.log('📦 Moving utility files...');
      for (const fileConfig of this.utilityFiles) {
        this.moveFile(fileConfig);
      }

      // Update import statements
      this.log('🔄 Updating import statements...');
      this.updateImports();

      // Create category index files
      this.log('📄 Creating category index files...');
      this.createCategoryIndexFiles();

      // Update main utils index
      this.log('📝 Updating main utils index...');
      this.updateMainUtilsIndex();

      // Validate moves
      if (!this.dryRun) {
        this.validateMoves();
      }

      this.log(`✅ Phase 2 completed successfully! ${this.dryRun ? '(DRY RUN)' : ''}`);
      
      if (!this.dryRun) {
        this.log('📋 Next steps:');
        this.log('  1. Test that imports are working correctly');
        this.log('  2. Run Phase 3 to extract and move custom hooks');
        this.log('  3. Update any remaining import paths if needed');
        this.log(`  4. Review moved files: ${this.importMappings.size} files relocated`);
      }

    } catch (error) {
      this.log(`❌ Phase 2 failed: ${error.message}`);
      throw error;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  const phase2 = new Phase2UtilityMover(dryRun);
  phase2.run().catch(error => {
    console.error('❌ Phase 2 failed:', error.message);
    process.exit(1);
  });
}

module.exports = Phase2UtilityMover;