# Architectural Migration Scripts

Comprehensive automation scripts for the 6-phase architectural improvement migration based on the analysis in `architecture-analysis.txt`.

## Overview

This migration transforms a monolithic dashboard structure into a clean, modular architecture with:
- Proper folder organization
- Component splitting and extraction
- Custom hooks for state management
- Comprehensive testing coverage
- Automated import management

## Scripts Overview

### 🎯 Main Orchestrator
- **`migration-orchestrator.js`** - Central command center for managing all migration phases

### 📁 Phase Scripts
1. **`phase1-folders.js`** - Create new folder structure
2. **`phase2-utilities.js`** - Move utility functions
3. **`phase3-hooks.js`** - Extract and move custom hooks
4. **`phase4-components.js`** - Split large components
5. **`phase5-reorganize.js`** - Reorganize components by category
6. **`phase6-tests.js`** - Generate comprehensive tests

### 🔧 Supporting Tools
- **`import-updater.js`** - Advanced import path automation
- **`validation-rollback.js`** - Validation and rollback capabilities

## Quick Start

### 1. Dry Run (Recommended First)
```bash
# Run complete migration in dry-run mode
node migration-orchestrator.js run-all --dry-run

# Or run individual phases
node migration-orchestrator.js run 1 --dry-run
```

### 2. Execute Migration
```bash
# Run all phases
node migration-orchestrator.js run-all

# Or run phases individually
node migration-orchestrator.js run 1
node migration-orchestrator.js run 2
# ... continue through phase 6
```

### 3. Validate Results
```bash
# Run comprehensive validation
node validation-rollback.js validate

# Check specific areas
node validation-rollback.js validate-imports
node validation-rollback.js validate-build
```

## Phase Details

### Phase 1: Create Folder Structure (30 min)
**Risk Level**: Low
```bash
node migration-orchestrator.js run 1
```

Creates:
- `src/components/ui/` - Basic reusable UI components
- `src/components/feature/` - Business feature components
- `src/components/layout/` - Layout and navigation
- `src/pages/` - Top-level page components
- `src/hooks/` - Custom hooks
- `src/services/` - API clients
- `src/utils/` - Utility functions
- `src/types/` - TypeScript definitions

### Phase 2: Move Utilities (2-4 hours)
**Risk Level**: Low
```bash
node migration-orchestrator.js run 2
```

Moves 109 utility files to organized structure:
- Configuration files → `src/utils/config/`
- Mock files → `src/utils/mocks/`
- Analysis tools → `src/utils/analysis/`
- Validation utilities → `src/utils/validation/`

### Phase 3: Extract Hooks (1-2 days)
**Risk Level**: Medium
```bash
node migration-orchestrator.js run 3
```

Extracts custom hooks:
- `useTradingDashboardData` - State management for trading dashboard
- `useDailyGuide` - Daily guide functionality
- Updates 1 import dependency

### Phase 4: Split Components (3-5 days)
**Risk Level**: High
```bash
node migration-orchestrator.js run 4
```

Splits 37 large components:
- `ProfitLossCell` → Core component + Calculator utility
- `TradingPlan` → Container + View + Hook
- `KeyLevels` → Display component + Data hook
- Updates 37 import dependencies

### Phase 5: Reorganize Components (1-2 days)
**Risk Level**: Medium
```bash
node migration-orchestrator.js run 5
```

Moves 85 components to appropriate folders:
- UI components → `src/components/ui/`
- Feature components → `src/components/feature/`
- Layout components → `src/components/layout/`
- Page components → `src/pages/`
- Updates 171 import dependencies

### Phase 6: Add Tests (2-3 days)
**Risk Level**: Low
```bash
node migration-orchestrator.js run 6
```

Generates tests for 32 critical files:
- Hook tests with React Testing Library
- Component tests with Jest
- Utility function tests
- Test infrastructure setup

## Advanced Usage

### Running Specific Phase Ranges
```bash
# Run phases 2-4 only
node migration-orchestrator.js run-all --start-from=2 --stop-at=4

# Skip to phase 5
node migration-orchestrator.js run-all --start-from=5
```

### Import Management
```bash
# Auto-update all imports after manual changes
node import-updater.js

# Use custom mappings file
node import-updater.js --mappings=custom-mappings.json

# Verbose output
node import-updater.js --verbose
```

### Validation and Troubleshooting
```bash
# Full validation suite
node validation-rollback.js validate

# Individual validations
node validation-rollback.js validate-structure
node validation-rollback.js validate-imports
node validation-rollback.js validate-syntax
node validation-rollback.js validate-tests
node validation-rollback.js validate-build
```

### Rollback Operations
```bash
# Rollback specific phase
node validation-rollback.js rollback 3

# Emergency rollback to beginning
node validation-rollback.js emergency-rollback

# Check migration status
node migration-orchestrator.js status
```

## Safety Features

### Backup System
- Automatic git commits before each phase
- Rollback points stored in `migration-backups/`
- Migration state tracking in `migration-state.json`

### Validation
- Prerequisites checking before execution
- Import resolution validation
- TypeScript compilation checks
- Test execution verification
- Build process validation

### Dry Run Mode
All scripts support `--dry-run` to preview changes without execution:
```bash
node migration-orchestrator.js run-all --dry-run
node import-updater.js --dry-run
node validation-rollback.js validate --dry-run
```

## Output Files

### Reports
- `migration.log` - Detailed execution log
- `migration-state.json` - Current migration state
- `validation-report.md` - Validation results
- `import-update-report.md` - Import transformation details
- `phase5-migration-report.md` - Component reorganization details
- `phase6-test-report.md` - Test generation summary

### Generated Code
- Index files for each folder category
- README files with guidelines
- Hook implementations
- Split component files
- Comprehensive test suites
- Jest configuration

## Dependencies

### Required
- Node.js 14+
- Git (for backup and rollback)
- npm/yarn

### Recommended
- TypeScript compiler (`npx tsc`)
- ESLint for code quality
- Jest for testing

## Troubleshooting

### Common Issues

**Import Resolution Errors**
```bash
# Re-run import updater
node import-updater.js

# Check specific imports
node validation-rollback.js validate-imports
```

**Build Failures**
```bash
# Validate syntax first
node validation-rollback.js validate-syntax

# Check build process
node validation-rollback.js validate-build
```

**Migration State Issues**
```bash
# Check current state
node migration-orchestrator.js status

# Reset state (caution!)
rm migration-state.json
```

### Getting Help
```bash
# Show command help
node migration-orchestrator.js
node import-updater.js
node validation-rollback.js
```

## Best Practices

1. **Always start with dry-run** to preview changes
2. **Run validation** after each phase
3. **Commit changes** after successful phases
4. **Test thoroughly** before proceeding to next phase
5. **Keep backups** until migration is fully validated
6. **Update documentation** as you progress

## Architecture Benefits

After completing all phases, you'll have:

✅ **Clean Architecture**
- Organized folder structure
- Separation of concerns
- Reusable components

✅ **Better Maintainability**
- Smaller, focused files
- Clear dependencies
- Consistent patterns

✅ **Improved Testing**
- Comprehensive test coverage
- Isolated unit tests
- Test infrastructure

✅ **Enhanced Developer Experience**
- Clear file organization
- Easier navigation
- Better tooling support

## Support

For issues or questions:
1. Check the generated reports for detailed error information
2. Use `--verbose` flag for detailed logging
3. Review the validation output for specific problems
4. Consider rollback if issues persist