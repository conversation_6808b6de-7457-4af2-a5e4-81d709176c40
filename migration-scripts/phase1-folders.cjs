#!/usr/bin/env node

/**
 * Phase 1: Create New Folder Structure
 * Low-risk operation that creates the new architectural folders
 * Estimated time: 30 minutes
 */

const fs = require('fs');
const path = require('path');

class Phase1FolderCreator {
  constructor(dryRun = false) {
    this.dryRun = dryRun;
    this.folders = [
      'src/components/ui',
      'src/components/feature', 
      'src/components/layout',
      'src/pages',
      'src/hooks',
      'src/services',
      'src/utils',
      'src/types'
    ];
  }

  log(message) {
    console.log(`[Phase 1] ${message}`);
  }

  createFolder(folderPath) {
    const fullPath = path.join(process.cwd(), folderPath);
    
    if (fs.existsSync(fullPath)) {
      this.log(`📁 Folder already exists: ${folderPath}`);
      return;
    }

    if (this.dryRun) {
      this.log(`🏗️  [DRY RUN] Would create: ${folderPath}`);
      return;
    }

    try {
      fs.mkdirSync(fullPath, { recursive: true });
      this.log(`✅ Created folder: ${folderPath}`);
    } catch (error) {
      this.log(`❌ Failed to create folder ${folderPath}: ${error.message}`);
      throw error;
    }
  }

  createIndexFiles() {
    const indexFiles = [
      { path: 'src/components/index.ts', content: this.generateComponentIndex() },
      { path: 'src/hooks/index.ts', content: this.generateHooksIndex() },
      { path: 'src/services/index.ts', content: this.generateServicesIndex() },
      { path: 'src/utils/index.ts', content: this.generateUtilsIndex() },
      { path: 'src/types/index.ts', content: this.generateTypesIndex() }
    ];

    for (const file of indexFiles) {
      const fullPath = path.join(process.cwd(), file.path);
      
      if (fs.existsSync(fullPath)) {
        this.log(`📄 Index file already exists: ${file.path}`);
        continue;
      }

      if (this.dryRun) {
        this.log(`📝 [DRY RUN] Would create index file: ${file.path}`);
        continue;
      }

      try {
        fs.writeFileSync(fullPath, file.content);
        this.log(`✅ Created index file: ${file.path}`);
      } catch (error) {
        this.log(`❌ Failed to create index file ${file.path}: ${error.message}`);
        throw error;
      }
    }
  }

  generateComponentIndex() {
    return `/**
 * Component exports organized by category
 * This file will be populated as components are moved to their new locations
 */

// UI Components
export * from './ui';

// Feature Components  
export * from './feature';

// Layout Components
export * from './layout';
`;
  }

  generateHooksIndex() {
    return `/**
 * Custom hooks exports
 * This file will be populated as hooks are extracted and moved
 */

// Export all custom hooks here
// Example: export { useTradingDashboardData } from './useTradingDashboardData';
`;
  }

  generateServicesIndex() {
    return `/**
 * Service layer exports
 * API clients and external service integrations
 */

// Export all services here
// Example: export { dailyGuideApi } from './dailyGuideApi';
// Example: export { tradeAnalysisApi } from './tradeAnalysisApi';
`;
  }

  generateUtilsIndex() {
    return `/**
 * Utility functions exports
 * Pure utility functions and helpers
 */

// Export all utility functions here
// They will be moved here in Phase 2
`;
  }

  generateTypesIndex() {
    return `/**
 * TypeScript type definitions exports
 * Shared types used across the application
 */

// Export all shared types here
// Example: export * from './trade-types';
// Example: export * from './ui-types';
`;
  }

  createReadmeFiles() {
    const readmeFiles = [
      { 
        path: 'src/components/ui/README.md', 
        content: this.generateUIReadme() 
      },
      { 
        path: 'src/components/feature/README.md', 
        content: this.generateFeatureReadme() 
      },
      { 
        path: 'src/components/layout/README.md', 
        content: this.generateLayoutReadme() 
      },
      { 
        path: 'src/hooks/README.md', 
        content: this.generateHooksReadme() 
      },
      { 
        path: 'src/services/README.md', 
        content: this.generateServicesReadme() 
      },
      { 
        path: 'src/utils/README.md', 
        content: this.generateUtilsReadme() 
      }
    ];

    for (const file of readmeFiles) {
      const fullPath = path.join(process.cwd(), file.path);
      
      if (fs.existsSync(fullPath)) {
        this.log(`📖 README already exists: ${file.path}`);
        continue;
      }

      if (this.dryRun) {
        this.log(`📚 [DRY RUN] Would create README: ${file.path}`);
        continue;
      }

      try {
        fs.writeFileSync(fullPath, file.content);
        this.log(`✅ Created README: ${file.path}`);
      } catch (error) {
        this.log(`❌ Failed to create README ${file.path}: ${error.message}`);
        throw error;
      }
    }
  }

  generateUIReadme() {
    return `# UI Components

Basic, reusable UI components like buttons, inputs, forms, etc.

## Guidelines

- Components should be pure and reusable
- No business logic or API calls
- Should accept props for customization
- Include proper TypeScript types
- Add Storybook stories for documentation

## Structure

\`\`\`
ui/
├── buttons/
├── forms/
├── inputs/
├── modals/
└── index.ts
\`\`\`
`;
  }

  generateFeatureReadme() {
    return `# Feature Components

Components tied to specific business features and domains.

## Guidelines

- Components specific to business logic
- Can contain state management
- May integrate with services/APIs
- Should be well-tested
- Organize by feature domain

## Structure

\`\`\`
feature/
├── trading/
├── analysis/
├── settings/
└── index.ts
\`\`\`
`;
  }

  generateLayoutReadme() {
    return `# Layout Components

App layout, navigation, headers, and structural components.

## Guidelines

- Components for app structure
- Navigation and routing
- Headers, footers, sidebars
- Responsive design considerations

## Structure

\`\`\`
layout/
├── headers/
├── navigation/
├── containers/
└── index.ts
\`\`\`
`;
  }

  generateHooksReadme() {
    return `# Custom Hooks

Reusable custom React hooks for state management and side effects.

## Guidelines

- Follow React hooks rules
- Single responsibility principle
- Should be well-tested
- Include proper TypeScript types
- Document parameters and return values

## Structure

\`\`\`
hooks/
├── useTradingData.ts
├── useLocalStorage.ts
├── useApi.ts
└── index.ts
\`\`\`
`;
  }

  generateServicesReadme() {
    return `# Services

API clients and external service integrations.

## Guidelines

- Handle all external API communication
- Include error handling
- Use consistent patterns
- Should be framework-agnostic
- Include proper typing for API responses

## Structure

\`\`\`
services/
├── api/
├── storage/
├── analytics/
└── index.ts
\`\`\`
`;
  }

  generateUtilsReadme() {
    return `# Utilities

Pure utility functions and helpers.

## Guidelines

- Pure functions only (no side effects)
- Should be easily testable
- Single responsibility
- Framework-agnostic
- Include proper TypeScript types

## Structure

\`\`\`
utils/
├── formatting/
├── validation/
├── calculations/
└── index.ts
\`\`\`
`;
  }

  validateStructure() {
    this.log('🔍 Validating folder structure...');
    
    for (const folder of this.folders) {
      const fullPath = path.join(process.cwd(), folder);
      if (!fs.existsSync(fullPath)) {
        throw new Error(`Expected folder does not exist: ${folder}`);
      }
    }

    this.log('✅ Folder structure validation passed');
  }

  async run() {
    this.log(`🚀 Starting Phase 1: Create New Folder Structure ${this.dryRun ? '(DRY RUN)' : ''}`);
    
    try {
      // Create main folders
      this.log('📁 Creating folder structure...');
      for (const folder of this.folders) {
        this.createFolder(folder);
      }

      // Create index files
      this.log('📄 Creating index files...');
      this.createIndexFiles();

      // Create README files for documentation
      this.log('📚 Creating README files...');
      this.createReadmeFiles();

      // Validate the structure
      if (!this.dryRun) {
        this.validateStructure();
      }

      this.log(`✅ Phase 1 completed successfully! ${this.dryRun ? '(DRY RUN)' : ''}`);
      
      if (!this.dryRun) {
        this.log('📋 Next steps:');
        this.log('  1. Review the created folder structure');
        this.log('  2. Run Phase 2 to move utility functions');
        this.log('  3. Update any existing import paths if needed');
      }

    } catch (error) {
      this.log(`❌ Phase 1 failed: ${error.message}`);
      throw error;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  const phase1 = new Phase1FolderCreator(dryRun);
  phase1.run().catch(error => {
    console.error('❌ Phase 1 failed:', error.message);
    process.exit(1);
  });
}

module.exports = Phase1FolderCreator;