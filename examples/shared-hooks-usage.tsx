/**
 * Shared Hooks Usage Examples
 * 
 * This file demonstrates how to use the new shared hooks created in Phase 1
 * of the refactoring plan. These examples show the patterns for the team.
 */

import React from 'react';
import { 
  useLoadingState, 
  useFormField, 
  useDataSection,
  useProfitLossFormatting,
  useDataFormatting,
  validationRules,
  EnhancedFormField,
  LoadingSpinner,
  SortableTable
} from '@adhd-trading-dashboard/shared';

// ============================================================================
// EXAMPLE 1: useLoadingState Hook
// ============================================================================

/**
 * Example: Using useLoadingState for consistent loading patterns
 * 
 * BEFORE: Manual loading state management in every component
 * AFTER: Centralized loading logic with error handling
 */
export const LoadingStateExample: React.FC = () => {
  const { isLoading, error, withLoading } = useLoadingState();

  const fetchTrades = async () => {
    // The withLoading wrapper automatically handles loading states
    const trades = await withLoading(async () => {
      const response = await fetch('/api/trades');
      if (!response.ok) throw new Error('Failed to fetch trades');
      return response.json();
    });
    
    console.log('Trades loaded:', trades);
  };

  return (
    <div>
      <h3>Loading State Example</h3>
      <button onClick={fetchTrades} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Fetch Trades'}
      </button>
      {error && <div style={{ color: 'red' }}>Error: {error}</div>}
    </div>
  );
};

// ============================================================================
// EXAMPLE 2: useFormField Hook with Validation
// ============================================================================

/**
 * Example: Using useFormField for consistent form behavior
 * 
 * BEFORE: Manual form state and validation in every form
 * AFTER: Centralized form logic with built-in validation
 */
export const FormFieldExample: React.FC = () => {
  const emailField = useFormField({
    initialValue: '',
    required: true,
    type: 'email',
    validationRules: [
      validationRules.required('Email is required'),
      validationRules.email('Please enter a valid email'),
    ],
    validateOnBlur: true,
  });

  const amountField = useFormField({
    initialValue: 0,
    required: true,
    type: 'number',
    validationRules: [
      validationRules.required('Amount is required'),
      validationRules.min(0.01, 'Amount must be greater than 0'),
    ],
    validateOnChange: true,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const emailValid = await emailField.validate();
    const amountValid = await amountField.validate();
    
    if (emailValid && amountValid) {
      console.log('Form data:', {
        email: emailField.value,
        amount: amountField.value,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h3>Form Field Example</h3>
      
      {/* Using the hook directly */}
      <div>
        <label>Email:</label>
        <input
          type="email"
          value={emailField.value}
          onChange={emailField.handleChange}
          onBlur={emailField.handleBlur}
          style={{ borderColor: emailField.error ? 'red' : 'gray' }}
        />
        {emailField.error && emailField.touched && (
          <div style={{ color: 'red', fontSize: '12px' }}>{emailField.error}</div>
        )}
      </div>

      {/* Using the EnhancedFormField component */}
      <EnhancedFormField
        name="amount"
        label="Trade Amount"
        type="number"
        required={true}
        validationRules={[
          validationRules.required('Amount is required'),
          validationRules.min(0.01, 'Amount must be greater than 0'),
        ]}
        placeholder="Enter trade amount"
        helpText="Enter the trade amount in USD"
      />

      <button type="submit">Submit</button>
    </form>
  );
};

// ============================================================================
// EXAMPLE 3: useDataSection Hook
// ============================================================================

/**
 * Example: Using useDataSection for consistent data loading patterns
 * 
 * BEFORE: Manual data fetching, loading, and error states in every component
 * AFTER: Centralized data section logic with automatic refresh
 */
export const DataSectionExample: React.FC = () => {
  const tradesSection = useDataSection({
    fetchData: async () => {
      const response = await fetch('/api/trades');
      if (!response.ok) throw new Error('Failed to fetch trades');
      return response.json();
    },
    fetchOnMount: true,
    refreshInterval: 30000, // Refresh every 30 seconds
    isEmpty: (trades) => !trades || trades.length === 0,
  });

  if (tradesSection.isLoading) {
    return <LoadingSpinner size="lg" />;
  }

  if (tradesSection.error) {
    return (
      <div>
        <div style={{ color: 'red' }}>Error: {tradesSection.error}</div>
        <button onClick={tradesSection.refresh}>Retry</button>
      </div>
    );
  }

  if (tradesSection.isEmpty) {
    return <div>No trades found</div>;
  }

  return (
    <div>
      <h3>Data Section Example</h3>
      <div>
        <button onClick={tradesSection.refresh}>Refresh</button>
        <span>Last updated: {tradesSection.lastFetched?.toLocaleTimeString()}</span>
      </div>
      <div>Found {tradesSection.data?.length} trades</div>
    </div>
  );
};

// ============================================================================
// EXAMPLE 4: useProfitLossFormatting Hook
// ============================================================================

/**
 * Example: Using useProfitLossFormatting for consistent P&L display
 * 
 * BEFORE: Duplicate formatting logic in every component
 * AFTER: Centralized formatting with consistent styling
 */
export const ProfitLossFormattingExample: React.FC = () => {
  const { formatCurrency, formatPercent } = useDataFormatting();
  
  const profit = useProfitLossFormatting(1234.56, {
    currency: '$',
    showPositiveSign: true,
  });

  const loss = useProfitLossFormatting(-567.89, {
    currency: '$',
  });

  const neutral = useProfitLossFormatting(0, {
    currency: '$',
  });

  return (
    <div>
      <h3>Profit/Loss Formatting Example</h3>
      
      <div style={{ color: profit.isProfit ? 'green' : 'red' }}>
        Profit: {profit.formattedAmount}
      </div>
      
      <div style={{ color: loss.isLoss ? 'red' : 'green' }}>
        Loss: {loss.formattedAmount}
      </div>
      
      <div style={{ color: 'gray' }}>
        Neutral: {neutral.formattedAmount}
      </div>

      <div>
        <h4>Data Formatting Examples:</h4>
        <div>Currency: {formatCurrency(1234.56)}</div>
        <div>Percentage: {formatPercent(75.5)}</div>
        <div>Date: {new Date().toLocaleDateString()}</div>
      </div>
    </div>
  );
};

// ============================================================================
// EXAMPLE 5: Complete Component Using Multiple Hooks
// ============================================================================

/**
 * Example: Complete component using multiple shared hooks
 * 
 * This shows how the hooks work together to create a clean, maintainable component
 */
export const CompleteExample: React.FC = () => {
  const { formatCurrency } = useDataFormatting();
  
  const tradesData = useDataSection({
    fetchData: async () => {
      // Simulate API call
      return [
        { id: 1, symbol: 'EURUSD', profitLoss: 123.45, date: '2023-12-01' },
        { id: 2, symbol: 'GBPUSD', profitLoss: -67.89, date: '2023-12-02' },
        { id: 3, symbol: 'USDJPY', profitLoss: 234.56, date: '2023-12-03' },
      ];
    },
    fetchOnMount: true,
  });

  const columns = [
    { field: 'symbol' as const, label: 'Symbol', sortable: true },
    { field: 'profitLoss' as const, label: 'P&L', sortable: true },
    { field: 'date' as const, label: 'Date', sortable: true },
  ];

  const renderCell = (value: any, row: any, column: any) => {
    if (column.field === 'profitLoss') {
      return (
        <span style={{ color: value > 0 ? 'green' : 'red' }}>
          {formatCurrency(value, { showPositiveSign: true })}
        </span>
      );
    }
    return value;
  };

  if (tradesData.isLoading) return <LoadingSpinner />;
  if (tradesData.error) return <div>Error: {tradesData.error}</div>;

  return (
    <div>
      <h3>Complete Example: Trades Table</h3>
      <SortableTable
        data={tradesData.data || []}
        columns={columns}
        renderCell={renderCell}
        defaultSort={{ field: 'profitLoss', direction: 'desc' }}
      />
    </div>
  );
};

// ============================================================================
// MAIN EXAMPLE COMPONENT
// ============================================================================

export const SharedHooksExamples: React.FC = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h1>Shared Hooks Usage Examples</h1>
      <p>
        These examples demonstrate the new shared hooks created in Phase 1 of the refactoring plan.
        Each hook provides consistent behavior and reduces boilerplate code.
      </p>
      
      <LoadingStateExample />
      <hr />
      
      <FormFieldExample />
      <hr />
      
      <DataSectionExample />
      <hr />
      
      <ProfitLossFormattingExample />
      <hr />
      
      <CompleteExample />
    </div>
  );
};

export default SharedHooksExamples;
