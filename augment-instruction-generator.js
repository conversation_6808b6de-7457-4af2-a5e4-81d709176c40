#!/usr/bin/env node

/**
 * Augment AI Instruction Generator
 * Converts architecture analysis into specific, actionable tasks for AI assistants
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AugmentInstructionGenerator {
  constructor(options = {}) {
    this.rootDir = options.rootDir || process.cwd();
    this.analysisFile = options.analysisFile || 'architecture-analysis.json';
    this.outputDir = options.outputDir || 'augment-instructions';
    this.maxFilesPerTask = options.maxFilesPerTask || 5;
    this.priorityThreshold = options.priorityThreshold || 'medium';

    this.analysis = null;
    this.instructions = {
      immediate: [], // High-impact, low-effort tasks
      quick: [], // Can be done in < 30 minutes
      moderate: [], // 1-3 hour tasks
      comprehensive: [], // Multi-day tasks
    };
  }

  log(message) {
    console.log(`[Instruction Generator] ${message}`);
  }

  /**
   * Main generation process
   */
  async generate() {
    this.log('🤖 Generating Augment AI instructions...');

    try {
      // 1. Load analysis data
      await this.loadAnalysis();

      // 2. Generate task categories
      await this.generateImmediateTasks();
      await this.generateQuickTasks();
      await this.generateModerateTasks();
      await this.generateComprehensiveTasks();

      // 3. Output instructions
      await this.outputInstructions();

      // 4. Generate summary
      await this.generateSummary();

      this.log('✅ Instructions generated successfully!');
    } catch (error) {
      this.log(`❌ Generation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load architecture analysis
   */
  async loadAnalysis() {
    const analysisPath = path.join(this.rootDir, this.analysisFile);

    try {
      const content = await fs.readFile(analysisPath, 'utf8');
      this.analysis = JSON.parse(content);
      this.log(`📊 Loaded analysis with ${this.analysis.components?.length || 0} components`);
    } catch (error) {
      throw new Error(`Failed to load analysis file ${analysisPath}: ${error.message}`);
    }
  }

  /**
   * Generate immediate, high-impact tasks
   */
  async generateImmediateTasks() {
    this.log('⚡ Generating immediate tasks...');

    const { analysis } = this;

    // Task 1: Fix critical import issues
    const brokenImports = this.findBrokenImports();
    if (brokenImports.length > 0) {
      this.instructions.immediate.push({
        id: 'fix-broken-imports',
        title: 'Fix Broken Import Statements',
        priority: 'critical',
        estimatedTime: '15-30 minutes',
        description: 'Update import statements that reference non-existent files',
        context: 'These imports are causing build errors and need immediate attention',
        files: brokenImports.slice(0, this.maxFilesPerTask),
        instructions: this.generateImportFixInstructions(brokenImports),
        validation: 'Run `yarn build` to verify all imports resolve correctly',
      });
    }

    // Task 2: Create missing index files
    const missingIndexes = this.findMissingIndexFiles();
    if (missingIndexes.length > 0) {
      this.instructions.immediate.push({
        id: 'create-index-files',
        title: 'Create Missing Index Files',
        priority: 'high',
        estimatedTime: '10-20 minutes',
        description: 'Create index.ts files for better import organization',
        context: 'Index files make imports cleaner and easier to manage',
        directories: missingIndexes,
        instructions: this.generateIndexFileInstructions(missingIndexes),
        validation: 'Verify that imports work using the new index files',
      });
    }

    // Task 3: Fix module type inconsistencies
    const moduleIssues = this.findModuleTypeIssues();
    if (moduleIssues.length > 0) {
      this.instructions.immediate.push({
        id: 'fix-module-types',
        title: 'Standardize Module Types (ESM/CommonJS)',
        priority: 'high',
        estimatedTime: '20-30 minutes',
        description: 'Convert mixed module usage to consistent ES modules',
        context: 'Your project uses "type": "module" but some files still use CommonJS',
        files: moduleIssues.slice(0, this.maxFilesPerTask),
        instructions: this.generateModuleFixInstructions(moduleIssues),
        validation: 'Ensure all files use consistent import/export syntax',
      });
    }
  }

  /**
   * Generate quick improvement tasks
   */
  async generateQuickTasks() {
    this.log('🚀 Generating quick tasks...');

    const { analysis } = this;

    // Task 1: Organize scattered utility files
    const scatteredUtils = this.findScatteredUtilities();
    if (scatteredUtils.length > 0) {
      this.instructions.quick.push({
        id: 'organize-utilities',
        title: 'Organize Utility Functions',
        priority: 'medium',
        estimatedTime: '30-45 minutes',
        description: 'Move utility functions to organized folder structure',
        context: 'Utilities are scattered across the codebase and need organization',
        files: scatteredUtils.slice(0, this.maxFilesPerTask),
        instructions: this.generateUtilityOrganizationInstructions(scatteredUtils),
        validation: 'Verify all moved utilities are properly imported',
      });
    }

    // Task 2: Add missing type definitions
    const missingTypes = this.findMissingTypeDefinitions();
    if (missingTypes.length > 0) {
      this.instructions.quick.push({
        id: 'add-type-definitions',
        title: 'Add Missing TypeScript Types',
        priority: 'medium',
        estimatedTime: '20-40 minutes',
        description: 'Add proper TypeScript type definitions',
        context: 'Improve type safety and developer experience',
        files: missingTypes.slice(0, this.maxFilesPerTask),
        instructions: this.generateTypeDefinitionInstructions(missingTypes),
        validation: 'Run `yarn vitest run --reporter=verbose` to check for type errors',
      });
    }

    // Task 3: Create basic test files
    const untestedCritical = this.findUntestedCriticalFiles();
    if (untestedCritical.length > 0) {
      this.instructions.quick.push({
        id: 'create-basic-tests',
        title: 'Create Basic Test Files',
        priority: 'medium',
        estimatedTime: '45-60 minutes',
        description: 'Create basic test files for critical untested components',
        context: 'These critical files lack test coverage',
        files: untestedCritical.slice(0, 3), // Limit to 3 for quick task
        instructions: this.generateBasicTestInstructions(untestedCritical),
        validation: 'Run `yarn test` to verify tests pass',
      });
    }
  }

  /**
   * Generate moderate refactoring tasks
   */
  async generateModerateTasks() {
    this.log('🔧 Generating moderate tasks...');

    const { analysis } = this;

    // Task 1: Split large components
    const largeComponents = this.findLargeComponents();
    if (largeComponents.length > 0) {
      this.instructions.moderate.push({
        id: 'split-large-components',
        title: 'Split Large Components',
        priority: 'medium',
        estimatedTime: '2-3 hours',
        description: 'Break down overly large components into smaller, manageable pieces',
        context: 'Large components are harder to maintain and test',
        files: largeComponents.slice(0, 3),
        instructions: this.generateComponentSplitInstructions(largeComponents),
        validation: 'Ensure split components maintain the same functionality',
      });
    }

    // Task 2: Extract custom hooks
    const hookOpportunities = this.findHookExtractionOpportunities();
    if (hookOpportunities.length > 0) {
      this.instructions.moderate.push({
        id: 'extract-custom-hooks',
        title: 'Extract Custom Hooks',
        priority: 'medium',
        estimatedTime: '1-2 hours',
        description: 'Extract reusable state logic into custom hooks',
        context: 'Components have complex state logic that could be reused',
        files: hookOpportunities.slice(0, this.maxFilesPerTask),
        instructions: this.generateHookExtractionInstructions(hookOpportunities),
        validation: 'Test that extracted hooks work correctly in components',
      });
    }

    // Task 3: Reorganize component structure
    const misorganizedComponents = this.findMisorganizedComponents();
    if (misorganizedComponents.length > 0) {
      this.instructions.moderate.push({
        id: 'reorganize-components',
        title: 'Reorganize Component Structure',
        priority: 'medium',
        estimatedTime: '1-2 hours',
        description: 'Move components to appropriate categorical folders',
        context: 'Components should be organized by type (UI, feature, layout)',
        files: misorganizedComponents.slice(0, this.maxFilesPerTask),
        instructions: this.generateComponentReorganizationInstructions(misorganizedComponents),
        validation: 'Verify all imports still work after reorganization',
      });
    }
  }

  /**
   * Generate comprehensive architecture tasks
   */
  async generateComprehensiveTasks() {
    this.log('🏗️ Generating comprehensive tasks...');

    const { analysis } = this;

    // Task 1: Implement comprehensive testing strategy
    if (analysis.metrics?.testCoverage < 50) {
      this.instructions.comprehensive.push({
        id: 'implement-testing-strategy',
        title: 'Implement Comprehensive Testing Strategy',
        priority: 'medium',
        estimatedTime: '1-2 days',
        description: 'Add comprehensive test coverage for the entire codebase',
        context: `Current test coverage is ${analysis.metrics.testCoverage}%, should be 70%+`,
        files: this.getAllTestableFiles(),
        instructions: this.generateComprehensiveTestingInstructions(),
        validation: 'Achieve 70%+ test coverage across the codebase',
      });
    }

    // Task 2: Implement proper error boundaries and error handling
    const errorHandlingIssues = this.findErrorHandlingIssues();
    if (errorHandlingIssues.length > 0) {
      this.instructions.comprehensive.push({
        id: 'implement-error-handling',
        title: 'Implement Proper Error Handling',
        priority: 'low',
        estimatedTime: '1-2 days',
        description: 'Add error boundaries and comprehensive error handling',
        context: 'Improve user experience and debugging capabilities',
        files: errorHandlingIssues,
        instructions: this.generateErrorHandlingInstructions(errorHandlingIssues),
        validation: 'Test error scenarios and verify graceful handling',
      });
    }

    // Task 3: Performance optimization
    const performanceIssues = this.findPerformanceIssues();
    if (performanceIssues.length > 0) {
      this.instructions.comprehensive.push({
        id: 'optimize-performance',
        title: 'Optimize Component Performance',
        priority: 'low',
        estimatedTime: '2-3 days',
        description: 'Implement performance optimizations (memoization, lazy loading)',
        context: 'Improve app performance and user experience',
        files: performanceIssues,
        instructions: this.generatePerformanceOptimizationInstructions(performanceIssues),
        validation: 'Measure performance improvements with profiling tools',
      });
    }
  }

  /**
   * Analysis helper methods
   */
  findBrokenImports() {
    // Analyze import statements that reference non-existent files
    const brokenImports = [];
    const allFiles = [
      ...(this.analysis.components || []),
      ...(this.analysis.hooks || []),
      ...(this.analysis.utilities || []),
      ...(this.analysis.services || []),
    ];

    for (const file of allFiles) {
      const imports = file.imports || [];
      for (const imp of imports) {
        if (imp.isRelative && imp.path.includes('../')) {
          // These might be broken after reorganization
          brokenImports.push({
            file: file.path,
            import: imp.path,
            reason: 'Relative import that might be broken',
          });
        }
      }
    }

    return brokenImports;
  }

  findMissingIndexFiles() {
    const directories = new Set();
    const allFiles = [
      ...(this.analysis.components || []),
      ...(this.analysis.hooks || []),
      ...(this.analysis.utilities || []),
    ];

    // Find directories that should have index files
    for (const file of allFiles) {
      const dir = path.dirname(file.path);
      if (dir.includes('components') || dir.includes('utils') || dir.includes('hooks')) {
        directories.add(dir);
      }
    }

    return Array.from(directories);
  }

  findModuleTypeIssues() {
    const issues = [];
    const allFiles = [...(this.analysis.components || []), ...(this.analysis.utilities || [])];

    for (const file of allFiles) {
      const imports = file.imports || [];
      const hasCJS = imports.some((imp) => imp.type === 'cjs');
      const hasESM = imports.some((imp) => imp.type === 'esm');

      if (hasCJS || (file.exports && file.exports.some((exp) => exp.module === 'cjs'))) {
        issues.push({
          file: file.path,
          issue: 'Uses CommonJS syntax in ES module project',
          hasESM,
          hasCJS,
        });
      }
    }

    return issues;
  }

  findScatteredUtilities() {
    return (this.analysis.utilities || []).filter(
      (util) =>
        !util.path.includes('/utils/') &&
        !util.path.includes('/lib/') &&
        !util.path.includes('/helpers/')
    );
  }

  findMissingTypeDefinitions() {
    return (this.analysis.components || []).filter(
      (comp) => comp.path.endsWith('.js') || comp.path.endsWith('.jsx')
    );
  }

  findUntestedCriticalFiles() {
    const allFiles = [
      ...(this.analysis.components || []),
      ...(this.analysis.hooks || []),
      ...(this.analysis.services || []),
    ];

    const testFiles = (this.analysis.tests || []).map((t) => t.path);

    return allFiles.filter((file) => {
      const testPattern = file.path.replace(/\.(ts|tsx|js|jsx)$/, '');
      const hasTest = testFiles.some(
        (testFile) =>
          testFile.includes(testPattern) || testFile.includes(path.basename(testPattern))
      );

      return !hasTest && (file.complexity > 10 || file.lines > 100);
    });
  }

  findLargeComponents() {
    return (this.analysis.components || []).filter(
      (comp) => comp.lines > 200 || comp.complexity > 15
    );
  }

  findHookExtractionOpportunities() {
    return (this.analysis.components || []).filter(
      (comp) =>
        comp.complexity > 12 &&
        comp.lines > 150 &&
        // Look for useState/useEffect patterns in file content
        comp.path.endsWith('.tsx')
    );
  }

  findMisorganizedComponents() {
    return (this.analysis.components || []).filter((comp) => {
      const path = comp.path.toLowerCase();
      // Components not in organized folders
      return (
        !path.includes('/ui/') &&
        !path.includes('/feature/') &&
        !path.includes('/layout/') &&
        !path.includes('/pages/')
      );
    });
  }

  getAllTestableFiles() {
    return [
      ...(this.analysis.components || []),
      ...(this.analysis.hooks || []),
      ...(this.analysis.utilities || []),
    ].slice(0, 20); // Limit for comprehensive task
  }

  findErrorHandlingIssues() {
    return (this.analysis.components || []).filter(
      (comp) => comp.lines > 100 && !comp.path.includes('ErrorBoundary')
    );
  }

  findPerformanceIssues() {
    return (this.analysis.components || []).filter(
      (comp) => comp.complexity > 10 || comp.lines > 150
    );
  }

  /**
   * Instruction generators
   */
  generateImportFixInstructions(brokenImports) {
    return `
## Fix Broken Import Statements

### Context
These import statements may be referencing files that have moved or don't exist.

### Instructions for Augment AI:

For each file with broken imports:

1. **Analyze the import statement**:
   - Check if the referenced file exists at the specified path
   - If not, search for the file in the current codebase
   - Determine the correct relative path

2. **Update the import**:
   - Replace the old path with the correct path
   - Ensure the import syntax is correct (ESM format)
   - Maintain any named imports or aliases

3. **Verify the fix**:
   - Check that the imported item is actually exported from the target file
   - Update export statements if necessary

### Example:
\`\`\`typescript
// Before (broken)
import { someUtil } from '../../../utils/someUtil';

// After (fixed)
import { someUtil } from '@/utils/helpers/someUtil';
\`\`\`

### Files to fix:
${brokenImports
  .map((item) => `- **${item.file}**: Fix import \`${item.import}\` (${item.reason})`)
  .join('\n')}
`;
  }

  generateIndexFileInstructions(missingIndexes) {
    return `
## Create Missing Index Files

### Context
Index files make imports cleaner and provide a single entry point for each module.

### Instructions for Augment AI:

For each directory that needs an index file:

1. **Analyze the directory contents**:
   - List all exportable files (components, hooks, utilities)
   - Identify what should be exported from each file

2. **Create index.ts file**:
   - Add exports for all public components/functions
   - Use named exports where possible
   - Group related exports with comments

3. **Update existing imports**:
   - Find files that import from this directory
   - Update them to use the new index file

### Example index.ts:
\`\`\`typescript
/**
 * UI Components
 */
export { Button } from './Button';
export { Input } from './Input';
export { Modal } from './Modal';

/**
 * Form Components
 */
export { FormField } from './FormField';
export { FormValidation } from './FormValidation';
\`\`\`

### Directories needing index files:
${missingIndexes.map((dir) => `- **${dir}**`).join('\n')}
`;
  }

  generateModuleFixInstructions(moduleIssues) {
    return `
## Standardize Module Types

### Context
Your project uses \`"type": "module"\` but some files still use CommonJS syntax.

### Instructions for Augment AI:

For each file with module type issues:

1. **Convert CommonJS to ES modules**:
   - Replace \`require()\` with \`import\`
   - Replace \`module.exports\` with \`export\`
   - Replace \`exports.foo\` with \`export const foo\`

2. **Update import syntax**:
   - Convert: \`const foo = require('bar')\` → \`import foo from 'bar'\`
   - Convert: \`const { a, b } = require('bar')\` → \`import { a, b } from 'bar'\`

3. **Update export syntax**:
   - Convert: \`module.exports = foo\` → \`export default foo\`
   - Convert: \`exports.foo = bar\` → \`export const foo = bar\`

### Example conversion:
\`\`\`typescript
// Before (CommonJS)
const fs = require('fs');
const { glob } = require('glob');

function analyze() {
  // ...
}

module.exports = analyze;

// After (ES Module)
import fs from 'fs';
import { glob } from 'glob';

function analyze() {
  // ...
}

export default analyze;
\`\`\`

### Files to convert:
${moduleIssues.map((issue) => `- **${issue.file}**: ${issue.issue}`).join('\n')}
`;
  }

  generateUtilityOrganizationInstructions(scatteredUtils) {
    return `
## Organize Utility Functions

### Context
Utility functions are scattered across the codebase and should be organized by category.

### Instructions for Augment AI:

1. **Analyze each utility file**:
   - Determine the category (formatting, validation, calculations, api, etc.)
   - Check dependencies and exports

2. **Create organized structure**:
   - Move files to \`src/utils/[category]/\`
   - Categories: helpers, formatting, validation, calculations, api, constants

3. **Update imports**:
   - Find all files that import these utilities
   - Update import paths to new locations
   - Use relative paths or alias paths consistently

4. **Create category index files**:
   - Each category should have an index.ts
   - Export all utilities from that category

### Suggested organization:
\`\`\`
src/utils/
├── formatting/
│   ├── currency.ts
│   ├── date.ts
│   └── index.ts
├── validation/
│   ├── schema.ts
│   ├── forms.ts
│   └── index.ts
└── calculations/
    ├── trading.ts
    └── index.ts
\`\`\`

### Files to organize:
${scatteredUtils.map((util) => `- **${util.path}**: Move to appropriate utils category`).join('\n')}
`;
  }

  generateBasicTestInstructions(untestedFiles) {
    return `
## Create Basic Test Files

### Context
These critical files lack test coverage and need basic tests.

### Instructions for Augment AI:

For each untested file:

1. **Create test file**:
   - Name: \`[filename].test.ts\` or place in \`__tests__/\` folder
   - Use Vitest framework (your project uses Vite)

2. **Write basic tests**:
   - Test component rendering (for React components)
   - Test function exports (for utilities)
   - Test happy path scenarios
   - Test error cases

3. **Test structure**:
   - Use describe blocks for organization
   - Include setup/cleanup if needed
   - Mock external dependencies

### Example component test:
\`\`\`typescript
import { render, screen } from '@testing-library/react';
import { ComponentName } from './ComponentName';

describe('ComponentName', () => {
  it('renders without crashing', () => {
    render(<ComponentName />);
    expect(screen.getByRole('...')).toBeInTheDocument();
  });

  it('handles props correctly', () => {
    render(<ComponentName prop="value" />);
    expect(screen.getByText('value')).toBeInTheDocument();
  });
});
\`\`\`

### Files needing tests:
${untestedFiles
  .slice(0, 5)
  .map((file) => `- **${file.path}**: ${file.lines} lines, complexity ${file.complexity}`)
  .join('\n')}
`;
  }

  generateComponentSplitInstructions(largeComponents) {
    return `
## Split Large Components

### Context
These components are too large and should be broken down for better maintainability.

### Instructions for Augment AI:

For each large component:

1. **Analyze the component structure**:
   - Identify distinct responsibilities
   - Look for reusable UI patterns
   - Find state management that could be extracted

2. **Create split strategy**:
   - **UI Components**: Extract presentational parts
   - **Container Components**: Keep business logic
   - **Custom Hooks**: Extract state management

3. **Implementation approach**:
   - Start with extracting smallest reusable pieces
   - Create new files in appropriate folders (ui/, feature/, hooks/)
   - Update imports and exports
   - Maintain same functionality

### Example split:
\`\`\`
// Before: LargeTradingDashboard.tsx (400 lines)

// After:
TradingDashboardContainer.tsx (business logic)
├── TradingDashboardView.tsx (presentation)
├── TradeList.tsx (reusable component)
├── TradeFilters.tsx (reusable component)
└── useTradingData.ts (custom hook)
\`\`\`

### Components to split:
${largeComponents
  .slice(0, 3)
  .map((comp) => `- **${comp.path}**: ${comp.lines} lines, complexity ${comp.complexity}`)
  .join('\n')}
`;
  }

  generateHookExtractionInstructions(hookOpportunities) {
    return `
## Extract Custom Hooks

### Context
These components have complex state logic that could be extracted into reusable hooks.

### Instructions for Augment AI:

For each component:

1. **Identify extractable logic**:
   - State management patterns
   - Side effects (useEffect)
   - Complex calculations
   - API calls

2. **Create custom hook**:
   - Name with \`use\` prefix
   - Return object with state and actions
   - Include proper TypeScript types

3. **Update component**:
   - Replace inline logic with hook usage
   - Maintain same functionality
   - Improve readability

### Example extraction:
\`\`\`typescript
// Before (in component):
const [data, setData] = useState([]);
const [loading, setLoading] = useState(false);
useEffect(() => {
  // complex data fetching logic
}, []);

// After (custom hook):
// useTradingData.ts
export const useTradingData = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  
  // extracted logic
  
  return { data, loading, refetch };
};

// In component:
const { data, loading, refetch } = useTradingData();
\`\`\`

### Components for hook extraction:
${hookOpportunities
  .slice(0, 5)
  .map((comp) => `- **${comp.path}**: Complex state logic to extract`)
  .join('\n')}
`;
  }

  generateComponentReorganizationInstructions(misorganizedComponents) {
    return `
## Reorganize Component Structure

### Context
Components should be organized by their type and responsibility.

### Instructions for Augment AI:

1. **Categorize each component**:
   - **UI**: Reusable, presentational components (buttons, inputs, cards)
   - **Feature**: Business logic components tied to specific features
   - **Layout**: App structure (headers, sidebars, navigation)
   - **Pages**: Top-level route components

2. **Move to appropriate folders**:
   - \`src/components/ui/\` - Basic reusable components
   - \`src/components/feature/\` - Feature-specific components
   - \`src/components/layout/\` - Layout and navigation
   - \`src/pages/\` - Page components

3. **Update all imports**:
   - Find all files that import these components
   - Update import paths to new locations
   - Consider using path aliases (@/components/ui/Button)

4. **Create/update index files**:
   - Each category folder should export its components
   - Main components/index.ts should export all categories

### Organization example:
\`\`\`
src/
├── components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── index.ts
│   ├── feature/
│   │   ├── TradingDashboard.tsx
│   │   ├── TradeForm.tsx
│   │   └── index.ts
│   └── layout/
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── index.ts
└── pages/
    ├── Dashboard.tsx
    └── Settings.tsx
\`\`\`

### Components to reorganize:
${misorganizedComponents
  .slice(0, 8)
  .map((comp) => `- **${comp.path}**: Move to appropriate category folder`)
  .join('\n')}
`;
  }

  generateComprehensiveTestingInstructions() {
    return `
## Implement Comprehensive Testing Strategy

### Context
Current test coverage is low and needs significant improvement.

### Instructions for Augment AI:

1. **Set up testing infrastructure**:
   - Ensure Vitest is properly configured
   - Set up testing utilities and helpers
   - Configure coverage reporting

2. **Create test categories**:
   - **Unit tests**: Individual functions and hooks
   - **Component tests**: React component behavior
   - **Integration tests**: Feature workflows

3. **Testing priorities** (in order):
   - Critical business logic functions
   - Custom hooks
   - Core UI components
   - Feature components
   - Utility functions

4. **Test patterns to implement**:
   - Render testing for components
   - User interaction testing
   - Error boundary testing
   - Hook testing with renderHook
   - API mocking for services

### Coverage goals:
- Utilities: 90%+
- Hooks: 85%+
- Components: 75%+
- Overall: 70%+

Focus on testing actual user behavior and business logic rather than implementation details.
`;
  }

  generateErrorHandlingInstructions(errorFiles) {
    return `
## Implement Proper Error Handling

### Context
Components need better error handling and user feedback.

### Instructions for Augment AI:

1. **Add Error Boundaries**:
   - Create reusable ErrorBoundary component
   - Wrap feature components with error boundaries
   - Provide fallback UI for errors

2. **Improve component error handling**:
   - Add try-catch blocks for async operations
   - Show user-friendly error messages
   - Log errors for debugging

3. **Add loading states**:
   - Show loading indicators during async operations
   - Prevent multiple submissions
   - Handle network errors gracefully

### Example implementation:
\`\`\`typescript
// ErrorBoundary.tsx
class ErrorBoundary extends React.Component {
  // Error boundary implementation
}

// In components:
try {
  await apiCall();
} catch (error) {
  setError('Something went wrong. Please try again.');
  console.error('API Error:', error);
}
\`\`\`
`;
  }

  generatePerformanceOptimizationInstructions(performanceFiles) {
    return `
## Optimize Component Performance

### Context
These components could benefit from performance optimizations.

### Instructions for Augment AI:

1. **Add React.memo** for expensive components
2. **Use useMemo** for expensive calculations
3. **Use useCallback** for event handlers passed to children
4. **Implement lazy loading** for large components
5. **Optimize re-renders** by moving state closer to usage

### Performance patterns:
\`\`\`typescript
// Memoization
const ExpensiveComponent = React.memo(({ data }) => {
  const expensiveValue = useMemo(() => {
    return heavyCalculation(data);
  }, [data]);
  
  const handleClick = useCallback((id) => {
    // handle click
  }, []);
  
  return <div>{expensiveValue}</div>;
});

// Lazy loading
const LazyComponent = lazy(() => import('./HeavyComponent'));
\`\`\`
`;
  }

  /**
   * Output all instructions
   */
  async outputInstructions() {
    this.log('📁 Creating output directory...');

    const outputPath = path.join(this.rootDir, this.outputDir);
    await fs.mkdir(outputPath, { recursive: true });

    // Output each category
    for (const [category, tasks] of Object.entries(this.instructions)) {
      if (tasks.length === 0) continue;

      const categoryPath = path.join(outputPath, `${category}-tasks.md`);
      const content = this.formatCategoryInstructions(category, tasks);

      await fs.writeFile(categoryPath, content);
      this.log(`📄 Created ${category} tasks: ${categoryPath}`);
    }

    // Output individual task files for easy AI consumption
    await this.outputIndividualTasks();
  }

  async outputIndividualTasks() {
    const tasksPath = path.join(this.rootDir, this.outputDir, 'individual-tasks');
    await fs.mkdir(tasksPath, { recursive: true });

    const allTasks = [
      ...this.instructions.immediate,
      ...this.instructions.quick,
      ...this.instructions.moderate,
      ...this.instructions.comprehensive,
    ];

    for (const task of allTasks) {
      const taskPath = path.join(tasksPath, `${task.id}.md`);
      const content = this.formatIndividualTask(task);

      await fs.writeFile(taskPath, content);
    }

    this.log(`📁 Created ${allTasks.length} individual task files`);
  }

  formatCategoryInstructions(category, tasks) {
    const categoryTitles = {
      immediate: 'Immediate Tasks (High Impact, Quick Fixes)',
      quick: 'Quick Improvements (30-60 minutes)',
      moderate: 'Moderate Refactoring (1-3 hours)',
      comprehensive: 'Comprehensive Architecture (1+ days)',
    };

    return `# ${categoryTitles[category]}

${tasks
  .map(
    (task) => `
## ${task.title}

**Priority**: ${task.priority}  
**Estimated Time**: ${task.estimatedTime}  
**ID**: \`${task.id}\`

### Description
${task.description}

### Context
${task.context}

${
  task.files
    ? `### Files Affected
${task.files.map((f) => `- ${typeof f === 'string' ? f : f.path || f.file}`).join('\n')}
`
    : ''
}

${
  task.directories
    ? `### Directories
${task.directories.map((d) => `- ${d}`).join('\n')}
`
    : ''
}

### Instructions
${task.instructions}

### Validation
${task.validation}

---
`
  )
  .join('\n')}`;
  }

  formatIndividualTask(task) {
    return `# ${task.title}

**ID**: \`${task.id}\`  
**Priority**: ${task.priority}  
**Estimated Time**: ${task.estimatedTime}

## Context
${task.context}

## Description  
${task.description}

${
  task.files
    ? `## Files to Modify
${task.files.map((f) => `- ${typeof f === 'string' ? f : f.path || f.file}`).join('\n')}
`
    : ''
}

${
  task.directories
    ? `## Directories
${task.directories.map((d) => `- ${d}`).join('\n')}
`
    : ''
}

## Instructions for AI
${task.instructions}

## Validation
${task.validation}

## Notes
- This is an automatically generated task based on codebase analysis
- Maintain existing functionality while implementing improvements
- Test thoroughly after making changes
- Update related documentation if necessary
`;
  }

  /**
   * Generate summary report
   */
  async generateSummary() {
    const allTasks = [
      ...this.instructions.immediate,
      ...this.instructions.quick,
      ...this.instructions.moderate,
      ...this.instructions.comprehensive,
    ];

    const summary = `# Augment AI Task Summary

Generated: ${new Date().toISOString()}

## Overview
Based on the architecture analysis, here are the prioritized tasks for improving your codebase:

## Task Categories

### ⚡ Immediate Tasks (${this.instructions.immediate.length})
Critical issues that should be fixed immediately:
${this.instructions.immediate.map((t) => `- **${t.title}** (${t.estimatedTime})`).join('\n')}

### 🚀 Quick Tasks (${this.instructions.quick.length})
Quick wins that provide good value:
${this.instructions.quick.map((t) => `- **${t.title}** (${t.estimatedTime})`).join('\n')}

### 🔧 Moderate Tasks (${this.instructions.moderate.length})
Refactoring that improves maintainability:
${this.instructions.moderate.map((t) => `- **${t.title}** (${t.estimatedTime})`).join('\n')}

### 🏗️ Comprehensive Tasks (${this.instructions.comprehensive.length})
Long-term architectural improvements:
${this.instructions.comprehensive.map((t) => `- **${t.title}** (${t.estimatedTime})`).join('\n')}

## Recommended Execution Order

1. **Start with Immediate tasks** - Fix critical issues first
2. **Pick 2-3 Quick tasks** - Get some quick wins
3. **Choose 1 Moderate task** - Improve architecture gradually  
4. **Plan Comprehensive tasks** - Schedule for future sprints

## Files and Folders Created

- \`${this.outputDir}/immediate-tasks.md\` - Critical fixes
- \`${this.outputDir}/quick-tasks.md\` - Quick improvements
- \`${this.outputDir}/moderate-tasks.md\` - Refactoring tasks
- \`${this.outputDir}/comprehensive-tasks.md\` - Architecture improvements
- \`${this.outputDir}/individual-tasks/\` - Individual task files for AI

## Usage with Augment AI

1. **Choose a task** from the appropriate category
2. **Open the individual task file** in \`individual-tasks/[task-id].md\`
3. **Share the task file** with Augment AI
4. **Let Augment implement** the specific changes
5. **Validate the results** using the provided validation steps

## Total Effort Estimate
- **Immediate**: ${this.instructions.immediate.length * 20} minutes
- **Quick**: ${this.instructions.quick.length * 45} minutes  
- **Moderate**: ${this.instructions.moderate.length * 2} hours
- **Comprehensive**: ${this.instructions.comprehensive.length * 16} hours

Focus on immediate and quick tasks first for maximum impact with minimal effort.
`;

    const summaryPath = path.join(this.rootDir, this.outputDir, 'TASK_SUMMARY.md');
    await fs.writeFile(summaryPath, summary);

    this.log(`📋 Generated summary: ${summaryPath}`);
    this.log(`📊 Total tasks generated: ${allTasks.length}`);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const options = {
    rootDir: args.find((arg) => arg.startsWith('--root='))?.split('=')[1] || process.cwd(),
    analysisFile:
      args.find((arg) => arg.startsWith('--analysis='))?.split('=')[1] ||
      'architecture-analysis.json',
    outputDir:
      args.find((arg) => arg.startsWith('--output='))?.split('=')[1] || 'augment-instructions',
    maxFilesPerTask:
      parseInt(args.find((arg) => arg.startsWith('--max-files='))?.split('=')[1]) || 5,
  };

  console.log(`
🤖 Augment AI Instruction Generator

Generating AI-friendly instructions from architecture analysis...
Input: ${options.analysisFile}
Output: ${options.outputDir}/
  `);

  const generator = new AugmentInstructionGenerator(options);
  generator.generate().catch((error) => {
    console.error('❌ Instruction generation failed:', error.message);
    process.exit(1);
  });
}

export default AugmentInstructionGenerator;
