🏗️  ARCHITECTURAL IMPROVEMENT PLANNER
📁 Analyzing: /Users/<USER>/adhd-trading-dashboard-lib
================================================================================
📊 Scanned 283 files
🔗 Building dependency graph...
📁 Creating folder restructure plan...
✂️  Creating component split plan...
🪝 Creating hook extraction plan...
🔧 Creating service extraction plan...
🧪 Creating testing plan...
🚀 Creating migration plan...

🎯 ARCHITECTURAL IMPROVEMENT REPORT
================================================================================

📋 EXECUTIVE SUMMARY
   • 37 components need splitting
   • 1 custom hooks can be extracted
   • 0 service layers can be created
   • 151 files need tests
   • 6 migration phases planned

📁 PROPOSED FOLDER STRUCTURE

src/components/ui/
   Purpose: Basic, reusable UI components (buttons, inputs, etc.)
   Files to move: 38
   Examples: App.tsx, MinimalApp.tsx, SimpleApp.tsx

src/components/feature/
   Purpose: Components tied to specific business features
   Files to move: 32
   Examples: TestApp.tsx, TradingPlan.tsx, DailyGuideContext.tsx

src/components/layout/
   Purpose: App layout and navigation components
   Files to move: 7
   Examples: TradeFormHeader.tsx, TradeJournalHeader.tsx, TradeListHeader.tsx

src/pages/
   Purpose: Top-level page components
   Files to move: 5
   Examples: DailyGuide.tsx, NotFound.tsx, Settings.tsx

src/hooks/
   Purpose: Reusable custom hooks
   Files to move: 26
   Examples: architectural-improvement-planner.js, codebase-analyzer.js, DailyGuideContext.tsx

src/services/
   Purpose: API clients and external service integrations
   Files to move: 2
   Examples: dailyGuideApi.ts, tradeAnalysisApi.ts

src/utils/
   Purpose: Pure utility functions and helpers
   Files to move: 109
   Examples: .eslintrc.js, fileMock.js, styleMock.js

src/types/
   Purpose: Shared TypeScript type definitions
   Files to move: 6
   Examples: types.ts, types.ts, react-types.d.ts

✂️  COMPONENT SPLIT RECOMMENDATIONS

1. packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx
   Reason: Too large (432 lines, complexity 1)
   Current responsibilities: 
   Proposed splits:
   Files that import this: 0

2. packages/dashboard/src/components/molecules/ProfitLossCell.tsx
   Reason: Too complex (210 lines, complexity 48)
   Current responsibilities: UI Rendering
   Proposed splits:
   Files that import this: 1

3. packages/dashboard/src/features/daily-guide/components/KeyLevels.tsx
   Reason: Too complex (201 lines, complexity 14)
   Current responsibilities: Event Handling, UI Rendering
   Proposed splits:
   Files that import this: 2

4. packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx
   Reason: Too large (371 lines, complexity 27)
   Current responsibilities: State Management, Event Handling, UI Rendering
   Proposed splits:
     → TradingPlanContainer: Handle state management and business logic
       New path: src/components/feature/TradingPlanContainer.tsx
     → TradingPlanView: Handle pure UI rendering
       New path: src/components/ui/TradingPlanView.tsx
     → useTradingPlan: Extract state management logic
       New path: src/hooks/useTradingPlan.ts
   Files that import this: 2

5. packages/dashboard/src/features/settings/Settings.tsx
   Reason: Too complex (292 lines, complexity 1)
   Current responsibilities: Event Handling, UI Rendering
   Proposed splits:
   Files that import this: 0

🪝 CUSTOM HOOK EXTRACTION OPPORTUNITIES

1. Extract useTradingDashboardData
   From: packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx
   New location: src/hooks/useTradingDashboardData.ts
   Logic to extract: State Management, Data Fetching, Side Effects
   Benefits: Reusability, Testability, Separation of Concerns

🧪 TESTING STRATEGY
Total untested files: 151
Critical priority: 32 files
High priority: 30 files
Medium priority: 93 files

Critical files needing tests:
   architectural-improvement-planner.js (complexity: 124)
   codebase-analyzer.js (complexity: 185)
   packages/dashboard/src/TestApp.tsx (complexity: 3)
   packages/dashboard/src/components/molecules/ProfitLossCell.tsx (complexity: 48)
   packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx (complexity: 27)

🚀 MIGRATION ROADMAP

Phase 1: Create New Folder Structure
   Description: Set up the new architectural folders
   Risk Level: Low
   Estimated Time: 30 minutes
   Actions: 8 items

Phase 2: Move Utility Functions
   Description: Move pure utility functions to new structure
   Risk Level: Low
   Estimated Time: 2-4 hours
   Actions: 109 items

Phase 3: Extract and Move Custom Hooks
   Description: Create custom hooks and move existing ones
   Risk Level: Medium
   Estimated Time: 1-2 days
   Actions: 1 items
   Dependencies: 1 import updates needed

Phase 4: Split Large Components
   Description: Break down overly complex components
   Risk Level: High
   Estimated Time: 3-5 days
   Actions: 37 items
   Dependencies: 37 import updates needed

Phase 5: Reorganize Components
   Description: Move components to their appropriate folders
   Risk Level: Medium
   Estimated Time: 1-2 days
   Actions: 85 items
   Dependencies: 171 import updates needed

Phase 6: Add Missing Tests
   Description: Create tests for critical components
   Risk Level: Low
   Estimated Time: 2-3 days
   Actions: 32 items

⚠️  BREAKING CHANGES ANALYSIS
   • 440 import statements will need updating
   • Recommended approach: Use automated refactoring tools
   • Suggested tools: jscodeshift, ts-morph, or IDE refactoring

🛠️  IMPLEMENTATION HELPERS

To start the migration, run these commands:
```bash
# Phase 1: Create folder structure
mkdir -p src/components/ui
mkdir -p src/components/feature
mkdir -p src/components/layout
mkdir -p src/pages
mkdir -p src/hooks
mkdir -p src/services
mkdir -p src/utils
mkdir -p src/types

# Phase 2: Move utility files (safest first)
git mv .eslintrc.js src/utils/.eslintrc.js
git mv __mocks__/fileMock.js src/utils/fileMock.js
git mv __mocks__/styleMock.js src/utils/styleMock.js
```

🎯 IMMEDIATE NEXT STEPS
   1. Review and approve the proposed folder structure
   2. Start with Phase 1 (low-risk folder creation)
   3. Create a feature branch for the migration
   4. Begin with utility file moves (lowest risk)
   5. Set up automated testing for each phase
   6. Consider using codemods for import updates

🔄 ROLLBACK STRATEGY
   • Each phase is designed to be independently reversible
   • Keep detailed logs of all file moves and changes
   • Use git branches for each migration phase
   • Test thoroughly before proceeding to next phase
