#!/usr/bin/env node

/**
 * Component Reusability & Architecture Analyzer
 * 
 * Analyzes components for:
 * 1. Reusability patterns and over-responsibility
 * 2. Data flow patterns and prop drilling
 * 3. Large files with many imports (doing multiple jobs)
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComponentReusabilityAnalyzer {
  constructor() {
    this.fileMap = new Map();
    this.componentAnalysis = new Map();
    this.dataFlowPatterns = [];
    this.oversizedComponents = [];
    this.reusabilityIssues = [];
  }

  async analyze() {
    console.log('🔍 Analyzing component reusability and architecture patterns...\n');
    
    await this.scanSourceFiles();
    this.analyzeComponentReusability();
    this.analyzeDataFlowPatterns();
    this.identifyOversizedComponents();
    this.generateReport();
  }

  async scanSourceFiles() {
    const sourceDirectories = [
      'packages/dashboard/src',
      'packages/shared/src'
    ];

    for (const dir of sourceDirectories) {
      if (fs.existsSync(dir)) {
        await this.scanDirectory(dir);
      }
    }
  }

  async scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
          await this.scanDirectory(fullPath);
        }
      } else if (this.isSourceFile(item)) {
        await this.analyzeFile(fullPath);
      }
    }
  }

  isSourceFile(filename) {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    return extensions.some(ext => filename.endsWith(ext)) && 
           !filename.includes('.test.') && 
           !filename.includes('.spec.') &&
           !filename.includes('.d.ts');
  }

  async analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);
      const lines = content.split('\n');
      
      const analysis = {
        path: relativePath,
        lineCount: lines.length,
        imports: this.extractImports(content),
        exports: this.extractExports(content),
        props: this.extractProps(content),
        hooks: this.extractHooks(content),
        stateManagement: this.analyzeStateManagement(content),
        responsibilities: this.identifyResponsibilities(content),
        isComponent: this.isComponent(content, filePath),
        isHook: this.isHook(filePath),
        complexity: this.calculateComplexity(content),
        propDrilling: this.detectPropDrilling(content),
        sideEffects: this.detectSideEffects(content)
      };
      
      this.fileMap.set(relativePath, analysis);
      
      if (analysis.isComponent) {
        this.componentAnalysis.set(relativePath, analysis);
      }
    } catch (error) {
      console.error(`Error analyzing ${filePath}:`, error.message);
    }
  }

  extractImports(content) {
    const imports = [];
    const importPatterns = [
      /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g,
    ];
    
    for (const pattern of importPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const importPath = match[1];
        if (this.isInternalImport(importPath)) {
          imports.push({
            path: importPath,
            statement: match[0],
            isRelative: importPath.startsWith('.'),
            isShared: importPath.includes('@adhd-trading-dashboard/shared')
          });
        }
      }
    }
    
    return imports;
  }

  extractExports(content) {
    const exports = [];
    const exportPatterns = [
      /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g,
      /export\s+\{([^}]+)\}/g,
    ];
    
    for (const pattern of exportPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        exports.push({
          statement: match[0],
          name: match[1] || 'default'
        });
      }
    }
    
    return exports;
  }

  extractProps(content) {
    const props = [];
    
    // Extract interface props
    const interfacePattern = /interface\s+\w*Props\s*\{([^}]+)\}/g;
    let match;
    while ((match = interfacePattern.exec(content)) !== null) {
      const propsContent = match[1];
      const propLines = propsContent.split('\n').filter(line => line.trim());
      props.push(...propLines.map(line => line.trim()));
    }
    
    // Extract destructured props
    const destructurePattern = /\{\s*([^}]+)\s*\}:\s*\w*Props/g;
    while ((match = destructurePattern.exec(content)) !== null) {
      const destructuredProps = match[1].split(',').map(p => p.trim());
      props.push(...destructuredProps);
    }
    
    return props;
  }

  extractHooks(content) {
    const hooks = [];
    const hookPattern = /use\w+\(/g;
    let match;
    while ((match = hookPattern.exec(content)) !== null) {
      hooks.push(match[0].slice(0, -1)); // Remove the opening parenthesis
    }
    return [...new Set(hooks)]; // Remove duplicates
  }

  analyzeStateManagement(content) {
    return {
      hasUseState: content.includes('useState'),
      hasUseEffect: content.includes('useEffect'),
      hasUseContext: content.includes('useContext'),
      hasCustomHooks: /use[A-Z]\w+/.test(content),
      hasRedux: content.includes('useSelector') || content.includes('useDispatch'),
      stateComplexity: (content.match(/useState/g) || []).length + 
                      (content.match(/useEffect/g) || []).length
    };
  }

  identifyResponsibilities(content) {
    const responsibilities = [];
    
    // Data fetching
    if (content.includes('fetch') || content.includes('axios') || content.includes('api')) {
      responsibilities.push('data-fetching');
    }
    
    // Form handling
    if (content.includes('onSubmit') || content.includes('formData') || content.includes('validation')) {
      responsibilities.push('form-handling');
    }
    
    // Routing
    if (content.includes('useNavigate') || content.includes('useRouter') || content.includes('Link')) {
      responsibilities.push('routing');
    }
    
    // UI rendering
    if (content.includes('return') && content.includes('jsx')) {
      responsibilities.push('ui-rendering');
    }
    
    // State management
    if (content.includes('useState') || content.includes('useReducer')) {
      responsibilities.push('state-management');
    }
    
    // Event handling
    if (content.includes('onClick') || content.includes('onChange') || content.includes('onSubmit')) {
      responsibilities.push('event-handling');
    }
    
    // Business logic
    if (content.includes('calculate') || content.includes('transform') || content.includes('process')) {
      responsibilities.push('business-logic');
    }
    
    return responsibilities;
  }

  isComponent(content, filePath) {
    return (filePath.includes('components') || 
            filePath.endsWith('.tsx') ||
            content.includes('React.FC') ||
            content.includes('function') && content.includes('return') && content.includes('jsx')) &&
           !filePath.includes('hooks') &&
           !filePath.includes('utils');
  }

  isHook(filePath) {
    return filePath.includes('hooks') || 
           path.basename(filePath).startsWith('use');
  }

  calculateComplexity(content) {
    const lines = content.split('\n');
    const complexityIndicators = {
      conditionals: (content.match(/if\s*\(|switch\s*\(|\?\s*:/g) || []).length,
      loops: (content.match(/for\s*\(|while\s*\(|\.map\(|\.forEach\(/g) || []).length,
      functions: (content.match(/function\s+\w+|=>\s*\{|const\s+\w+\s*=/g) || []).length,
      nestedBlocks: this.countNestedBlocks(content),
      imports: (content.match(/^import/gm) || []).length
    };
    
    return {
      ...complexityIndicators,
      total: Object.values(complexityIndicators).reduce((sum, val) => sum + val, 0)
    };
  }

  countNestedBlocks(content) {
    let maxNesting = 0;
    let currentNesting = 0;
    
    for (const char of content) {
      if (char === '{') {
        currentNesting++;
        maxNesting = Math.max(maxNesting, currentNesting);
      } else if (char === '}') {
        currentNesting--;
      }
    }
    
    return maxNesting;
  }

  detectPropDrilling(content) {
    // Look for patterns where props are passed through without being used
    const propPassingPattern = /(\w+)=\{(\w+)\}/g;
    const propDrillingIndicators = [];
    
    let match;
    while ((match = propPassingPattern.exec(content)) !== null) {
      const propName = match[1];
      const propValue = match[2];
      
      // If prop name matches prop value, it might be prop drilling
      if (propName === propValue) {
        propDrillingIndicators.push(propName);
      }
    }
    
    return {
      hasPropDrilling: propDrillingIndicators.length > 0,
      drilledProps: propDrillingIndicators,
      severity: propDrillingIndicators.length > 3 ? 'high' : 
                propDrillingIndicators.length > 1 ? 'medium' : 'low'
    };
  }

  detectSideEffects(content) {
    return {
      hasApiCalls: content.includes('fetch') || content.includes('axios'),
      hasLocalStorage: content.includes('localStorage') || content.includes('sessionStorage'),
      hasConsoleLog: content.includes('console.log'),
      hasWindowAccess: content.includes('window.'),
      hasDocumentAccess: content.includes('document.'),
      effectCount: (content.match(/useEffect/g) || []).length
    };
  }

  isInternalImport(importPath) {
    return importPath.startsWith('.') || 
           importPath.startsWith('@adhd-trading-dashboard') ||
           importPath.startsWith('/');
  }

  analyzeComponentReusability() {
    console.log('📊 Analyzing component reusability patterns...\n');
    
    for (const [filePath, analysis] of this.componentAnalysis) {
      // Check for over-responsibility
      if (analysis.responsibilities.length > 4) {
        this.reusabilityIssues.push({
          type: 'over-responsibility',
          file: filePath,
          responsibilities: analysis.responsibilities,
          severity: analysis.responsibilities.length > 6 ? 'high' : 'medium',
          suggestion: 'Split into smaller, focused components'
        });
      }
      
      // Check for high complexity
      if (analysis.complexity.total > 20) {
        this.reusabilityIssues.push({
          type: 'high-complexity',
          file: filePath,
          complexity: analysis.complexity.total,
          severity: analysis.complexity.total > 30 ? 'high' : 'medium',
          suggestion: 'Extract logic into custom hooks or smaller components'
        });
      }
      
      // Check for too many imports
      if (analysis.imports.length > 8) {
        this.reusabilityIssues.push({
          type: 'too-many-imports',
          file: filePath,
          importCount: analysis.imports.length,
          severity: analysis.imports.length > 12 ? 'high' : 'medium',
          suggestion: 'Consider if this component is doing too many things'
        });
      }
    }
  }

  analyzeDataFlowPatterns() {
    console.log('🔄 Analyzing data flow patterns...\n');
    
    for (const [filePath, analysis] of this.componentAnalysis) {
      // Detect prop drilling
      if (analysis.propDrilling.hasPropDrilling && analysis.propDrilling.severity !== 'low') {
        this.dataFlowPatterns.push({
          type: 'prop-drilling',
          file: filePath,
          drilledProps: analysis.propDrilling.drilledProps,
          severity: analysis.propDrilling.severity,
          suggestion: 'Consider using Context API or state management library'
        });
      }
      
      // Detect components that mainly pass props through
      const propCount = analysis.props.length;
      const responsibilityCount = analysis.responsibilities.length;
      
      if (propCount > 5 && responsibilityCount <= 2 && 
          analysis.responsibilities.includes('ui-rendering')) {
        this.dataFlowPatterns.push({
          type: 'prop-passthrough',
          file: filePath,
          propCount,
          suggestion: 'Consider if this component adds value or is just passing props'
        });
      }
      
      // Detect missing abstraction opportunities
      if (analysis.stateManagement.stateComplexity > 5 && 
          !analysis.stateManagement.hasCustomHooks) {
        this.dataFlowPatterns.push({
          type: 'missing-abstraction',
          file: filePath,
          stateComplexity: analysis.stateManagement.stateComplexity,
          suggestion: 'Extract state logic into custom hooks'
        });
      }
    }
  }

  identifyOversizedComponents() {
    console.log('📏 Identifying oversized components...\n');
    
    for (const [filePath, analysis] of this.componentAnalysis) {
      if (analysis.lineCount > 200 && analysis.imports.length > 5) {
        this.oversizedComponents.push({
          file: filePath,
          lineCount: analysis.lineCount,
          importCount: analysis.imports.length,
          responsibilities: analysis.responsibilities,
          complexity: analysis.complexity.total,
          severity: analysis.lineCount > 400 ? 'high' : 
                   analysis.lineCount > 300 ? 'medium' : 'low',
          suggestions: this.generateSplittingSuggestions(analysis)
        });
      }
    }
  }

  generateSplittingSuggestions(analysis) {
    const suggestions = [];
    
    if (analysis.responsibilities.includes('data-fetching')) {
      suggestions.push('Extract data fetching logic into custom hooks');
    }
    
    if (analysis.responsibilities.includes('form-handling')) {
      suggestions.push('Extract form logic into separate form components');
    }
    
    if (analysis.stateManagement.stateComplexity > 3) {
      suggestions.push('Extract state management into custom hooks or context');
    }
    
    if (analysis.complexity.conditionals > 5) {
      suggestions.push('Extract conditional rendering into separate components');
    }
    
    if (analysis.imports.length > 8) {
      suggestions.push('Split into multiple focused components');
    }
    
    return suggestions;
  }

  generateReport() {
    console.log('📋 COMPONENT REUSABILITY & ARCHITECTURE ANALYSIS REPORT\n');
    console.log('=' .repeat(70));
    
    // Summary
    console.log(`\n📈 SUMMARY:`);
    console.log(`Total components analyzed: ${this.componentAnalysis.size}`);
    console.log(`Reusability issues found: ${this.reusabilityIssues.length}`);
    console.log(`Data flow issues found: ${this.dataFlowPatterns.length}`);
    console.log(`Oversized components found: ${this.oversizedComponents.length}`);
    
    // Component Reusability Issues
    if (this.reusabilityIssues.length > 0) {
      console.log(`\n🔧 COMPONENT REUSABILITY ISSUES:`);
      
      const groupedIssues = this.groupBy(this.reusabilityIssues, 'type');
      
      for (const [type, issues] of Object.entries(groupedIssues)) {
        console.log(`\n${type.toUpperCase().replace('-', ' ')} (${issues.length} issues):`);
        
        issues.slice(0, 5).forEach((issue, index) => {
          console.log(`\n${index + 1}. ${issue.file}`);
          console.log(`   Severity: ${issue.severity}`);
          console.log(`   Suggestion: ${issue.suggestion}`);
          
          if (issue.responsibilities) {
            console.log(`   Responsibilities: ${issue.responsibilities.join(', ')}`);
          }
          if (issue.complexity) {
            console.log(`   Complexity Score: ${issue.complexity}`);
          }
          if (issue.importCount) {
            console.log(`   Import Count: ${issue.importCount}`);
          }
        });
        
        if (issues.length > 5) {
          console.log(`   ... and ${issues.length - 5} more`);
        }
      }
    }
    
    // Data Flow Patterns
    if (this.dataFlowPatterns.length > 0) {
      console.log(`\n🔄 DATA FLOW PATTERN ISSUES:`);
      
      const groupedPatterns = this.groupBy(this.dataFlowPatterns, 'type');
      
      for (const [type, patterns] of Object.entries(groupedPatterns)) {
        console.log(`\n${type.toUpperCase().replace('-', ' ')} (${patterns.length} issues):`);
        
        patterns.slice(0, 5).forEach((pattern, index) => {
          console.log(`\n${index + 1}. ${pattern.file}`);
          console.log(`   Suggestion: ${pattern.suggestion}`);
          
          if (pattern.drilledProps) {
            console.log(`   Drilled Props: ${pattern.drilledProps.join(', ')}`);
          }
          if (pattern.propCount) {
            console.log(`   Prop Count: ${pattern.propCount}`);
          }
          if (pattern.stateComplexity) {
            console.log(`   State Complexity: ${pattern.stateComplexity}`);
          }
        });
        
        if (patterns.length > 5) {
          console.log(`   ... and ${patterns.length - 5} more`);
        }
      }
    }
    
    // Oversized Components
    if (this.oversizedComponents.length > 0) {
      console.log(`\n📏 OVERSIZED COMPONENTS (>200 lines with many imports):`);
      
      this.oversizedComponents
        .sort((a, b) => b.lineCount - a.lineCount)
        .slice(0, 10)
        .forEach((component, index) => {
          console.log(`\n${index + 1}. ${component.file}`);
          console.log(`   Lines: ${component.lineCount}`);
          console.log(`   Imports: ${component.importCount}`);
          console.log(`   Complexity: ${component.complexity}`);
          console.log(`   Responsibilities: ${component.responsibilities.join(', ')}`);
          console.log(`   Severity: ${component.severity}`);
          console.log(`   Suggestions:`);
          component.suggestions.forEach(suggestion => {
            console.log(`     • ${suggestion}`);
          });
        });
    }
    
    // Top Recommendations
    console.log(`\n🎯 TOP RECOMMENDATIONS:`);
    
    const highSeverityIssues = [
      ...this.reusabilityIssues.filter(i => i.severity === 'high'),
      ...this.dataFlowPatterns.filter(p => p.severity === 'high'),
      ...this.oversizedComponents.filter(c => c.severity === 'high')
    ];
    
    if (highSeverityIssues.length > 0) {
      console.log(`\n🔥 HIGH PRIORITY (${highSeverityIssues.length} issues):`);
      highSeverityIssues.slice(0, 5).forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.file || issue.file}`);
        console.log(`   ${issue.suggestion || issue.suggestions?.[0]}`);
      });
    }
    
    console.log('\n' + '='.repeat(70));
    console.log('Analysis complete! 🎉');
  }

  groupBy(array, key) {
    return array.reduce((groups, item) => {
      const group = item[key];
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  }
}

// Run the analyzer
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new ComponentReusabilityAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default ComponentReusabilityAnalyzer;
