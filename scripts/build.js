#!/usr/bin/env node

/**
 * Build Script for ADHD Trading Dashboard
 * 
 * Main build script that orchestrates building packages in the monorepo.
 * Supports building individual packages or all packages in dependency order.
 */

import buildUtils from './build/build-utils.js';

// Parse command line arguments
const args = process.argv.slice(2);
const target = args.find(arg => !arg.startsWith('--')) || 'all';
const options = {
  clean: args.includes('--clean'),
  dev: args.includes('--dev'),
  watch: args.includes('--watch'),
  validate: args.includes('--validate'),
  production: !args.includes('--dev')
};

// Display help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
ADHD Trading Dashboard Build Script

Usage: node scripts/build.js [target] [options]

Targets:
  all        Build all packages (default)
  shared     Build shared package only
  dashboard  Build dashboard package only

Options:
  --clean      Clean build artifacts before building
  --dev        Development build (faster, no optimization)
  --watch      Watch mode (rebuild on changes)
  --validate   Validate build after completion
  --help, -h   Show this help message

Examples:
  node scripts/build.js                    # Build all packages
  node scripts/build.js shared             # Build shared package only
  node scripts/build.js --clean --dev      # Clean and dev build all
  node scripts/build.js dashboard --watch  # Watch mode for dashboard
`);
  process.exit(0);
}

// Main execution
async function main() {
  console.log('🏎️  ADHD Trading Dashboard Build');
  console.log('================================');
  console.log(`Target: ${target}`);
  console.log(`Options:`, options);
  console.log('');

  try {
    await buildUtils.build(target, options);
    
    if (options.validate) {
      console.log('\n🔍 Validating build...');
      // Add validation logic here if needed
      console.log('✅ Build validation passed');
    }
    
    console.log('\n🎉 Build completed successfully!');
  } catch (error) {
    console.error('\n❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Run the build
main().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
