#!/usr/bin/env node

/**
 * Circular Dependency Analyzer
 *
 * Analyzes the codebase for circular dependencies and import relationship issues
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CircularDependencyAnalyzer {
  constructor() {
    this.fileMap = new Map(); // filepath -> file info
    this.dependencyGraph = new Map(); // filepath -> [dependencies]
    this.reverseGraph = new Map(); // filepath -> [dependents]
    this.circularPaths = [];
    this.redFlags = [];
  }

  /**
   * Main analysis function
   */
  async analyze() {
    console.log('🔍 Analyzing circular dependencies...\n');

    // Step 1: Scan all source files
    await this.scanSourceFiles();

    // Step 2: Build dependency graphs
    this.buildDependencyGraphs();

    // Step 3: Find circular dependencies
    this.findCircularDependencies();

    // Step 4: Identify red flags
    this.identifyRedFlags();

    // Step 5: Generate report
    this.generateReport();
  }

  /**
   * Scan all TypeScript/JavaScript source files
   */
  async scanSourceFiles() {
    const sourceDirectories = ['packages/dashboard/src', 'packages/shared/src'];

    for (const dir of sourceDirectories) {
      if (fs.existsSync(dir)) {
        await this.scanDirectory(dir);
      }
    }
  }

  /**
   * Recursively scan directory for source files
   */
  async scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules, dist, etc.
        if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
          await this.scanDirectory(fullPath);
        }
      } else if (this.isSourceFile(item)) {
        await this.analyzeFile(fullPath);
      }
    }
  }

  /**
   * Check if file is a source file we care about
   */
  isSourceFile(filename) {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    return (
      extensions.some((ext) => filename.endsWith(ext)) &&
      !filename.includes('.test.') &&
      !filename.includes('.spec.') &&
      !filename.includes('.d.ts')
    );
  }

  /**
   * Analyze individual file for imports and exports
   */
  async analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);

      const fileInfo = {
        path: relativePath,
        fullPath: filePath,
        imports: this.extractImports(content),
        exports: this.extractExports(content),
        directory: path.dirname(relativePath),
        filename: path.basename(filePath),
        isComponent: this.isComponent(content, filePath),
        isHook: this.isHook(filePath),
        isUtil: this.isUtil(filePath),
        isService: this.isService(filePath),
      };

      this.fileMap.set(relativePath, fileInfo);
    } catch (error) {
      console.error(`Error analyzing ${filePath}:`, error.message);
    }
  }

  /**
   * Extract import statements from file content
   */
  extractImports(content) {
    const imports = [];

    // ES6 import patterns
    const importPatterns = [
      /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g,
      /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g, // Dynamic imports
    ];

    for (const pattern of importPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const importPath = match[1];

        // Only track internal imports (relative or package imports)
        if (this.isInternalImport(importPath)) {
          imports.push({
            path: importPath,
            statement: match[0],
            isRelative: importPath.startsWith('.'),
            isPackageImport: importPath.startsWith('@adhd-trading-dashboard'),
          });
        }
      }
    }

    return imports;
  }

  /**
   * Extract export statements (simplified)
   */
  extractExports(content) {
    const exports = [];

    // Basic export patterns
    const exportPatterns = [
      /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g,
      /export\s+\{([^}]+)\}/g,
      /export\s+\*\s+from\s+['"`]([^'"`]+)['"`]/g,
    ];

    for (const pattern of exportPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        exports.push({
          statement: match[0],
          name: match[1] || 'unknown',
        });
      }
    }

    return exports;
  }

  /**
   * Check if import is internal to our codebase
   */
  isInternalImport(importPath) {
    return (
      importPath.startsWith('.') ||
      importPath.startsWith('@adhd-trading-dashboard') ||
      importPath.startsWith('/')
    );
  }

  /**
   * Determine if file is a React component
   */
  isComponent(content, filePath) {
    return (
      (filePath.includes('components') ||
        filePath.endsWith('.tsx') ||
        content.includes('React.FC') ||
        (content.includes('function') && content.includes('return') && content.includes('jsx'))) &&
      !filePath.includes('hooks') &&
      !filePath.includes('utils')
    );
  }

  /**
   * Determine if file is a custom hook
   */
  isHook(filePath) {
    return filePath.includes('hooks') || path.basename(filePath).startsWith('use');
  }

  /**
   * Determine if file is a utility
   */
  isUtil(filePath) {
    return filePath.includes('utils') || filePath.includes('helpers');
  }

  /**
   * Determine if file is a service
   */
  isService(filePath) {
    return filePath.includes('services') || filePath.includes('api');
  }

  /**
   * Build dependency graphs
   */
  buildDependencyGraphs() {
    for (const [filePath, fileInfo] of this.fileMap) {
      const dependencies = [];

      for (const imp of fileInfo.imports) {
        const resolvedPath = this.resolveImportPath(filePath, imp.path);
        if (resolvedPath && this.fileMap.has(resolvedPath)) {
          dependencies.push(resolvedPath);

          // Build reverse graph
          if (!this.reverseGraph.has(resolvedPath)) {
            this.reverseGraph.set(resolvedPath, []);
          }
          this.reverseGraph.get(resolvedPath).push(filePath);
        }
      }

      this.dependencyGraph.set(filePath, dependencies);
    }
  }

  /**
   * Resolve import path to actual file path
   */
  resolveImportPath(fromFile, importPath) {
    if (importPath.startsWith('@adhd-trading-dashboard/shared')) {
      // Package import
      const subPath = importPath.replace('@adhd-trading-dashboard/shared', 'packages/shared/src');
      return this.findActualFile(subPath);
    } else if (importPath.startsWith('.')) {
      // Relative import
      const fromDir = path.dirname(fromFile);
      const resolved = path.normalize(path.join(fromDir, importPath));
      return this.findActualFile(resolved);
    }

    return null;
  }

  /**
   * Find actual file (handle index files and extensions)
   */
  findActualFile(basePath) {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];

    // Try exact path
    for (const ext of extensions) {
      const withExt = basePath + ext;
      if (this.fileMap.has(withExt)) {
        return withExt;
      }
    }

    // Try index file
    for (const ext of extensions) {
      const indexPath = path.join(basePath, 'index' + ext);
      if (this.fileMap.has(indexPath)) {
        return indexPath;
      }
    }

    return null;
  }

  /**
   * Find circular dependencies using DFS
   */
  findCircularDependencies() {
    const visited = new Set();
    const recursionStack = new Set();

    for (const filePath of this.fileMap.keys()) {
      if (!visited.has(filePath)) {
        this.dfsCircular(filePath, visited, recursionStack, []);
      }
    }
  }

  /**
   * DFS helper for finding circular dependencies
   */
  dfsCircular(filePath, visited, recursionStack, path) {
    visited.add(filePath);
    recursionStack.add(filePath);
    path.push(filePath);

    const dependencies = this.dependencyGraph.get(filePath) || [];

    for (const dep of dependencies) {
      if (!visited.has(dep)) {
        this.dfsCircular(dep, visited, recursionStack, [...path]);
      } else if (recursionStack.has(dep)) {
        // Found circular dependency
        const cycleStart = path.indexOf(dep);
        const cycle = [...path.slice(cycleStart), dep];
        this.circularPaths.push(cycle);
      }
    }

    recursionStack.delete(filePath);
  }

  /**
   * Identify red flag import patterns
   */
  identifyRedFlags() {
    for (const [filePath, fileInfo] of this.fileMap) {
      // Red flag 1: Utils importing UI components
      if (fileInfo.isUtil) {
        for (const imp of fileInfo.imports) {
          const depPath = this.resolveImportPath(filePath, imp.path);
          const depInfo = depPath ? this.fileMap.get(depPath) : null;

          if (depInfo && depInfo.isComponent) {
            this.redFlags.push({
              type: 'util-imports-component',
              from: filePath,
              to: depPath,
              message: 'Utility importing UI component',
            });
          }
        }
      }

      // Red flag 2: Files in same directory importing each other
      const sameDirImports = fileInfo.imports.filter((imp) => {
        const depPath = this.resolveImportPath(filePath, imp.path);
        const depInfo = depPath ? this.fileMap.get(depPath) : null;
        return depInfo && depInfo.directory === fileInfo.directory && depPath !== filePath;
      });

      if (sameDirImports.length > 2) {
        this.redFlags.push({
          type: 'same-directory-coupling',
          from: filePath,
          imports: sameDirImports,
          message: 'High coupling within directory',
        });
      }

      // Red flag 3: Components importing other components from same level
      if (fileInfo.isComponent) {
        for (const imp of fileInfo.imports) {
          const depPath = this.resolveImportPath(filePath, imp.path);
          const depInfo = depPath ? this.fileMap.get(depPath) : null;

          if (
            depInfo &&
            depInfo.isComponent &&
            this.isSameLevel(fileInfo.directory, depInfo.directory)
          ) {
            this.redFlags.push({
              type: 'component-same-level-import',
              from: filePath,
              to: depPath,
              message: 'Component importing component from same level',
            });
          }
        }
      }
    }
  }

  /**
   * Check if two directories are at the same level
   */
  isSameLevel(dir1, dir2) {
    const depth1 = dir1.split('/').length;
    const depth2 = dir2.split('/').length;
    return Math.abs(depth1 - depth2) <= 1;
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    console.log('📊 CIRCULAR DEPENDENCY ANALYSIS REPORT\n');
    console.log('='.repeat(60));

    // Summary
    console.log(`\n📈 SUMMARY:`);
    console.log(`Total files analyzed: ${this.fileMap.size}`);
    console.log(`Circular dependencies found: ${this.circularPaths.length}`);
    console.log(`Red flags identified: ${this.redFlags.length}`);

    // File breakdown
    const components = Array.from(this.fileMap.values()).filter((f) => f.isComponent).length;
    const hooks = Array.from(this.fileMap.values()).filter((f) => f.isHook).length;
    const utils = Array.from(this.fileMap.values()).filter((f) => f.isUtil).length;
    const services = Array.from(this.fileMap.values()).filter((f) => f.isService).length;

    console.log(`\n📁 FILE BREAKDOWN:`);
    console.log(`Components: ${components}`);
    console.log(`Hooks: ${hooks}`);
    console.log(`Utils: ${utils}`);
    console.log(`Services: ${services}`);
    console.log(`Other: ${this.fileMap.size - components - hooks - utils - services}`);

    // Circular dependencies
    if (this.circularPaths.length > 0) {
      console.log(`\n🔄 CIRCULAR DEPENDENCIES:`);
      this.circularPaths.forEach((cycle, index) => {
        console.log(`\n${index + 1}. Circular path:`);
        cycle.forEach((file, i) => {
          console.log(`   ${i === 0 ? '→' : ' '} ${file}`);
        });
      });
    } else {
      console.log(`\n✅ No circular dependencies found!`);
    }

    // Red flags
    if (this.redFlags.length > 0) {
      console.log(`\n🚩 RED FLAGS:`);
      this.redFlags.forEach((flag, index) => {
        console.log(`\n${index + 1}. ${flag.message}`);
        console.log(`   From: ${flag.from}`);
        if (flag.to) {
          console.log(`   To: ${flag.to}`);
        }
        if (flag.imports) {
          console.log(`   Imports: ${flag.imports.length} same-directory files`);
        }
      });
    } else {
      console.log(`\n✅ No red flags found!`);
    }

    // Detailed file analysis
    console.log(`\n📋 DETAILED FILE ANALYSIS:`);
    console.log(`\nTop 10 most imported files:`);

    const importCounts = new Map();
    for (const [file, dependents] of this.reverseGraph) {
      importCounts.set(file, dependents.length);
    }

    const sortedByImports = Array.from(importCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    sortedByImports.forEach(([file, count], index) => {
      console.log(`${index + 1}. ${file} (imported by ${count} files)`);
    });

    console.log(`\nTop 10 files with most dependencies:`);

    const depCounts = new Map();
    for (const [file, deps] of this.dependencyGraph) {
      depCounts.set(file, deps.length);
    }

    const sortedByDeps = Array.from(depCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    sortedByDeps.forEach(([file, count], index) => {
      console.log(`${index + 1}. ${file} (imports ${count} files)`);
    });

    console.log('\n' + '='.repeat(60));
    console.log('Analysis complete! 🎉');
  }
}

// Run the analyzer
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new CircularDependencyAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default CircularDependencyAnalyzer;
