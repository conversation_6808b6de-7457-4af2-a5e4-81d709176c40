#!/usr/bin/env node

/**
 * TradingDashboard Pre-Refactor Analysis Tool
 * 
 * Analyzes the TradingDashboard component for:
 * 1. User interaction patterns and workflows
 * 2. Data dependency analysis and flow mapping
 * 3. Error boundary analysis and failure points
 * 4. Performance hotspots and expensive operations
 * 5. Risk assessment for 6-component split
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TradingDashboardAnalyzer {
  constructor() {
    this.componentAnalysis = {
      userInteractions: [],
      dataFlows: [],
      errorBoundaries: [],
      performanceHotspots: [],
      dependencies: [],
      stateManagement: [],
      eventHandlers: [],
      computations: [],
      renderPatterns: []
    };
  }

  async analyze() {
    console.log('🔍 Analyzing TradingDashboard component for refactoring...\n');
    
    await this.analyzeTradingDashboard();
    await this.analyzeRelatedComponents();
    this.generateRiskAssessment();
  }

  async analyzeTradingDashboard() {
    const filePath = 'packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx';
    
    if (!fs.existsSync(filePath)) {
      console.error(`❌ TradingDashboard.tsx not found at ${filePath}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    console.log('📊 TRADING DASHBOARD ANALYSIS');
    console.log('=' .repeat(50));
    console.log(`File: ${filePath}`);
    console.log(`Lines: ${lines.length}`);
    console.log(`Size: ${(content.length / 1024).toFixed(1)}KB\n`);

    // Analyze different aspects
    this.analyzeUserInteractions(content);
    this.analyzeDataDependencies(content);
    this.analyzeErrorBoundaries(content);
    this.analyzePerformanceHotspots(content);
    this.analyzeStateManagement(content);
  }

  analyzeUserInteractions(content) {
    console.log('👆 USER INTERACTION PATTERNS:');
    
    const interactions = {
      clicks: [],
      forms: [],
      navigation: [],
      realtime: []
    };

    // Find click handlers
    const clickPatterns = [
      /onClick\s*=\s*\{([^}]+)\}/g,
      /onSubmit\s*=\s*\{([^}]+)\}/g,
      /onChange\s*=\s*\{([^}]+)\}/g,
      /onSelect\s*=\s*\{([^}]+)\}/g
    ];

    clickPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        interactions.clicks.push({
          handler: match[1].trim(),
          context: this.getContextAroundMatch(content, match.index, 50)
        });
      }
    });

    // Find form interactions
    const formPatterns = [
      /handleSubmit/g,
      /handleChange/g,
      /handleBlur/g,
      /handleFocus/g,
      /validation/gi
    ];

    formPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        interactions.forms.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 30)
        });
      }
    });

    // Find navigation patterns
    const navPatterns = [
      /setActiveTab/g,
      /navigate/g,
      /router/g,
      /tab.*click/gi
    ];

    navPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        interactions.navigation.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 30)
        });
      }
    });

    // Find real-time interactions
    const realtimePatterns = [
      /useEffect/g,
      /setInterval/g,
      /setTimeout/g,
      /websocket/gi,
      /polling/gi
    ];

    realtimePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        interactions.realtime.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 40)
        });
      }
    });

    console.log(`  Click Handlers: ${interactions.clicks.length}`);
    console.log(`  Form Interactions: ${interactions.forms.length}`);
    console.log(`  Navigation: ${interactions.navigation.length}`);
    console.log(`  Real-time Updates: ${interactions.realtime.length}`);

    // Identify high-frequency interactions
    const highFrequency = [
      ...interactions.clicks.filter(c => 
        c.handler.includes('Tab') || 
        c.handler.includes('refresh') || 
        c.handler.includes('submit')
      ),
      ...interactions.forms.filter(f => 
        f.type.includes('Change') || 
        f.type.includes('Submit')
      )
    ];

    console.log(`  High-Frequency Interactions: ${highFrequency.length}`);
    
    this.componentAnalysis.userInteractions = interactions;
    console.log('');
  }

  analyzeDataDependencies(content) {
    console.log('📊 DATA DEPENDENCY ANALYSIS:');
    
    const dependencies = {
      imports: [],
      hooks: [],
      props: [],
      context: [],
      external: []
    };

    // Find imports
    const importPattern = /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+['"`]([^'"`]+)['"`]/g;
    let match;
    while ((match = importPattern.exec(content)) !== null) {
      dependencies.imports.push({
        path: match[1],
        statement: match[0]
      });
    }

    // Find hooks
    const hookPattern = /use\w+\(/g;
    while ((match = hookPattern.exec(content)) !== null) {
      dependencies.hooks.push({
        hook: match[0].slice(0, -1),
        context: this.getContextAroundMatch(content, match.index, 30)
      });
    }

    // Find props destructuring
    const propsPattern = /\{\s*([^}]+)\s*\}:\s*\w*Props/g;
    while ((match = propsPattern.exec(content)) !== null) {
      const props = match[1].split(',').map(p => p.trim());
      dependencies.props.push(...props);
    }

    // Find context usage
    const contextPattern = /useContext\(([^)]+)\)/g;
    while ((match = contextPattern.exec(content)) !== null) {
      dependencies.context.push(match[1]);
    }

    // Find external API calls
    const apiPattern = /(fetch|axios|api\.|\.get\(|\.post\(|\.put\(|\.delete\()/g;
    while ((match = apiPattern.exec(content)) !== null) {
      dependencies.external.push({
        type: match[1],
        context: this.getContextAroundMatch(content, match.index, 40)
      });
    }

    console.log(`  Imports: ${dependencies.imports.length}`);
    console.log(`  Hooks: ${dependencies.hooks.length}`);
    console.log(`  Props: ${dependencies.props.length}`);
    console.log(`  Context Usage: ${dependencies.context.length}`);
    console.log(`  External APIs: ${dependencies.external.length}`);

    // Identify critical data flows
    const criticalFlows = dependencies.hooks.filter(h => 
      h.hook.includes('useState') || 
      h.hook.includes('useEffect') || 
      h.hook.includes('useCallback')
    );

    console.log(`  Critical Data Flows: ${criticalFlows.length}`);
    
    this.componentAnalysis.dataFlows = dependencies;
    console.log('');
  }

  analyzeErrorBoundaries(content) {
    console.log('🚨 ERROR BOUNDARY ANALYSIS:');
    
    const errorPoints = {
      asyncOperations: [],
      formValidation: [],
      dataFetching: [],
      userInput: [],
      calculations: []
    };

    // Find async operations
    const asyncPatterns = [
      /await\s+/g,
      /\.then\(/g,
      /\.catch\(/g,
      /try\s*\{/g,
      /catch\s*\(/g
    ];

    asyncPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        errorPoints.asyncOperations.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 50)
        });
      }
    });

    // Find form validation
    const validationPatterns = [
      /validation/gi,
      /error/gi,
      /invalid/gi,
      /required/gi
    ];

    validationPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        errorPoints.formValidation.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 30)
        });
      }
    });

    // Find data fetching
    const fetchPatterns = [
      /fetch\(/g,
      /api\./g,
      /\.get\(/g,
      /\.post\(/g
    ];

    fetchPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        errorPoints.dataFetching.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 40)
        });
      }
    });

    console.log(`  Async Operations: ${errorPoints.asyncOperations.length}`);
    console.log(`  Form Validation Points: ${errorPoints.formValidation.length}`);
    console.log(`  Data Fetching Points: ${errorPoints.dataFetching.length}`);

    this.componentAnalysis.errorBoundaries = errorPoints;
    console.log('');
  }

  analyzePerformanceHotspots(content) {
    console.log('⚡ PERFORMANCE HOTSPOT ANALYSIS:');
    
    const hotspots = {
      heavyComputations: [],
      frequentRenders: [],
      memoryLeaks: [],
      inefficientPatterns: []
    };

    // Find heavy computations
    const computationPatterns = [
      /\.map\(/g,
      /\.filter\(/g,
      /\.reduce\(/g,
      /\.sort\(/g,
      /for\s*\(/g,
      /while\s*\(/g
    ];

    computationPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        hotspots.heavyComputations.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 40)
        });
      }
    });

    // Find frequent render triggers
    const renderPatterns = [
      /useState/g,
      /useEffect/g,
      /useMemo/g,
      /useCallback/g
    ];

    renderPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        hotspots.frequentRenders.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 30)
        });
      }
    });

    // Find potential memory leaks
    const memoryPatterns = [
      /setInterval/g,
      /setTimeout/g,
      /addEventListener/g,
      /useEffect.*\[\]/g
    ];

    memoryPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        hotspots.memoryLeaks.push({
          type: match[0],
          context: this.getContextAroundMatch(content, match.index, 50)
        });
      }
    });

    console.log(`  Heavy Computations: ${hotspots.heavyComputations.length}`);
    console.log(`  Render Triggers: ${hotspots.frequentRenders.length}`);
    console.log(`  Potential Memory Leaks: ${hotspots.memoryLeaks.length}`);

    this.componentAnalysis.performanceHotspots = hotspots;
    console.log('');
  }

  analyzeStateManagement(content) {
    console.log('🔄 STATE MANAGEMENT ANALYSIS:');
    
    const stateAnalysis = {
      localState: [],
      sharedState: [],
      derivedState: [],
      sideEffects: []
    };

    // Find useState
    const statePattern = /useState\s*\(\s*([^)]*)\s*\)/g;
    let match;
    while ((match = statePattern.exec(content)) !== null) {
      stateAnalysis.localState.push({
        initialValue: match[1],
        context: this.getContextAroundMatch(content, match.index, 40)
      });
    }

    // Find useEffect
    const effectPattern = /useEffect\s*\(\s*[^,]+,\s*(\[[^\]]*\])/g;
    while ((match = effectPattern.exec(content)) !== null) {
      stateAnalysis.sideEffects.push({
        dependencies: match[1],
        context: this.getContextAroundMatch(content, match.index, 50)
      });
    }

    console.log(`  Local State Variables: ${stateAnalysis.localState.length}`);
    console.log(`  Side Effects: ${stateAnalysis.sideEffects.length}`);

    this.componentAnalysis.stateManagement = stateAnalysis;
    console.log('');
  }

  async analyzeRelatedComponents() {
    console.log('🔗 RELATED COMPONENTS ANALYSIS:');
    
    const relatedPaths = [
      'packages/dashboard/src/features/trading-dashboard/',
      'packages/dashboard/src/components/',
      'packages/shared/src/'
    ];

    let totalRelated = 0;
    for (const basePath of relatedPaths) {
      if (fs.existsSync(basePath)) {
        const files = this.getFilesRecursively(basePath)
          .filter(f => f.endsWith('.tsx') || f.endsWith('.ts'))
          .filter(f => !f.includes('.test.') && !f.includes('.spec.'));
        totalRelated += files.length;
      }
    }

    console.log(`  Related Components: ${totalRelated}`);
    console.log('');
  }

  generateRiskAssessment() {
    console.log('⚠️  REFACTORING RISK ASSESSMENT');
    console.log('=' .repeat(50));

    const proposedComponents = [
      {
        name: 'TradingDashboardContainer',
        purpose: 'Main orchestrator (80 lines)',
        risks: this.assessContainerRisks()
      },
      {
        name: 'F1Header',
        purpose: 'Reusable header (40 lines)',
        risks: this.assessHeaderRisks()
      },
      {
        name: 'DashboardTabs',
        purpose: 'Tab navigation (60 lines)',
        risks: this.assessTabsRisks()
      },
      {
        name: 'QuickTradeForm',
        purpose: 'Form component (80 lines)',
        risks: this.assessFormRisks()
      },
      {
        name: 'useTradingDashboardData',
        purpose: 'Data logic hook (100 lines)',
        risks: this.assessDataHookRisks()
      },
      {
        name: 'TradingDashboardContext',
        purpose: 'State management (50 lines)',
        risks: this.assessContextRisks()
      }
    ];

    proposedComponents.forEach((component, index) => {
      console.log(`\n${index + 1}. ${component.name}`);
      console.log(`   Purpose: ${component.purpose}`);
      console.log(`   Risk Level: ${component.risks.level}`);
      console.log(`   Key Risks:`);
      component.risks.risks.forEach(risk => {
        console.log(`     • ${risk}`);
      });
      console.log(`   Mitigation:`);
      component.risks.mitigation.forEach(mit => {
        console.log(`     ✓ ${mit}`);
      });
    });

    console.log('\n🎯 OVERALL REFACTORING STRATEGY:');
    console.log('  1. Start with low-risk components (F1Header, DashboardTabs)');
    console.log('  2. Create context and data hook next');
    console.log('  3. Extract form component with careful testing');
    console.log('  4. Refactor container last to wire everything together');
    console.log('  5. Implement gradual migration with feature flags');
  }

  assessContainerRisks() {
    return {
      level: 'MEDIUM',
      risks: [
        'Integration complexity with all child components',
        'State coordination between multiple contexts',
        'Potential prop drilling during transition',
        'Error boundary placement decisions'
      ],
      mitigation: [
        'Use composition pattern with clear interfaces',
        'Implement gradual migration strategy',
        'Add comprehensive integration tests',
        'Use feature flags for safe rollback'
      ]
    };
  }

  assessHeaderRisks() {
    return {
      level: 'LOW',
      risks: [
        'F1 theme consistency across different contexts',
        'Responsive design edge cases',
        'Live session indicator state management'
      ],
      mitigation: [
        'Extract F1 theme to shared constants',
        'Add responsive design tests',
        'Use context for live session state'
      ]
    };
  }

  assessTabsRisks() {
    return {
      level: 'LOW-MEDIUM',
      risks: [
        'Tab state synchronization with URL routing',
        'Active tab persistence across page refreshes',
        'Keyboard navigation accessibility'
      ],
      mitigation: [
        'Use URL-based tab state management',
        'Implement localStorage for tab persistence',
        'Add comprehensive accessibility tests'
      ]
    };
  }

  assessFormRisks() {
    return {
      level: 'HIGH',
      risks: [
        'Form validation logic complexity',
        'Real-time data updates during form entry',
        'Form state persistence across tab switches',
        'Integration with trading APIs'
      ],
      mitigation: [
        'Use shared form validation hooks',
        'Implement optimistic updates with rollback',
        'Add form state persistence layer',
        'Create comprehensive API integration tests'
      ]
    };
  }

  assessDataHookRisks() {
    return {
      level: 'HIGH',
      risks: [
        'Data fetching race conditions',
        'Cache invalidation complexity',
        'Real-time data synchronization',
        'Error handling for multiple data sources'
      ],
      mitigation: [
        'Use React Query for data fetching',
        'Implement proper loading states',
        'Add retry mechanisms with exponential backoff',
        'Create centralized error handling'
      ]
    };
  }

  assessContextRisks() {
    return {
      level: 'MEDIUM',
      risks: [
        'Context value object recreation causing re-renders',
        'Provider placement in component tree',
        'State update batching issues',
        'Memory leaks from uncleaned subscriptions'
      ],
      mitigation: [
        'Use useMemo for context values',
        'Implement proper provider hierarchy',
        'Use React 18 automatic batching',
        'Add cleanup in useEffect hooks'
      ]
    };
  }

  getContextAroundMatch(content, index, chars) {
    const start = Math.max(0, index - chars);
    const end = Math.min(content.length, index + chars);
    return content.slice(start, end).replace(/\s+/g, ' ').trim();
  }

  getFilesRecursively(dir) {
    let files = [];
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory() && !['node_modules', 'dist', 'build'].includes(item)) {
          files = files.concat(this.getFilesRecursively(fullPath));
        } else if (stat.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or permission denied
    }
    return files;
  }
}

// Run the analyzer
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new TradingDashboardAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default TradingDashboardAnalyzer;
