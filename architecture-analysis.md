# Architecture Analysis Report

Generated: 2025-05-25T10:55:55.694Z

## Project Overview


### adhd-trading-dashboard-lib
- **Type**: react
- **Module Type**: ES Module
- **Path**: /Users/<USER>/adhd-trading-dashboard-lib
- **Version**: 1.0.0
- **Dependencies**: 6
- **Scripts**: start, dev, build, build:clean, build:dev, build:validate, build:shared, build:dashboard, analyze, deploy, deploy:all, deploy:gh-pages, type-check, type-check:watch, lint, test, test:watch, test:coverage, test:e2e, test:e2e:ui, storybook, build-storybook, clean, clean:deep, check-versions, fix-versions, manage-assets, diagnostics, postinstall, health, health:analyze, health:scripts, health:cleanup, health:cleanup:dry, health:setup, // Note

### @adhd-trading-dashboard/shared
- **Type**: react
- **Module Type**: CommonJS
- **Path**: /Users/<USER>/adhd-trading-dashboard-lib/packages/shared
- **Version**: 1.0.0
- **Dependencies**: 3
- **Scripts**: build, build:dev, dev, clean, test, test:watch, typecheck, storybook, build-storybook

### @adhd-trading-dashboard/dashboard
- **Type**: react
- **Module Type**: CommonJS
- **Path**: /Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard
- **Version**: 0.1.0
- **Dependencies**: 8
- **Scripts**: prestart, start, predev, dev, prebuild, build, build:dev, preview, test, test:watch, typecheck, lint, storybook, build-storybook


## Metrics Summary

| Metric | Value |
|--------|-------|
| Total Files | 222 |
| Total Lines | 28,942 |
| Average Complexity | 10.50 |
| Test Coverage | 14% |

## Module Types

| Type | Count |
|------|-------|
| ES Modules | 176 |
| CommonJS | 7 |
| Mixed | 0 |

## File Breakdown

| Category | Count |
|----------|-------|
| Components | 116 |
| Hooks | 15 |
| Utilities | 80 |
| Services | 11 |
| Tests | 30 |

## Largest Files

- **packages/shared/src/services/tradeStorage.ts**: 830 lines
- **code-health/dynamic-scripts-cleanup.js**: 785 lines
- **packages/shared/src/components/molecules/Table.tsx**: 474 lines
- **packages/shared/src/components/molecules/TradeTable.tsx**: 439 lines
- **packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts**: 435 lines

## Most Complex Files

- **packages/shared/src/components/molecules/TradeTable.tsx**: Complexity 96
- **code-health/dynamic-scripts-cleanup.js**: Complexity 88
- **packages/shared/src/components/atoms/Input.tsx**: Complexity 78
- **packages/shared/src/components/molecules/Table.tsx**: Complexity 75
- **packages/shared/src/components/molecules/TradeTableRow.tsx**: Complexity 74

## Recommendations


### Split large files (high priority)
Several files exceed 300 lines and should be split into smaller modules

**Affected files:**
- packages/shared/src/services/tradeStorage.ts
- code-health/dynamic-scripts-cleanup.js
- packages/shared/src/components/molecules/Table.tsx
- packages/shared/src/components/molecules/TradeTable.tsx
- packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts
- packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx
- packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts
- packages/shared/src/components/atoms/Input.tsx
- packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx
- packages/shared/src/components/atoms/Select.tsx

### Increase test coverage (high priority)
Test coverage is 14%. Aim for at least 70%.



## Next Steps

1. Review high-priority recommendations
2. Consider running migration scripts for architectural improvements
3. Add tests for uncovered areas
4. Monitor complexity metrics over time
5. Standardize module types if mixed usage detected
