{"projectStructure": {"/Users/<USER>/adhd-trading-dashboard-lib": {"name": "adhd-trading-dashboard-lib", "version": "1.0.0", "path": "/Users/<USER>/adhd-trading-dashboard-lib", "type": "react", "dependencies": ["core-js", "react", "react-dom", "react-router-dom", "recharts", "styled-components"], "devDependencies": ["@babel/cli", "@babel/core", "@babel/plugin-proposal-class-properties", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-transform-runtime", "@babel/preset-env", "@babel/preset-react", "@babel/preset-typescript", "@babel/runtime", "@playwright/test", "@storybook/addon-a11y", "@storybook/addon-docs", "@storybook/addon-essentials", "@storybook/addon-interactions", "@storybook/addon-links", "@storybook/react", "@storybook/react-webpack5", "@testing-library/jest-dom", "@testing-library/react", "@testing-library/user-event", "@types/node", "@types/react", "@types/react-dom", "@types/react-router-dom", "@types/styled-components", "@typescript-eslint/eslint-plugin", "@typescript-eslint/parser", "@vitejs/plugin-react", "acorn", "babel-loader", "babel-plugin-styled-components", "chalk", "css-loader", "css-minimizer-webpack-plugin", "eslint", "eslint-plugin-react", "eslint-plugin-react-hooks", "glob", "html-webpack-plugin", "mini-css-extract-plugin", "<PERSON><PERSON><PERSON>", "schema-utils", "storybook", "style-loader", "terser-webpack-plugin", "typescript", "vite", "vitest", "webpack", "webpack-bundle-analyzer", "webpack-cli", "webpack-dev-server"], "scripts": ["start", "dev", "build", "build:clean", "build:dev", "build:validate", "build:shared", "build:dashboard", "analyze", "deploy", "deploy:all", "deploy:gh-pages", "type-check", "type-check:watch", "lint", "test", "test:watch", "test:coverage", "test:e2e", "test:e2e:ui", "storybook", "build-storybook", "clean", "clean:deep", "check-versions", "fix-versions", "manage-assets", "diagnostics", "postinstall", "health", "health:analyze", "health:scripts", "health:cleanup", "health:cleanup:dry", "health:setup", "// Note"], "isRoot": true, "isModule": true}, "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared": {"name": "@adhd-trading-dashboard/shared", "version": "1.0.0", "path": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared", "type": "react", "dependencies": ["@anthropic-ai/sdk", "@babel/parser", "@babel/traverse"], "devDependencies": ["canvas", "react", "react-dom", "styled-components", "typescript"], "scripts": ["build", "build:dev", "dev", "clean", "test", "test:watch", "typecheck", "storybook", "build-storybook"], "isRoot": false, "isModule": false}, "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard": {"name": "@adhd-trading-dashboard/dashboard", "version": "0.1.0", "path": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard", "type": "react", "dependencies": ["@adhd-trading-dashboard/shared", "@anthropic-ai/sdk", "@babel/parser", "@babel/traverse", "canvas", "express", "idb", "react-router-dom"], "devDependencies": ["@storybook/addon-essentials", "@storybook/addon-interactions", "@storybook/addon-links", "@storybook/addon-onboarding", "@storybook/addon-a11y", "@storybook/blocks", "@storybook/react", "@storybook/react-vite", "@storybook/test", "react", "react-dom", "react-scripts", "recharts", "storybook", "styled-components", "typescript", "vite-plugin-pwa", "web-vitals"], "scripts": ["prestart", "start", "predev", "dev", "prebuild", "build", "build:dev", "preview", "test", "test:watch", "typecheck", "lint", "storybook", "build-storybook"], "isRoot": false, "isModule": false}}, "components": [{"path": "packages/shared/src/styled.d.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/styled.d.ts", "size": 1103, "lines": 48, "complexity": 2, "imports": [{"statement": "import \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Theme } from \"./theme/types\"", "path": "./theme/types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface DefaultTheme", "name": "DefaultTheme", "type": "named", "module": "esm"}, {"statement": "export type ThemeProps", "name": "ThemeProps", "type": "named", "module": "esm"}, {"statement": "export type WithTheme", "name": "WithTheme", "type": "named", "module": "esm"}, {"statement": "export type StyledComponent", "name": "StyledComponent", "type": "named", "module": "esm"}], "dependencies": ["styled-components"], "lastModified": "2025-05-21T14:49:39.465Z"}, {"path": "packages/dashboard/src/TestApp.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/TestApp.tsx", "size": 1565, "lines": 72, "complexity": 1, "imports": [{"statement": "import * as React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react"], "lastModified": "2025-05-25T09:22:17.181Z"}, {"path": "packages/dashboard/src/SimpleApp.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/SimpleApp.tsx", "size": 554, "lines": 26, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react"], "lastModified": "2025-05-25T09:22:17.180Z"}, {"path": "packages/dashboard/src/MinimalApp.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/MinimalApp.tsx", "size": 926, "lines": 42, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react"], "lastModified": "2025-05-25T09:22:17.178Z"}, {"path": "packages/dashboard/src/App.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/App.tsx", "size": 590, "lines": 24, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { BrowserRouter } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { AppRoutes } from './routes'", "path": "./routes", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import AppErrorBoundary from './components/AppErrorBoundary'", "path": "./components/AppErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-25T09:22:17.177Z"}, {"path": "packages/shared/src/theme/ThemeProvider.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/ThemeProvider.tsx", "size": 3472, "lines": 122, "complexity": 15, "imports": [{"statement": "import React, { useState, createContext, useContext } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Theme } from './types'", "path": "./types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { f1Theme } from './f1Theme'", "path": "./f1Theme", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { lightTheme } from './lightTheme'", "path": "./lightTheme", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { darkTheme } from './darkTheme'", "path": "./darkTheme", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import GlobalStyles from './GlobalStyles'", "path": "./GlobalStyles", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const ThemeContext", "name": "ThemeContext", "type": "named", "module": "esm"}, {"statement": "export const useTheme", "name": "useTheme", "type": "named", "module": "esm"}, {"statement": "export const ThemeProvider", "name": "ThemeProvider", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T14:04:44.056Z"}, {"path": "packages/shared/src/state/createStoreContext.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/state/createStoreContext.tsx", "size": 3406, "lines": 133, "complexity": 6, "imports": [{"statement": "import React, { createContext, useContext, useReducer, useMemo, ReactNode } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface Action", "name": "Action", "type": "named", "module": "esm"}, {"statement": "export type Reducer", "name": "Reducer", "type": "named", "module": "esm"}, {"statement": "export type Selector", "name": "Selector", "type": "named", "module": "esm"}, {"statement": "export type ActionCreator", "name": "ActionCreator", "type": "named", "module": "esm"}, {"statement": "export type Dispatch", "name": "Dispatch", "type": "named", "module": "esm"}, {"statement": "export interface StoreContext", "name": "StoreContext", "type": "named", "module": "esm"}, {"statement": "export interface StoreProviderProps", "name": "StoreProviderProps", "type": "named", "module": "esm"}, {"statement": "export function createStoreContext", "name": "createStoreContext", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T18:18:23.177Z"}, {"path": "packages/shared/src/components/base.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/base.tsx", "size": 3868, "lines": 159, "complexity": 25, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface ButtonProps", "name": "ButtonProps", "type": "named", "module": "esm"}, {"statement": "export const Button", "name": "<PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export interface CardProps", "name": "CardProps", "type": "named", "module": "esm"}, {"statement": "export const Card", "name": "Card", "type": "named", "module": "esm"}, {"statement": "export interface InputProps", "name": "InputProps", "type": "named", "module": "esm"}, {"statement": "export const Input", "name": "Input", "type": "named", "module": "esm"}, {"statement": "export interface SelectOption", "name": "SelectOption", "type": "named", "module": "esm"}, {"statement": "export interface SelectProps", "name": "SelectProps", "type": "named", "module": "esm"}, {"statement": "export const Select", "name": "Select", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T00:34:41.460Z"}, {"path": "packages/dashboard/src/routes/routes.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/routes.tsx", "size": 1882, "lines": 56, "complexity": 1, "imports": [{"statement": "import React, { lazy, Suspense } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Routes, Route, Navigate } from \"react-router-dom\"", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import MainLayout from \"../layouts/MainLayout\"", "path": "../layouts/MainLayout", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import LoadingScreen from \"../components/molecules/LoadingScreen\"", "path": "../components/molecules/LoadingScreen", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom"], "lastModified": "2025-05-25T09:22:17.195Z"}, {"path": "packages/dashboard/src/routes/index.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/index.tsx", "size": 2312, "lines": 61, "complexity": 1, "imports": [{"statement": "import React, { lazy, Suspense } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Routes, Route, Navigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import MainLayout from './layouts/MainLayout'", "path": "./layouts/MainLayout", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import LoadingScreen from './components/molecules/LoadingScreen'", "path": "./components/molecules/LoadingScreen", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom"], "lastModified": "2025-05-23T15:19:47.749Z"}, {"path": "packages/dashboard/src/pages/TradeJournal.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/TradeJournal.tsx", "size": 796, "lines": 41, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T08:44:56.877Z"}, {"path": "packages/dashboard/src/pages/TradeForm.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/TradeForm.tsx", "size": 1781, "lines": 53, "complexity": 5, "imports": [{"statement": "import React, { useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useParams, useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import TradeFormFeature from '../features/trade-journal/TradeForm'", "path": "../features/trade-journal/TradeForm", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom"], "lastModified": "2025-05-23T21:47:27.800Z"}, {"path": "packages/dashboard/src/pages/TradeAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/TradeAnalysis.tsx", "size": 1161, "lines": 45, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeAnalysis as TradeAnalysisFeature } from '../features/trade-analysis'", "path": "../features/trade-analysis", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import FeatureErrorBoundary from '../components/FeatureErrorBoundary'", "path": "../components/FeatureErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom"], "lastModified": "2025-05-23T21:47:27.799Z"}, {"path": "packages/dashboard/src/pages/Settings.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/Settings.tsx", "size": 783, "lines": 41, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T08:45:19.127Z"}, {"path": "packages/dashboard/src/pages/NotFound.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/NotFound.tsx", "size": 1564, "lines": 64, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Link } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "react-router-dom"], "lastModified": "2025-05-21T08:45:29.953Z"}, {"path": "packages/dashboard/src/pages/Dashboard.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/Dashboard.tsx", "size": 369, "lines": 20, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradingDashboard } from '../features/trading-dashboard'", "path": "../features/trading-dashboard", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react"], "lastModified": "2025-05-21T08:43:51.310Z"}, {"path": "packages/dashboard/src/pages/DailyGuide.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/pages/DailyGuide.tsx", "size": 857, "lines": 41, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T08:44:49.756Z"}, {"path": "packages/dashboard/src/layouts/Sidebar.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/layouts/Sidebar.tsx", "size": 4311, "lines": 149, "complexity": 10, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { NavLink, useLocation } from \"react-router-dom\"", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components"], "lastModified": "2025-05-21T00:52:12.030Z"}, {"path": "packages/dashboard/src/layouts/MainLayout.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/layouts/MainLayout.tsx", "size": 3211, "lines": 128, "complexity": 6, "imports": [{"statement": "import React, { useState } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Outlet } from \"react-router-dom\"", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import Sidebar from \"./Sidebar\"", "path": "./Sidebar", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import Header from \"./Header\"", "path": "./Header", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components"], "lastModified": "2025-05-21T00:52:11.001Z"}, {"path": "packages/dashboard/src/layouts/Header.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/layouts/Header.tsx", "size": 2653, "lines": 117, "complexity": 2, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T00:52:11.560Z"}, {"path": "packages/dashboard/src/components/NotFound.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/NotFound.tsx", "size": 1813, "lines": 69, "complexity": 1, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Link } from \"react-router-dom\"", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "react-router-dom"], "lastModified": "2025-05-21T06:54:06.278Z"}, {"path": "packages/dashboard/src/components/FeatureErrorBoundary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/FeatureErrorBoundary.tsx", "size": 1414, "lines": 56, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { FeatureErrorBoundary as UnifiedFeatureErrorBoundary } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface FeatureErrorBoundaryProps", "name": "FeatureErrorBoundaryProps", "type": "named", "module": "esm"}, {"statement": "export const FeatureErrorBoundary", "name": "FeatureErrorBoundary", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-23T21:47:27.793Z"}, {"path": "packages/dashboard/src/components/AppErrorBoundary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/AppErrorBoundary.tsx", "size": 1177, "lines": 44, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { AppErrorBoundary as UnifiedAppErrorBoundary } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface AppErrorBoundaryProps", "name": "AppErrorBoundaryProps", "type": "named", "module": "esm"}, {"statement": "export const AppErrorBoundary", "name": "AppError<PERSON>ou<PERSON>ry", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-23T21:47:27.793Z"}, {"path": "packages/shared/src/components/templates/DashboardTemplate.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/templates/DashboardTemplate.tsx", "size": 2501, "lines": 85, "complexity": 7, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface DashboardTemplateProps", "name": "DashboardTemplateProps", "type": "named", "module": "esm"}, {"statement": "export const DashboardTemplate", "name": "DashboardTemplate", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T14:00:57.405Z"}, {"path": "packages/shared/src/components/organisms/DataCard.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/organisms/DataCard.tsx", "size": 2927, "lines": 106, "complexity": 19, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card, CardProps } from '../molecules/Card'", "path": "../molecules/Card", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { LoadingPlaceholder } from '../atoms/LoadingPlaceholder'", "path": "../atoms/LoadingPlaceholder", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { EmptyState } from '../molecules/EmptyState'", "path": "../molecules/EmptyState", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface DataCardProps", "name": "DataCardProps", "type": "named", "module": "esm"}, {"statement": "export const DataCard", "name": "DataCard", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.831Z"}, {"path": "packages/shared/src/components/molecules/UnifiedErrorBoundary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/UnifiedErrorBoundary.tsx", "size": 2062, "lines": 64, "complexity": 4, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ErrorBoundary, ErrorBoundaryProps } from './ErrorBoundary'", "path": "./ErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface UnifiedErrorBoundaryProps", "name": "UnifiedErrorBoundaryProps", "type": "named", "module": "esm"}, {"statement": "export const UnifiedErrorBoundary", "name": "UnifiedErrorBoundary", "type": "named", "module": "esm"}, {"statement": "export const AppErrorBoundary", "name": "AppError<PERSON>ou<PERSON>ry", "type": "named", "module": "esm"}, {"statement": "export const FeatureErrorBoundary", "name": "FeatureErrorBoundary", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-23T21:47:27.821Z"}, {"path": "packages/shared/src/components/molecules/TradeTableRow.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTableRow.tsx", "size": 8477, "lines": 289, "complexity": 74, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TableColumn } from './TradeTableColumns'", "path": "./TradeTableColumns", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableRowProps", "name": "TradeTableRowProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTableRow", "name": "TradeTableRow", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:26:23.474Z"}, {"path": "packages/shared/src/components/molecules/TradeTableFilters.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTableFilters.tsx", "size": 11151, "lines": 349, "complexity": 43, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Input } from '../atoms/Input'", "path": "../atoms/Input", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Select } from '../atoms/Select'", "path": "../atoms/Select", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeFilters } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableFiltersProps", "name": "TradeTableFiltersProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTableFilters", "name": "TradeTableFilters", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T16:54:55.384Z"}, {"path": "packages/shared/src/components/molecules/TradeTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTable.tsx", "size": 12911, "lines": 439, "complexity": 96, "imports": [{"statement": "import React, { useMemo, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CompleteTradeData, TradeFilters } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  TableColumn,\n  getTradeTableColumns,\n  getCompactTradeTableColumns,\n  getPerformanceTradeTableColumns,\n} from './TradeTableColumns'", "path": "./TradeTableColumns", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableRow } from './TradeTableRow'", "path": "./TradeTableRow", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableFilters } from './TradeTableFilters'", "path": "./TradeTableFilters", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableProps", "name": "TradeTableProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTable", "name": "TradeTable", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:25:39.080Z"}, {"path": "packages/shared/src/components/molecules/TradeTable.example.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTable.example.tsx", "size": 9106, "lines": 331, "complexity": 23, "imports": [{"statement": "import React, { useState, useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeTable } from './TradeTable'", "path": "./TradeTable", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CompleteTradeData, TradeFilters } from '../../services/tradeStorage'", "path": "../../services/tradeStorage", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const BasicTradeTableExample", "name": "BasicTradeTableExample", "type": "named", "module": "esm"}, {"statement": "export const FilteredTradeTableExample", "name": "FilteredTradeTableExample", "type": "named", "module": "esm"}, {"statement": "export const CompactTradeTableExample", "name": "CompactTradeTableExample", "type": "named", "module": "esm"}, {"statement": "export const PerformanceTradeTableExample", "name": "PerformanceTradeTableExample", "type": "named", "module": "esm"}, {"statement": "export const TradeTableExamples", "name": "TradeTableExamples", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-24T08:39:50.088Z"}, {"path": "packages/shared/src/components/molecules/Table.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Table.tsx", "size": 13184, "lines": 474, "complexity": 75, "imports": [{"statement": "import React, { useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TableColumn", "name": "TableColumn", "type": "named", "module": "esm"}, {"statement": "export interface TableProps", "name": "TableProps", "type": "named", "module": "esm"}, {"statement": "export function Table", "name": "Table", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T08:35:40.197Z"}, {"path": "packages/shared/src/components/molecules/Modal.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Modal.tsx", "size": 8005, "lines": 314, "complexity": 49, "imports": [{"statement": "import React, { useEffect, useRef } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { createPortal } from 'react-dom'", "path": "react-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css, keyframes } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface ModalProps", "name": "ModalProps", "type": "named", "module": "esm"}, {"statement": "export const Modal", "name": "Modal", "type": "named", "module": "esm"}], "dependencies": ["react", "react-dom", "styled-components"], "lastModified": "2025-05-23T21:47:27.819Z"}, {"path": "packages/shared/src/components/molecules/FormField.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/FormField.tsx", "size": 2827, "lines": 114, "complexity": 19, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface FormFieldProps", "name": "FormFieldProps", "type": "named", "module": "esm"}, {"statement": "export const FormField", "name": "FormField", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.817Z"}, {"path": "packages/shared/src/components/molecules/ErrorBoundary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/ErrorBoundary.tsx", "size": 8294, "lines": 306, "complexity": 41, "imports": [{"statement": "import React, { Component, ErrorInfo, ReactNode } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface ErrorBoundaryProps", "name": "ErrorBoundaryProps", "type": "named", "module": "esm"}, {"statement": "export class ErrorBoundary", "name": "Error<PERSON>ou<PERSON><PERSON>", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.816Z"}, {"path": "packages/shared/src/components/molecules/EmptyState.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/EmptyState.tsx", "size": 4808, "lines": 193, "complexity": 20, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export type EmptyStateVariant", "name": "EmptyStateVariant", "type": "named", "module": "esm"}, {"statement": "export type EmptyStateSize", "name": "EmptyStateSize", "type": "named", "module": "esm"}, {"statement": "export interface EmptyStateProps", "name": "EmptyStateProps", "type": "named", "module": "esm"}, {"statement": "export const EmptyState", "name": "EmptyState", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T22:47:54.839Z"}, {"path": "packages/shared/src/components/molecules/Card.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Card.tsx", "size": 6332, "lines": 262, "complexity": 26, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type CardVariant", "name": "CardVariant", "type": "named", "module": "esm"}, {"statement": "export type CardPadding", "name": "CardPadding", "type": "named", "module": "esm"}, {"statement": "export interface CardProps", "name": "CardProps", "type": "named", "module": "esm"}, {"statement": "export const Card", "name": "Card", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T17:30:13.187Z"}, {"path": "packages/shared/src/components/atoms/Tag.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Tag.tsx", "size": 4929, "lines": 197, "complexity": 16, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type TagVariant", "name": "TagVariant", "type": "named", "module": "esm"}, {"statement": "export type TagSize", "name": "TagSize", "type": "named", "module": "esm"}, {"statement": "export interface TagProps", "name": "TagProps", "type": "named", "module": "esm"}, {"statement": "export const Tag", "name": "Tag", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T16:37:58.512Z"}, {"path": "packages/shared/src/components/atoms/StatusIndicator.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/StatusIndicator.tsx", "size": 4069, "lines": 180, "complexity": 19, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type StatusType", "name": "StatusType", "type": "named", "module": "esm"}, {"statement": "export type StatusSize", "name": "StatusSize", "type": "named", "module": "esm"}, {"statement": "export interface StatusIndicatorProps", "name": "StatusIndicatorProps", "type": "named", "module": "esm"}, {"statement": "export const StatusIndicator", "name": "StatusIndicator", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T16:38:21.331Z"}, {"path": "packages/shared/src/components/atoms/Select.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Select.tsx", "size": 9863, "lines": 358, "complexity": 61, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface SelectOption", "name": "SelectOption", "type": "named", "module": "esm"}, {"statement": "export interface SelectProps", "name": "SelectProps", "type": "named", "module": "esm"}, {"statement": "export const Select", "name": "Select", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.815Z"}, {"path": "packages/shared/src/components/atoms/Select.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Select.stories.tsx", "size": 6052, "lines": 253, "complexity": 2, "imports": [{"statement": "import { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Select } from './Select'", "path": "./Select", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const WithLabel", "name": "With<PERSON>abel", "type": "named", "module": "esm"}, {"statement": "export const WithHelperText", "name": "WithHelperText", "type": "named", "module": "esm"}, {"statement": "export const WithError", "name": "WithE<PERSON>r", "type": "named", "module": "esm"}, {"statement": "export const WithSuccess", "name": "WithSuccess", "type": "named", "module": "esm"}, {"statement": "export const Disabled", "name": "Disabled", "type": "named", "module": "esm"}, {"statement": "export const Required", "name": "Required", "type": "named", "module": "esm"}, {"statement": "export const WithIcon", "name": "WithIcon", "type": "named", "module": "esm"}, {"statement": "export const GroupedOptions", "name": "GroupedOptions", "type": "named", "module": "esm"}, {"statement": "export const WithDisabledOptions", "name": "WithDisabledOptions", "type": "named", "module": "esm"}, {"statement": "export const Small", "name": "Small", "type": "named", "module": "esm"}, {"statement": "export const Large", "name": "Large", "type": "named", "module": "esm"}, {"statement": "export const AllVariants", "name": "AllVariants", "type": "named", "module": "esm"}], "dependencies": ["react", "@storybook/react"], "lastModified": "2025-05-22T14:01:54.871Z"}, {"path": "packages/shared/src/components/atoms/LoadingPlaceholder.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/LoadingPlaceholder.tsx", "size": 3596, "lines": 144, "complexity": 14, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css, keyframes } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type LoadingPlaceholderVariant", "name": "LoadingPlaceholderVariant", "type": "named", "module": "esm"}, {"statement": "export type LoadingPlaceholderSize", "name": "LoadingPlaceholderSize", "type": "named", "module": "esm"}, {"statement": "export interface LoadingPlaceholderProps", "name": "LoadingPlaceholderProps", "type": "named", "module": "esm"}, {"statement": "export const LoadingPlaceholder", "name": "LoadingPlaceholder", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:57:10.778Z"}, {"path": "packages/shared/src/components/atoms/Input.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Input.tsx", "size": 9450, "lines": 373, "complexity": 78, "imports": [{"statement": "import React, { useState, useRef } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface InputProps", "name": "InputProps", "type": "named", "module": "esm"}, {"statement": "export const Input", "name": "Input", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:59:57.382Z"}, {"path": "packages/shared/src/components/atoms/Input.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Input.stories.tsx", "size": 5222, "lines": 234, "complexity": 2, "imports": [{"statement": "import { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Input } from './Input'", "path": "./Input", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const WithLabel", "name": "With<PERSON>abel", "type": "named", "module": "esm"}, {"statement": "export const WithHelperText", "name": "WithHelperText", "type": "named", "module": "esm"}, {"statement": "export const WithError", "name": "WithE<PERSON>r", "type": "named", "module": "esm"}, {"statement": "export const WithSuccess", "name": "WithSuccess", "type": "named", "module": "esm"}, {"statement": "export const Disabled", "name": "Disabled", "type": "named", "module": "esm"}, {"statement": "export const Required", "name": "Required", "type": "named", "module": "esm"}, {"statement": "export const WithIcons", "name": "WithIcons", "type": "named", "module": "esm"}, {"statement": "export const Clearable", "name": "Clearable", "type": "named", "module": "esm"}, {"statement": "export const WithCharCount", "name": "WithCharCount", "type": "named", "module": "esm"}, {"statement": "export const Small", "name": "Small", "type": "named", "module": "esm"}, {"statement": "export const Large", "name": "Large", "type": "named", "module": "esm"}, {"statement": "export const FullWidth", "name": "FullWidth", "type": "named", "module": "esm"}, {"statement": "export const AllVariants", "name": "AllVariants", "type": "named", "module": "esm"}], "dependencies": ["react", "@storybook/react"], "lastModified": "2025-05-22T14:01:43.029Z"}, {"path": "packages/shared/src/components/atoms/Button.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Button.tsx", "size": 7141, "lines": 275, "complexity": 34, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css, keyframes } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface ButtonProps", "name": "ButtonProps", "type": "named", "module": "esm"}, {"statement": "export const Button", "name": "<PERSON><PERSON>", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.814Z"}, {"path": "packages/shared/src/components/atoms/Badge.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Badge.tsx", "size": 7337, "lines": 265, "complexity": 63, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type BadgeVariant", "name": "BadgeVariant", "type": "named", "module": "esm"}, {"statement": "export type BadgeSize", "name": "BadgeSize", "type": "named", "module": "esm"}, {"statement": "export interface BadgeProps", "name": "BadgeProps", "type": "named", "module": "esm"}, {"statement": "export const Badge", "name": "Badge", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:56:41.405Z"}, {"path": "packages/dashboard/src/routes/layouts/MainLayout.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/layouts/MainLayout.tsx", "size": 2640, "lines": 101, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Outlet, NavLink as RouterNavLink } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components"], "lastModified": "2025-05-23T21:47:27.802Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx", "size": 5890, "lines": 212, "complexity": 10, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTradingDashboard } from './hooks/useTradingDashboard'", "path": "./hooks/useTradingDashboard", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import MetricsPanel from './components/MetricsPanel'", "path": "./components/MetricsPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import PerformanceChart from './components/PerformanceChart'", "path": "./components/PerformanceChart", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import RecentTradesTable from './components/RecentTradesTable'", "path": "./components/RecentTradesTable", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import SetupAnalysis from './components/SetupAnalysis'", "path": "./components/SetupAnalysis", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const TradingDashboard", "name": "TradingDashboard", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-25T09:22:17.192Z"}, {"path": "packages/dashboard/src/features/trade-journal/TradeJournal.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/TradeJournal.tsx", "size": 2046, "lines": 72, "complexity": 1, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTradeJournal } from './hooks/useTradeJournal'", "path": "./hooks/useTradeJournal", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeFilters } from './hooks/useTradeFilters'", "path": "./hooks/useTradeFilters", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeJournalHeader, TradeJournalContent } from './components/trade-journal'", "path": "./components/trade-journal", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:25:27.393Z"}, {"path": "packages/dashboard/src/features/trade-journal/TradeForm.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/TradeForm.tsx", "size": 5540, "lines": 187, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useParams } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTradeForm } from './hooks/useTradeForm'", "path": "./hooks/useTradeForm", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import TabPanel from './components/TabPanel'", "path": "./components/TabPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import PatternQualityAssessment from './components/trade-pattern-quality/PatternQualityAssessment'", "path": "./components/trade-pattern-quality/PatternQualityAssessment", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLAnalysis from './components/trade-dol-analysis/TradeDOLAnalysis'", "path": "./components/trade-dol-analysis/TradeDOLAnalysis", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  TradeFormHeader,\n  TradeFormBasicFields,\n  TradeFormTimingFields,\n  TradeFormRiskFields,\n  TradeFormStrategyFields,\n  TradeFormActions,\n  TradeFormMessages,\n  TradeFormLoading,\n} from './components/trade-form'", "path": "./components/trade-form", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:26:27.729Z"}, {"path": "packages/dashboard/src/features/trade-analysis/TradeAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/TradeAnalysis.tsx", "size": 9114, "lines": 274, "complexity": 32, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DataCard } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeAnalysisProvider, useTradeAnalysis } from './hooks/TradeAnalysisContext'", "path": "./hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { FilterPanel } from './components/FilterPanel'", "path": "./components/FilterPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { PerformanceSummary } from './components/PerformanceSummary'", "path": "./components/PerformanceSummary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradesTable } from './components/TradesTable'", "path": "./components/TradesTable", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CategoryPerformanceChart } from './components/CategoryPerformanceChart'", "path": "./components/CategoryPerformanceChart", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TimePerformanceChart } from './components/TimePerformanceChart'", "path": "./components/TimePerformanceChart", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeDetail } from './components/TradeDetail'", "path": "./components/TradeDetail", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:32:09.772Z"}, {"path": "packages/dashboard/src/features/settings/Settings.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/Settings.tsx", "size": 7819, "lines": 292, "complexity": 1, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useSettings } from \"./hooks/useSettings\"", "path": "./hooks/useSettings", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-25T09:22:17.191Z"}, {"path": "packages/dashboard/src/features/performance-dashboard/Dashboard.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/performance-dashboard/Dashboard.tsx", "size": 2805, "lines": 99, "complexity": 1, "imports": [{"statement": "import React, { useEffect } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MetricsPanel } from \"./components/MetricsPanel\"", "path": "./components/MetricsPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { PerformanceChart } from \"./components/PerformanceChart\"", "path": "./components/PerformanceChart", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { RecentTradesPanel } from \"./components/RecentTradesPanel\"", "path": "./components/RecentTradesPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useDashboardData } from \"./hooks/useDashboardData\"", "path": "./hooks/useDashboardData", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T06:54:05.535Z"}, {"path": "packages/dashboard/src/features/daily-guide/DailyGuide.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/DailyGuide.tsx", "size": 2945, "lines": 121, "complexity": 5, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketOverview } from './components/MarketOverview'", "path": "./components/MarketOverview", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradingPlan } from './components/TradingPlan'", "path": "./components/TradingPlan", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { KeyLevels } from './components/KeyLevels'", "path": "./components/KeyLevels", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { MarketNews } from './components/MarketNews'", "path": "./components/MarketNews", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { SectionCard } from './components/ui'", "path": "./components/ui", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext'", "path": "./context/DailyGuideContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-25T09:22:17.183Z"}, {"path": "packages/dashboard/src/components/molecules/ProfitLossCell.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/molecules/ProfitLossCell.tsx", "size": 6098, "lines": 210, "complexity": 51, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface ProfitLossCellProps", "name": "ProfitLossCellProps", "type": "named", "module": "esm"}, {"statement": "export const ProfitLossCell", "name": "ProfitLossCell", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-25T08:25:57.843Z"}, {"path": "packages/dashboard/src/components/molecules/LoadingScreen.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/molecules/LoadingScreen.tsx", "size": 1097, "lines": 53, "complexity": 1, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T00:52:11.145Z"}, {"path": "packages/dashboard/src/routes/components/molecules/LoadingScreen.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/components/molecules/LoadingScreen.tsx", "size": 1227, "lines": 55, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T08:44:40.810Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/components/SetupAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/components/SetupAnalysis.tsx", "size": 5745, "lines": 193, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { SetupPerformance, SessionPerformance } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const SetupAnalysis", "name": "SetupAnalysis", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:43:43.923Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/components/RecentTradesTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/components/RecentTradesTable.tsx", "size": 4437, "lines": 158, "complexity": 8, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const RecentTradesTable", "name": "RecentTradesTable", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:43:31.633Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/components/PerformanceChart.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/components/PerformanceChart.tsx", "size": 3868, "lines": 160, "complexity": 8, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  Legend\n} from 'recharts'", "path": "recharts", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ChartDataPoint } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const PerformanceChart", "name": "Performance<PERSON>hart", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "recharts"], "lastModified": "2025-05-21T08:41:39.774Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/components/MetricsPanel.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/components/MetricsPanel.tsx", "size": 2708, "lines": 103, "complexity": 7, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { PerformanceMetric } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const MetricsPanel", "name": "MetricsPanel", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T08:41:18.432Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/TradeList.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/TradeList.tsx", "size": 5847, "lines": 209, "complexity": 29, "imports": [{"statement": "import React, { useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeList } from '../hooks/useTradeList'", "path": "../hooks/useTradeList", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  TradeListHeader,\n  TradeListRow,\n  TradeListExpandedRow,\n  TradeListEmpty,\n  TradeListLoading,\n} from './trade-list'", "path": "./trade-list", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeColumn", "name": "TradeColumn", "type": "named", "module": "esm"}], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:31:45.290Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/TimePicker.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/TimePicker.tsx", "size": 2247, "lines": 99, "complexity": 10, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T19:19:12.284Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/SelectDropdown.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/SelectDropdown.tsx", "size": 2501, "lines": 109, "complexity": 9, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T19:19:26.498Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradesTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradesTable.tsx", "size": 9827, "lines": 330, "complexity": 38, "imports": [{"statement": "import React, { useState, useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeDirection, TradeStatus } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Badge, Tag } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const TradesTable", "name": "TradesTable", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:15:43.580Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeDetail.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeDetail.tsx", "size": 6955, "lines": 240, "complexity": 16, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Badge, Card, Tag } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const TradeDetail", "name": "TradeDetail", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:14:52.715Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeAnalysisTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeAnalysisTable.tsx", "size": 4325, "lines": 160, "complexity": 14, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Table, Card, Badge } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, TradeSort } from '../hooks/tradeAnalysisState'", "path": "../hooks/tradeAnalysisState", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeAnalysisTableProps", "name": "TradeAnalysisTableProps", "type": "named", "module": "esm"}, {"statement": "export const TradeAnalysisTable", "name": "TradeAnalysisTable", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:15:30.590Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeAnalysisSummary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeAnalysisSummary.tsx", "size": 3867, "lines": 137, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeSummary } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeAnalysisSummaryProps", "name": "TradeAnalysisSummaryProps", "type": "named", "module": "esm"}, {"statement": "export const TradeAnalysisSummary", "name": "TradeAnalysisSummary", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T18:31:49.089Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeAnalysisFilter.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeAnalysisFilter.tsx", "size": 4623, "lines": 165, "complexity": 13, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  Card, \n  Input, \n  Select, \n  Button, \n  FormField \n} from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFilter } from '../state/tradeAnalysisState'", "path": "../state/tradeAnalysisState", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeAnalysisFilterProps", "name": "TradeAnalysisFilterProps", "type": "named", "module": "esm"}, {"statement": "export const TradeAnalysisFilter", "name": "TradeAnalysisFilter", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T18:23:56.835Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeAnalysisCharts.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeAnalysisCharts.tsx", "size": 11475, "lines": 304, "complexity": 7, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { EquityPoint, DistributionBar } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeAnalysisChartsProps", "name": "TradeAnalysisChartsProps", "type": "named", "module": "esm"}, {"statement": "export const TradeAnalysisCharts", "name": "TradeAnalysisCharts", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T18:32:39.451Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TradeAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TradeAnalysis.tsx", "size": 3401, "lines": 144, "complexity": 5, "imports": [{"statement": "import React, { useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeAnalysisProvider } from '../hooks/tradeAnalysisState'", "path": "../hooks/tradeAnalysisState", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/useTradeAnalysis'", "path": "../hooks/useTradeAnalysis", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeAnalysisFilter } from './TradeAnalysisFilter'", "path": "./TradeAnalysisFilter", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeAnalysisTable } from './TradeAnalysisTable'", "path": "./TradeAnalysisTable", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeAnalysisSummary } from './TradeAnalysisSummary'", "path": "./TradeAnalysisSummary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeAnalysisCharts } from './TradeAnalysisCharts'", "path": "./TradeAnalysisCharts", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeAnalysisProps", "name": "TradeAnalysisProps", "type": "named", "module": "esm"}, {"statement": "export const TradeAnalysis", "name": "TradeAnalysis", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:15:06.256Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/TimePerformanceChart.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/TimePerformanceChart.tsx", "size": 4982, "lines": 171, "complexity": 11, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TimePerformance } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const TimePerformanceChart", "name": "TimePerformanceChart", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:32:54.262Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/PerformanceSummary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/PerformanceSummary.tsx", "size": 4405, "lines": 140, "complexity": 9, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { PerformanceMetrics } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const PerformanceSummary", "name": "PerformanceSummary", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:32:36.924Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/MetricsPanel.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/MetricsPanel.tsx", "size": 2990, "lines": 110, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T13:57:02.328Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/FilterPanel.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/FilterPanel.tsx", "size": 9223, "lines": 313, "complexity": 16, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { <PERSON><PERSON>, <PERSON>, Tag } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const FilterPanel", "name": "FilterPanel", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:36:52.323Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/EquityCurve.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/EquityCurve.tsx", "size": 5222, "lines": 192, "complexity": 8, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface EquityPoint", "name": "EquityPoint", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T13:56:49.579Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/DistributionChart.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/DistributionChart.tsx", "size": 4387, "lines": 156, "complexity": 5, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface DistributionBar", "name": "DistributionBar", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T13:56:36.836Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/CategoryPerformanceChart.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/CategoryPerformanceChart.tsx", "size": 7945, "lines": 271, "complexity": 32, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CategoryPerformance } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeAnalysis } from '../hooks/TradeAnalysisContext'", "path": "../hooks/TradeAnalysisContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const CategoryPerformanceChart", "name": "CategoryPerformanceChart", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:32:18.900Z"}, {"path": "packages/dashboard/src/features/settings/components/ToggleSwitch.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/components/ToggleSwitch.tsx", "size": 1540, "lines": 85, "complexity": 2, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T07:07:49.922Z"}, {"path": "packages/dashboard/src/features/settings/components/SettingsSection.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/components/SettingsSection.tsx", "size": 1074, "lines": 48, "complexity": 1, "imports": [{"statement": "import React, { ReactNode } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T07:07:49.166Z"}, {"path": "packages/dashboard/src/features/settings/components/SettingItem.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/components/SettingItem.tsx", "size": 1527, "lines": 71, "complexity": 3, "imports": [{"statement": "import React, { ReactNode } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T07:07:49.176Z"}, {"path": "packages/dashboard/src/features/performance-dashboard/components/RecentTradesPanel.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/performance-dashboard/components/RecentTradesPanel.tsx", "size": 3685, "lines": 137, "complexity": 16, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const RecentTradesPanel", "name": "RecentTradesPanel", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T13:55:45.784Z"}, {"path": "packages/dashboard/src/features/performance-dashboard/components/PerformanceChart.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/performance-dashboard/components/PerformanceChart.tsx", "size": 1318, "lines": 55, "complexity": 3, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const PerformanceChart", "name": "Performance<PERSON>hart", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T06:54:06.201Z"}, {"path": "packages/dashboard/src/features/performance-dashboard/components/MetricsPanel.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/performance-dashboard/components/MetricsPanel.tsx", "size": 1827, "lines": 78, "complexity": 3, "imports": [{"statement": "import React from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from \"styled-components\"", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const MetricsPanel", "name": "MetricsPanel", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T06:54:06.199Z"}, {"path": "packages/dashboard/src/features/daily-guide/context/DailyGuideContext.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/context/DailyGuideContext.tsx", "size": 3555, "lines": 145, "complexity": 11, "imports": [{"statement": "import React, {\n  createContext,\n  useContext,\n  useReducer,\n  ReactNode,\n  useCallback,\n  useEffect,\n} from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  DailyGuideState,\n  DailyGuideAction,\n  // Using these types but not directly referencing them in variable declarations\n  // DailyGuideData,\n  // MarketData,\n  // TradingPlanItem,\n  // KeyLevel,\n  // MarketNews\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { fetchDailyGuideData } from '../api/dailyGuideApi'", "path": "../api/dailyGuideApi", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const DailyGuideProvider", "name": "DailyGuideProvider", "type": "named", "module": "esm"}, {"statement": "export const useDailyGuide", "name": "useDailyGuide", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-25T09:22:17.189Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx", "size": 10971, "lines": 371, "complexity": 27, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card, Badge, Button, Input, FormField } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradingPlan as TradingPlanType, TradingPlanItem, TradingPlanPriority } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface TradingPlanProps", "name": "TradingPlanProps", "type": "named", "module": "esm"}, {"statement": "export const TradingPlan", "name": "TradingPlan", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared", "styled-components"], "lastModified": "2025-05-25T08:25:57.845Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/MarketSummary.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/MarketSummary.tsx", "size": 2428, "lines": 104, "complexity": 8, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Badge } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketSentiment } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface MarketSummaryProps", "name": "MarketSummaryProps", "type": "named", "module": "esm"}, {"statement": "export const MarketSummary", "name": "MarketSummary", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T09:49:33.468Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/MarketOverview.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/MarketOverview.tsx", "size": 3362, "lines": 128, "complexity": 13, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketOverview as MarketOverviewType } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketSummary } from './MarketSummary'", "path": "./MarketSummary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { MarketIndicators } from './MarketIndicators'", "path": "./MarketIndicators", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { MarketNews } from './MarketNews'", "path": "./MarketNews", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface MarketOverviewProps", "name": "MarketOverviewProps", "type": "named", "module": "esm"}, {"statement": "export const MarketOverview", "name": "MarketOverview", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared", "styled-components"], "lastModified": "2025-05-24T09:49:08.936Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/MarketNews.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/MarketNews.tsx", "size": 4920, "lines": 176, "complexity": 11, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Badge } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { EconomicEvent } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface MarketNewsProps", "name": "MarketNewsProps", "type": "named", "module": "esm"}, {"statement": "export const MarketNews", "name": "MarketNews", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T09:51:46.089Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/MarketIndicators.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/MarketIndicators.tsx", "size": 3052, "lines": 102, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketIndex } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface MarketIndicatorsProps", "name": "MarketIndicatorsProps", "type": "named", "module": "esm"}, {"statement": "export const MarketIndicators", "name": "MarketIndicators", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:49:52.831Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/KeyLevels.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/KeyLevels.tsx", "size": 5328, "lines": 201, "complexity": 17, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card, Badge } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { KeyPriceLevel } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface KeyLevelsProps", "name": "KeyLevelsProps", "type": "named", "module": "esm"}, {"statement": "export const KeyLevels", "name": "KeyLevels", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared", "styled-components"], "lastModified": "2025-05-25T09:22:17.188Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/SetupClassificationSection.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/SetupClassificationSection.tsx", "size": 3669, "lines": 136, "complexity": 14, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import PrimarySetupSelector from './PrimarySetupSelector'", "path": "./PrimarySetupSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import SecondarySetupSelector from './SecondarySetupSelector'", "path": "./SecondarySetupSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import LiquiditySelector from './LiquiditySelector'", "path": "./LiquiditySelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import FVGSelector from './FVGSelector'", "path": "./FVGSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLTargetSelector from './DOLTargetSelector'", "path": "./DOLTargetSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import ParentPDArraySelector from './ParentPDArraySelector'", "path": "./ParentPDArraySelector", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:30:59.671Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/SecondarySetupSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/SecondarySetupSelector.tsx", "size": 5557, "lines": 194, "complexity": 11, "imports": [{"statement": "import React, { useEffect, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  SETUP_CATEGORY_OPTIONS, \n  getSetupOptionsByCategory \n} from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:29:06.507Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/PrimarySetupSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/PrimarySetupSelector.tsx", "size": 5163, "lines": 176, "complexity": 7, "imports": [{"statement": "import React, { useEffect, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  SETUP_CATEGORY_OPTIONS, \n  getSetupOptionsByCategory \n} from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:28:40.676Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/ParentPDArraySelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/ParentPDArraySelector.tsx", "size": 2816, "lines": 99, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { PARENT_PD_ARRAY_OPTIONS } from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:30:39.709Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/LiquiditySelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/LiquiditySelector.tsx", "size": 2726, "lines": 98, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { LIQUIDITY_OPTIONS } from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:29:23.906Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/FVGSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/FVGSelector.tsx", "size": 6746, "lines": 218, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  TIME_BASED_FVG_OPTIONS,\n  CURRENT_SESSION_FVG_OPTIONS,\n  PREV_DAY_FVG_OPTIONS,\n  THREE_DAY_FVG_OPTIONS,\n  SPECIAL_FVG_OPTIONS\n} from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:29:53.921Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-setup-classification/DOLTargetSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-setup-classification/DOLTargetSelector.tsx", "size": 5460, "lines": 183, "complexity": 11, "imports": [{"statement": "import React, { useEffect, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  DOL_TARGET_OPTIONS, \n  getDOLTargetOptions \n} from '../../constants/setupClassification'", "path": "../../constants/setupClassification", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T22:30:21.609Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx", "size": 8486, "lines": 289, "complexity": 27, "imports": [{"statement": "import React, { useEffect, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import CriterionSelector from './CriterionSelector'", "path": "./CriterionSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  PATTERN_QUALITY_CRITERIA,\n  calculateTotalScore,\n  convertScoreToRating,\n  getRatingDescription,\n  getRatingColor,\n} from '../../constants/patternQuality'", "path": "../../constants/patternQuality", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ScoreRange } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:03:50.993Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx", "size": 3442, "lines": 125, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ScoreRange } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { SCORE_RANGE_OPTIONS, PATTERN_QUALITY_CRITERIA } from '../../constants/patternQuality'", "path": "../../constants/patternQuality", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:03:03.866Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/TradeListRow.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/TradeListRow.tsx", "size": 1472, "lines": 54, "complexity": 4, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeColumn } from '../TradeList'", "path": "../TradeList", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:33:12.795Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/TradeListLoading.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/TradeListLoading.tsx", "size": 1324, "lines": 58, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { keyframes } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:40:01.720Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/TradeListHeader.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/TradeListHeader.tsx", "size": 1251, "lines": 51, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeColumn } from '../TradeList'", "path": "../TradeList", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:39:49.210Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/TradeListExpandedRow.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/TradeListExpandedRow.tsx", "size": 6803, "lines": 205, "complexity": 24, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Link } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:35:22.683Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/TradeListEmpty.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/TradeListEmpty.tsx", "size": 2224, "lines": 76, "complexity": 5, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Link } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:38:28.896Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalHeader.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalHeader.tsx", "size": 2662, "lines": 97, "complexity": 4, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Link } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:38:04.680Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalFilters.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalFilters.tsx", "size": 9725, "lines": 322, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { FilterState } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:37:51.899Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormLoading.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormLoading.tsx", "size": 1544, "lines": 70, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:35:56.289Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormHeader.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormHeader.tsx", "size": 1055, "lines": 45, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:35:45.169Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormActions.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormActions.tsx", "size": 2114, "lines": 86, "complexity": 4, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:34:53.133Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/TradeDOLAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/TradeDOLAnalysis.tsx", "size": 3848, "lines": 148, "complexity": 18, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import DOLTypeSelector from './DOLTypeSelector'", "path": "./DOLTypeSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLStrengthSelector from './DOLStrengthSelector'", "path": "./DOLStrengthSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLReactionSelector from './DOLReactionSelector'", "path": "./DOLReactionSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLContextSelector from './DOLContextSelector'", "path": "./DOLContextSelector", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLDetailedAnalysis from './DOLDetailedAnalysis'", "path": "./DOLDetailedAnalysis", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import DOLEffectivenessRating from './DOLEffectivenessRating'", "path": "./DOLEffectivenessRating", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:29:51.133Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLEffectivenessRating.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLEffectivenessRating.tsx", "size": 5152, "lines": 198, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  DOL_EFFECTIVENESS_OPTIONS, \n  getDOLEffectivenessColor,\n  getDOLEffectivenessDescription\n} from '../../constants/dolAnalysis'", "path": "../../constants/dolAnalysis", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:29:29.943Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLDetailedAnalysis.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLDetailedAnalysis.tsx", "size": 5143, "lines": 159, "complexity": 15, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { \n  DOL_PRICE_ACTION_DESCRIPTIONS, \n  DOL_VOLUME_PROFILE_DESCRIPTIONS,\n  DOL_TIME_OF_DAY_DESCRIPTION,\n  DOL_MARKET_STRUCTURE_DESCRIPTION\n} from '../../constants/dolAnalysis'", "path": "../../constants/dolAnalysis", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:29:03.415Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLContextSelector.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLContextSelector.tsx", "size": 3076, "lines": 112, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DOL_CONTEXT_OPTIONS } from '../../constants/dolAnalysis'", "path": "../../constants/dolAnalysis", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T23:28:39.073Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/ui/SentimentBadge.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/ui/SentimentBadge.tsx", "size": 1496, "lines": 57, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MarketSentiment } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const SentimentBadge", "name": "SentimentBadge", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T14:59:23.536Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/ui/SectionCard.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/ui/SectionCard.tsx", "size": 2495, "lines": 96, "complexity": 7, "imports": [{"statement": "import React, { ReactNode } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const SectionCard", "name": "SectionCard", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T14:59:12.142Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/ui/PriorityTag.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/ui/PriorityTag.tsx", "size": 1401, "lines": 56, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradingPlanPriority } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const PriorityTag", "name": "PriorityTag", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-21T14:59:34.594Z"}], "hooks": [{"path": "packages/shared/src/hooks/usePagination.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/usePagination.ts", "size": 4789, "lines": 168, "complexity": 13, "imports": [{"statement": "import { useState, useMemo, useCallback } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useLocalStorage } from './useLocalStorage'", "path": "./useLocalStorage", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface PaginationOptions", "name": "PaginationOptions", "type": "named", "module": "esm"}, {"statement": "export interface PaginationState", "name": "PaginationState", "type": "named", "module": "esm"}, {"statement": "export interface PaginationActions", "name": "PaginationActions", "type": "named", "module": "esm"}, {"statement": "export function usePagination", "name": "usePagination", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T16:44:28.846Z"}, {"path": "packages/shared/src/hooks/useLocalStorage.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/useLocalStorage.ts", "size": 2313, "lines": 77, "complexity": 9, "imports": [{"statement": "import { useState, useEffect } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export function useLocalStorage", "name": "useLocalStorage", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T00:34:42.962Z"}, {"path": "packages/shared/src/hooks/useErrorHandler.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/useErrorHandler.ts", "size": 2907, "lines": 113, "complexity": 13, "imports": [{"statement": "import { useState, useCallback, useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface ErrorHandlerOptions", "name": "ErrorHandlerOptions", "type": "named", "module": "esm"}, {"statement": "export function useErrorHandler", "name": "useErrorHandler", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-22T16:44:21.919Z"}, {"path": "packages/shared/src/hooks/useDebounce.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/useDebounce.ts", "size": 765, "lines": 32, "complexity": 1, "imports": [{"statement": "import { useState, useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export function useDebounce", "name": "useDebounce", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T16:44:07.154Z"}, {"path": "packages/shared/src/hooks/useAsyncData.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/useAsyncData.ts", "size": 2200, "lines": 85, "complexity": 6, "imports": [{"statement": "import { useState, useCallback, useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface AsyncDataState", "name": "AsyncDataState", "type": "named", "module": "esm"}, {"statement": "export interface UseAsyncDataOptions", "name": "UseAsyncDataOptions", "type": "named", "module": "esm"}, {"statement": "export function useAsyncData", "name": "useAsyncData", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-21T16:43:59.008Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/hooks/useTradingDashboard.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/hooks/useTradingDashboard.ts", "size": 8024, "lines": 292, "complexity": 10, "imports": [{"statement": "import { useState, useEffect, useCallback } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  PerformanceMetric,\n  ChartDataPoint,\n  SetupPerformance,\n  SessionPerformance,\n  DashboardState,\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const useTradingDashboard", "name": "useTradingDashboard", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:44:14.796Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeValidation.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeValidation.ts", "size": 5645, "lines": 176, "complexity": 37, "imports": [{"statement": "import { useState, useCallback } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface ValidationErrors", "name": "ValidationErrors", "type": "named", "module": "esm"}, {"statement": "export function useTradeValidation", "name": "useTradeValidation", "type": "named", "module": "esm"}, {"statement": "export type TradeValidationHook", "name": "TradeValidationHook", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:42:30.529Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeSubmission.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeSubmission.ts", "size": 9368, "lines": 248, "complexity": 42, "imports": [{"statement": "import { useState, useCallback } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { tradeStorageService, Trade } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export function useTradeSubmission", "name": "useTradeSubmission", "type": "named", "module": "esm"}, {"statement": "export type TradeSubmissionHook", "name": "TradeSubmissionHook", "type": "named", "module": "esm"}], "dependencies": ["react", "react-router-dom", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:15:37.197Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeList.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeList.ts", "size": 1421, "lines": 55, "complexity": 4, "imports": [{"statement": "import { useState, useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export function useTradeList", "name": "useTradeList", "type": "named", "module": "esm"}, {"statement": "export type TradeListHook", "name": "TradeListHook", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:42:18.067Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeJournal.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeJournal.ts", "size": 2328, "lines": 97, "complexity": 3, "imports": [{"statement": "import { useState, useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData, TradeJournalState } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { tradeStorageService } from '@adhd-trading-dashboard/shared/services'", "path": "@adhd-trading-dashboard/shared/services", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const useTradeJournal", "name": "useTradeJournal", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T09:20:14.813Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeForm.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeForm.ts", "size": 6201, "lines": 189, "complexity": 13, "imports": [{"statement": "import { useState, useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useNavigate } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTradeFormData } from './useTradeFormData'", "path": "./useTradeFormData", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeValidation } from './useTradeValidation'", "path": "./useTradeValidation", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeCalculations } from './useTradeCalculations'", "path": "./useTradeCalculations", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { useTradeSubmission } from './useTradeSubmission'", "path": "./useTradeSubmission", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const MODEL_TYPE_OPTIONS", "name": "MODEL_TYPE_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const SESSION_OPTIONS", "name": "SESSION_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const SETUP_OPTIONS", "name": "SETUP_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const MARKET_OPTIONS", "name": "MARKET_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const ENTRY_VERSION_OPTIONS", "name": "ENTRY_VERSION_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const PATTERN_QUALITY_OPTIONS", "name": "PATTERN_QUALITY_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const useTradeForm", "name": "useTradeForm", "type": "named", "module": "esm"}], "dependencies": ["react", "react-router-dom", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:42:06.006Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeFilters.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeFilters.ts", "size": 6830, "lines": 239, "complexity": 45, "imports": [{"statement": "import { useState, useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { FilterState, CompleteTradeData } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export function useTradeFilters", "name": "useTradeFilters", "type": "named", "module": "esm"}, {"statement": "export type TradeFiltersHook", "name": "TradeFiltersHook", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:41:27.527Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/useTradeCalculations.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/useTradeCalculations.ts", "size": 2442, "lines": 77, "complexity": 12, "imports": [{"statement": "import { useEffect } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export function useTradeCalculations", "name": "useTradeCalculations", "type": "named", "module": "esm"}, {"statement": "export type TradeCalculationsHook", "name": "TradeCalculationsHook", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:41:09.783Z"}, {"path": "packages/dashboard/src/features/settings/hooks/useSettings.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/hooks/useSettings.ts", "size": 1265, "lines": 65, "complexity": 2, "imports": [{"statement": "import { useState } from \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTheme } from \"@adhd-trading-dashboard/shared\"", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export const useSettings", "name": "useSettings", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T07:07:49.508Z"}, {"path": "packages/dashboard/src/features/daily-guide/hooks/useDailyGuide.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/hooks/useDailyGuide.ts", "size": 9174, "lines": 337, "complexity": 3, "imports": [{"statement": "import { useEffect, useCallback, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  useDailyGuideStore,\n  useDailyGuideSelector,\n  useDailyGuideActions,\n  dailyGuideActions,\n  dailyGuideSelectors,\n} from '../state'", "path": "../state", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { DailyGuideData, TradingPlanItem } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export function useDailyGuide", "name": "useDailyGuide", "type": "named", "module": "esm"}], "dependencies": ["react"], "lastModified": "2025-05-23T22:30:15.272Z"}], "utilities": [{"path": "vitest.setup.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/vitest.setup.ts", "size": 1617, "lines": 57, "complexity": 4, "imports": [{"statement": "import '@testing-library/jest-dom'", "path": "@testing-library/jest-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { f1Theme } from './packages/shared/src/theme/f1Theme'", "path": "./packages/shared/src/theme/f1Theme", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["@testing-library/jest-dom", "vitest", "react"], "lastModified": "2025-05-25T08:08:20.451Z"}, {"path": "vitest.config.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/vitest.config.ts", "size": 917, "lines": 39, "complexity": 1, "imports": [{"statement": "import { defineConfig } from 'vitest/config'", "path": "vitest/config", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import react from '@vitejs/plugin-react'", "path": "@vitejs/plugin-react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { resolve } from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["vitest", "@vitejs/plugin-react", "path"], "lastModified": "2025-05-25T08:08:20.450Z"}, {"path": "server.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/server.js", "size": 1588, "lines": 54, "complexity": 6, "imports": [{"statement": "require('express')", "path": "express", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["express", "path", "fs"], "lastModified": "2025-05-25T08:08:20.439Z"}, {"path": "playwright.config.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/playwright.config.ts", "size": 1984, "lines": 73, "complexity": 3, "imports": [{"statement": "import { defineConfig, devices } from '@playwright/test'", "path": "@playwright/test", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@playwright/test"], "lastModified": "2025-05-25T08:08:20.437Z"}, {"path": "cli.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/cli.js", "size": 1246, "lines": 38, "complexity": 1, "imports": [{"statement": "import { Anthropic } from '@anthropic-ai/sdk'", "path": "@anthropic-ai/sdk", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@anthropic-ai/sdk"], "lastModified": "2025-05-25T08:08:20.430Z"}, {"path": "babel.config.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/babel.config.js", "size": 1004, "lines": 57, "complexity": 1, "imports": [], "exports": [{"statement": "module.exports", "name": "default", "type": "cjs", "module": "cjs"}], "dependencies": [], "lastModified": "2025-05-25T08:08:20.424Z"}, {"path": "code-health/test-improvements.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/code-health/test-improvements.js", "size": 2628, "lines": 81, "complexity": 3, "imports": [{"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { execSync } from 'child_process'", "path": "child_process", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["fs", "path", "child_process"], "lastModified": "2025-05-23T10:43:28.934Z"}, {"path": "code-health/test-health-orchestrator.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/code-health/test-health-orchestrator.js", "size": 5572, "lines": 186, "complexity": 13, "imports": [{"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { fileURLToPath } from 'url'", "path": "url", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import HealthOrchestrator from './orchestrate-health.js'", "path": "./orchestrate-health.js", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["fs", "path", "url"], "lastModified": "2025-05-23T11:10:54.589Z"}, {"path": "code-health/dynamic-scripts-cleanup.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/code-health/dynamic-scripts-cleanup.js", "size": 22355, "lines": 785, "complexity": 88, "imports": [{"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { execSync } from 'child_process'", "path": "child_process", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import ScriptAnalyzer from './enhanced-refactor-analyzer.js'", "path": "./enhanced-refactor-analyzer.js", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { program } from 'commander'", "path": "commander", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export {\n  // Add function exports here\n}", "name": "// Add function exports here\n", "type": "default", "module": "esm"}], "dependencies": ["fs", "path", "child_process", "commander"], "lastModified": "2025-05-23T11:09:23.237Z"}, {"path": "__mocks__/styleMock.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/__mocks__/styleMock.js", "size": 21, "lines": 2, "complexity": 1, "imports": [], "exports": [{"statement": "module.exports", "name": "default", "type": "cjs", "module": "cjs"}], "dependencies": [], "lastModified": "2025-05-25T08:08:20.421Z"}, {"path": "__mocks__/fileMock.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/__mocks__/fileMock.js", "size": 35, "lines": 2, "complexity": 1, "imports": [], "exports": [{"statement": "module.exports", "name": "default", "type": "cjs", "module": "cjs"}], "dependencies": [], "lastModified": "2025-05-25T08:08:20.420Z"}, {"path": "packages/shared/webpack.config.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/webpack.config.js", "size": 1746, "lines": 77, "complexity": 3, "imports": [{"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('terser-webpack-plugin')", "path": "terser-webpack-plugin", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [{"statement": "module.exports", "name": "default", "type": "cjs", "module": "cjs"}], "dependencies": ["path", "terser-webpack-plugin"], "lastModified": "2025-05-21T14:32:59.799Z"}, {"path": "packages/shared/vite.config.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/vite.config.ts", "size": 1191, "lines": 51, "complexity": 1, "imports": [{"statement": "import { defineConfig } from 'vite'", "path": "vite", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { resolve } from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import react from '@vitejs/plugin-react'", "path": "@vitejs/plugin-react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["vite", "path", "@vitejs/plugin-react"], "lastModified": "2025-05-22T10:42:57.212Z"}, {"path": "scripts/scripts.config.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/scripts.config.js", "size": 612, "lines": 40, "complexity": 1, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-23T21:47:27.848Z"}, {"path": "scripts/cli.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/cli.js", "size": 1625, "lines": 57, "complexity": 1, "imports": [{"statement": "import { program } from 'commander'", "path": "commander", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["commander", "path"], "lastModified": "2025-05-23T11:33:52.641Z"}, {"path": "packages/dashboard/src/simple-index.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/simple-index.tsx", "size": 719, "lines": 28, "complexity": 2, "imports": [{"statement": "import './devtools-config'", "path": "./devtools-config", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import ReactDOM from 'react-dom/client'", "path": "react-dom/client", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import SimpleApp from './SimpleApp'", "path": "./SimpleApp", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-dom"], "lastModified": "2025-05-25T09:22:17.197Z"}, {"path": "packages/dashboard/src/reportWebVitals.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/reportWebVitals.ts", "size": 491, "lines": 22, "complexity": 4, "imports": [{"statement": "import { ReportHandler } from \"web-vitals\"", "path": "web-vitals", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["web-vitals"], "lastModified": "2025-05-21T00:52:11.622Z"}, {"path": "packages/dashboard/src/index.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/index.tsx", "size": 1280, "lines": 51, "complexity": 3, "imports": [{"statement": "import './devtools-config'", "path": "./devtools-config", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import ReactDOM from 'react-dom/client'", "path": "react-dom/client", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import App from './App'", "path": "./App", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import reportWebVitals from './reportWebVitals'", "path": "./reportWebVitals", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import './styles/variables.css'", "path": "./styles/variables.css", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import './styles/global.css'", "path": "./styles/global.css", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import './styles/f1-theme.css'", "path": "./styles/f1-theme.css", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "react-dom"], "lastModified": "2025-05-25T09:22:17.193Z"}, {"path": "packages/dashboard/src/devtools-config.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/devtools-config.js", "size": 985, "lines": 31, "complexity": 3, "imports": [], "exports": [{"statement": "export function initDevTools", "name": "initDevTools", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.794Z"}, {"path": "packages/dashboard/dev-dist/sw.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/dev-dist/sw.js", "size": 2878, "lines": 94, "complexity": 10, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-25T09:22:17.176Z"}, {"path": "packages/dashboard/dev-dist/registerSW.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/dev-dist/registerSW.js", "size": 119, "lines": 1, "complexity": 3, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-24T21:39:05.110Z"}, {"path": "packages/dashboard/scripts/manage-assets.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/scripts/manage-assets.js", "size": 6665, "lines": 189, "complexity": 21, "imports": [{"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('./generate-logo-files')", "path": "./generate-logo-files", "type": "cjs", "isRelative": true, "isExternal": false}, {"statement": "require('../public/assets/generate-placeholder-assets')", "path": "../public/assets/generate-placeholder-assets", "type": "cjs", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["fs", "path"], "lastModified": "2025-05-23T21:47:27.791Z"}, {"path": "packages/dashboard/scripts/generate-logo-files.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/scripts/generate-logo-files.js", "size": 4048, "lines": 135, "complexity": 8, "imports": [{"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('canvas')", "path": "canvas", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["fs", "path", "canvas"], "lastModified": "2025-05-23T21:47:27.790Z"}, {"path": "packages/dashboard/scripts/ensure-assets.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/scripts/ensure-assets.js", "size": 1740, "lines": 57, "complexity": 9, "imports": [{"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["fs", "path"], "lastModified": "2025-05-23T21:47:27.788Z"}, {"path": "scripts/utils/test-script.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/utils/test-script.js", "size": 0, "lines": 1, "complexity": 1, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-23T11:23:43.451Z"}, {"path": "scripts/utils/scripts.config.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/utils/scripts.config.js", "size": 612, "lines": 40, "complexity": 1, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-23T11:33:07.995Z"}, {"path": "scripts/utils/cli.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/utils/cli.js", "size": 1625, "lines": 57, "complexity": 1, "imports": [{"statement": "import { program } from 'commander'", "path": "commander", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["commander", "path"], "lastModified": "2025-05-23T11:33:07.994Z"}, {"path": "packages/shared/src/theme/lightTheme.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/lightTheme.ts", "size": 2266, "lines": 97, "complexity": 1, "imports": [{"statement": "import { Theme } from './types'", "path": "./types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  baseColors,\n  lightModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens'", "path": "./tokens", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const lightTheme", "name": "lightTheme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T22:22:41.873Z"}, {"path": "packages/shared/src/theme/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/index.ts", "size": 413, "lines": 20, "complexity": 1, "imports": [], "exports": [{"statement": "export { f1Theme }", "name": "f1Theme ", "type": "default", "module": "esm"}, {"statement": "export { lightTheme }", "name": "lightTheme ", "type": "default", "module": "esm"}, {"statement": "export { darkTheme }", "name": "darkTheme ", "type": "default", "module": "esm"}, {"statement": "export * from './types'", "name": "./types", "type": "default", "module": "esm"}, {"statement": "export * from './tokens'", "name": "./tokens", "type": "default", "module": "esm"}, {"statement": "export * from './ThemeProvider'", "name": "./ThemeProvider", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.841Z"}, {"path": "packages/shared/src/theme/f1Theme.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/f1Theme.ts", "size": 2279, "lines": 97, "complexity": 1, "imports": [{"statement": "import { Theme } from './types'", "path": "./types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens'", "path": "./tokens", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const f1Theme", "name": "f1Theme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T22:22:28.907Z"}, {"path": "packages/shared/src/theme/darkTheme.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/darkTheme.ts", "size": 2392, "lines": 97, "complexity": 1, "imports": [{"statement": "import { Theme } from './types'", "path": "./types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens'", "path": "./tokens", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const darkTheme", "name": "darkTheme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T22:33:38.473Z"}, {"path": "packages/shared/src/theme/GlobalStyles.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/GlobalStyles.tsx", "size": 2961, "lines": 126, "complexity": 1, "imports": [{"statement": "import { createGlobalStyle } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Theme } from './types'", "path": "./types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const GlobalStyles", "name": "GlobalStyles", "type": "named", "module": "esm"}], "dependencies": ["styled-components"], "lastModified": "2025-05-23T21:47:27.837Z"}, {"path": "packages/shared/src/state/createSelector.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/state/createSelector.ts", "size": 2872, "lines": 100, "complexity": 4, "imports": [], "exports": [{"statement": "export type Selector", "name": "Selector", "type": "named", "module": "esm"}, {"statement": "export type InputSelector", "name": "InputSelector", "type": "named", "module": "esm"}, {"statement": "export type ResultFunc", "name": "ResultFunc", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}, {"statement": "export function createSelector", "name": "createSelector", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T18:18:44.675Z"}, {"path": "packages/shared/src/utils/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/utils/index.ts", "size": 3007, "lines": 126, "complexity": 9, "imports": [], "exports": [{"statement": "export function formatCurrency", "name": "formatCurrency", "type": "named", "module": "esm"}, {"statement": "export function formatPercentage", "name": "formatPercentage", "type": "named", "module": "esm"}, {"statement": "export function formatDate", "name": "formatDate", "type": "named", "module": "esm"}, {"statement": "export function truncateText", "name": "truncateText", "type": "named", "module": "esm"}, {"statement": "export function generateId", "name": "generateId", "type": "named", "module": "esm"}, {"statement": "export function debounce", "name": "debounce", "type": "named", "module": "esm"}, {"statement": "export function throttle", "name": "throttle", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.846Z"}, {"path": "packages/shared/src/hooks/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/index.ts", "size": 333, "lines": 12, "complexity": 1, "imports": [], "exports": [{"statement": "export { useAsyncData }", "name": "useAsyncData ", "type": "default", "module": "esm"}, {"statement": "export { useDebounce }", "name": "useDebounce ", "type": "default", "module": "esm"}, {"statement": "export { useErrorHandler }", "name": "useErrorHandler ", "type": "default", "module": "esm"}, {"statement": "export { useLocalStorage }", "name": "useLocalStorage ", "type": "default", "module": "esm"}, {"statement": "export { usePagination }", "name": "usePagination ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-22T14:53:53.197Z"}, {"path": "packages/shared/src/components/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/index.ts", "size": 340, "lines": 15, "complexity": 1, "imports": [], "exports": [{"statement": "export * from \"./atoms\"", "name": "./atoms", "type": "default", "module": "esm"}, {"statement": "export * from \"./molecules\"", "name": "./molecules", "type": "default", "module": "esm"}, {"statement": "export * from \"./organisms\"", "name": "./organisms", "type": "default", "module": "esm"}, {"statement": "export * from \"./templates\"", "name": "./templates", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T10:33:31.863Z"}, {"path": "packages/dashboard/src/routes/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/index.ts", "size": 79, "lines": 6, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as AppRoutes }", "name": "default as AppRoutes ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.801Z"}, {"path": "packages/dashboard/src/layouts/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/layouts/index.ts", "size": 227, "lines": 10, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as MainLayout }", "name": "default as MainLayout ", "type": "default", "module": "esm"}, {"statement": "export { default as Header }", "name": "default as Header ", "type": "default", "module": "esm"}, {"statement": "export { default as Sidebar }", "name": "default as Sidebar ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T06:54:05.412Z"}, {"path": "packages/shared/src/theme/variants/lightTheme.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/variants/lightTheme.ts", "size": 2280, "lines": 97, "complexity": 1, "imports": [{"statement": "import { Theme } from \"../types\"", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { \n  baseColors, \n  lightModeColors, \n  spacing, \n  fontSizes, \n  fontWeights, \n  lineHeights, \n  fontFamilies,\n  breakpoints, \n  borderRadius, \n  shadows, \n  transitions, \n  zIndex \n} from \"../tokens\"", "path": "../tokens", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const lightTheme", "name": "lightTheme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:04:25.248Z"}, {"path": "packages/shared/src/theme/variants/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/variants/index.ts", "size": 134, "lines": 9, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './f1Theme'", "name": "./f1Theme", "type": "default", "module": "esm"}, {"statement": "export * from './lightTheme'", "name": "./lightTheme", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.844Z"}, {"path": "packages/shared/src/theme/variants/f1Theme.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/variants/f1Theme.ts", "size": 2281, "lines": 97, "complexity": 1, "imports": [{"statement": "import { Theme } from \"../types\"", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from \"../tokens\"", "path": "../tokens", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const f1Theme", "name": "f1Theme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T10:55:52.667Z"}, {"path": "packages/shared/src/theme/tokens/typography.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/tokens/typography.ts", "size": 1306, "lines": 60, "complexity": 1, "imports": [], "exports": [{"statement": "export const fontSizes", "name": "fontSizes", "type": "named", "module": "esm"}, {"statement": "export const fontWeights", "name": "fontWeights", "type": "named", "module": "esm"}, {"statement": "export const lineHeights", "name": "lineHeights", "type": "named", "module": "esm"}, {"statement": "export const fontFamilies", "name": "fontFamilies", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:03:49.047Z"}, {"path": "packages/shared/src/theme/tokens/spacing.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/tokens/spacing.ts", "size": 328, "lines": 21, "complexity": 1, "imports": [], "exports": [{"statement": "export const spacing", "name": "spacing", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:03:37.827Z"}, {"path": "packages/shared/src/theme/tokens/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/tokens/index.ts", "size": 1219, "lines": 74, "complexity": 1, "imports": [], "exports": [{"statement": "export const breakpoints", "name": "breakpoints", "type": "named", "module": "esm"}, {"statement": "export const borderRadius", "name": "borderRadius", "type": "named", "module": "esm"}, {"statement": "export const shadows", "name": "shadows", "type": "named", "module": "esm"}, {"statement": "export const transitions", "name": "transitions", "type": "named", "module": "esm"}, {"statement": "export const zIndex", "name": "zIndex", "type": "named", "module": "esm"}, {"statement": "export * from './colors'", "name": "./colors", "type": "default", "module": "esm"}, {"statement": "export * from './spacing'", "name": "./spacing", "type": "default", "module": "esm"}, {"statement": "export * from './typography'", "name": "./typography", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:03:58.815Z"}, {"path": "packages/shared/src/theme/tokens/colors.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/tokens/colors.ts", "size": 3439, "lines": 142, "complexity": 1, "imports": [], "exports": [{"statement": "export const baseColors", "name": "baseColors", "type": "named", "module": "esm"}, {"statement": "export const darkModeColors", "name": "darkModeColors", "type": "named", "module": "esm"}, {"statement": "export const lightModeColors", "name": "lightModeColors", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:03:29.091Z"}, {"path": "packages/dashboard/public/assets/generate-placeholder-assets.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/public/assets/generate-placeholder-assets.js", "size": 2713, "lines": 59, "complexity": 2, "imports": [{"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["fs", "path"], "lastModified": "2025-05-23T21:47:27.788Z"}, {"path": "packages/dashboard/public/assets/create-images.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/public/assets/create-images.js", "size": 2066, "lines": 74, "complexity": 2, "imports": [{"statement": "require('fs')", "path": "fs", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('path')", "path": "path", "type": "cjs", "isRelative": false, "isExternal": true}, {"statement": "require('canvas')", "path": "canvas", "type": "cjs", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["fs", "path", "canvas"], "lastModified": "2025-05-23T21:47:27.787Z"}, {"path": "packages/shared/src/components/templates/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/templates/index.ts", "size": 213, "lines": 9, "complexity": 1, "imports": [], "exports": [{"statement": "export { DashboardTemplate }", "name": "DashboardTemplate ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T22:24:32.201Z"}, {"path": "packages/shared/src/components/templates/DashboardTemplate.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/templates/DashboardTemplate.stories.tsx", "size": 2646, "lines": 103, "complexity": 8, "imports": [{"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DashboardTemplate } from './DashboardTemplate'", "path": "./DashboardTemplate", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Card } from '../molecules/Card'", "path": "../molecules/Card", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const CollapsedSidebar", "name": "CollapsedSidebar", "type": "named", "module": "esm"}], "dependencies": ["@storybook/react"], "lastModified": "2025-05-22T14:06:02.925Z"}, {"path": "packages/shared/src/components/organisms/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/organisms/index.ts", "size": 201, "lines": 9, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './DataCard'", "name": "./DataCard", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T16:41:08.170Z"}, {"path": "packages/shared/src/components/organisms/DataCard.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/organisms/DataCard.stories.tsx", "size": 3942, "lines": 166, "complexity": 1, "imports": [{"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DataCard } from './DataCard'", "path": "./DataCard", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const WithActionButton", "name": "WithActionButton", "type": "named", "module": "esm"}, {"statement": "export const Loading", "name": "Loading", "type": "named", "module": "esm"}, {"statement": "export const Error", "name": "Error", "type": "named", "module": "esm"}, {"statement": "export const Empty", "name": "Empty", "type": "named", "module": "esm"}, {"statement": "export const ErrorWithoutRetry", "name": "ErrorWithoutRetry", "type": "named", "module": "esm"}, {"statement": "export const AllStates", "name": "AllStates", "type": "named", "module": "esm"}], "dependencies": ["@storybook/react"], "lastModified": "2025-05-22T14:05:49.190Z"}, {"path": "packages/shared/src/components/molecules/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/index.ts", "size": 541, "lines": 21, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './Card'", "name": "./Card", "type": "default", "module": "esm"}, {"statement": "export * from './EmptyState'", "name": "./EmptyState", "type": "default", "module": "esm"}, {"statement": "export * from './ErrorBoundary'", "name": "./ErrorBoundary", "type": "default", "module": "esm"}, {"statement": "export * from './UnifiedErrorBoundary'", "name": "./UnifiedErrorBoundary", "type": "default", "module": "esm"}, {"statement": "export * from './FormField'", "name": "./FormField", "type": "default", "module": "esm"}, {"statement": "export * from './Modal'", "name": "./Modal", "type": "default", "module": "esm"}, {"statement": "export * from './Table'", "name": "./Table", "type": "default", "module": "esm"}, {"statement": "export * from './TradeTable'", "name": "./TradeTable", "type": "default", "module": "esm"}, {"statement": "export * from './TradeTableRow'", "name": "./TradeTableRow", "type": "default", "module": "esm"}, {"statement": "export * from './TradeTableFilters'", "name": "./TradeTableFilters", "type": "default", "module": "esm"}, {"statement": "export * from './TradeTableColumns'", "name": "./TradeTableColumns", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T08:38:57.865Z"}, {"path": "packages/shared/src/components/molecules/Modal.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Modal.stories.tsx", "size": 6036, "lines": 234, "complexity": 2, "imports": [{"statement": "import { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Modal } from './Modal'", "path": "./Modal", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Input } from '../atoms/Input'", "path": "../atoms/Input", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const WithForm", "name": "WithForm", "type": "named", "module": "esm"}, {"statement": "export const Small", "name": "Small", "type": "named", "module": "esm"}, {"statement": "export const Large", "name": "Large", "type": "named", "module": "esm"}, {"statement": "export const WithoutFooter", "name": "WithoutFooter", "type": "named", "module": "esm"}, {"statement": "export const WithCustomFooter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const LoadingAction", "name": "LoadingAction", "type": "named", "module": "esm"}, {"statement": "export const LongContent", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const Confirmation", "name": "Confirmation", "type": "named", "module": "esm"}, {"statement": "export const WithoutTitle", "name": "WithoutTitle", "type": "named", "module": "esm"}], "dependencies": ["react", "@storybook/react"], "lastModified": "2025-05-22T14:02:31.972Z"}, {"path": "packages/shared/src/components/molecules/Card.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Card.stories.tsx", "size": 3827, "lines": 162, "complexity": 1, "imports": [{"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card } from './Card'", "path": "./Card", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const WithSubtitle", "name": "WithSubtitle", "type": "named", "module": "esm"}, {"statement": "export const WithActions", "name": "WithActions", "type": "named", "module": "esm"}, {"statement": "export const WithFooter", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const Loading", "name": "Loading", "type": "named", "module": "esm"}, {"statement": "export const Error", "name": "Error", "type": "named", "module": "esm"}, {"statement": "export const Clickable", "name": "Clickable", "type": "named", "module": "esm"}, {"statement": "export const Variants", "name": "Variants", "type": "named", "module": "esm"}], "dependencies": ["@storybook/react"], "lastModified": "2025-05-22T14:02:19.829Z"}, {"path": "packages/shared/src/components/atoms/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/index.ts", "size": 378, "lines": 15, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './Badge'", "name": "./Badge", "type": "default", "module": "esm"}, {"statement": "export * from './Button'", "name": "./Button", "type": "default", "module": "esm"}, {"statement": "export * from './Input'", "name": "./Input", "type": "default", "module": "esm"}, {"statement": "export * from './LoadingPlaceholder'", "name": "./LoadingPlaceholder", "type": "default", "module": "esm"}, {"statement": "export * from './Select'", "name": "./Select", "type": "default", "module": "esm"}, {"statement": "export * from './StatusIndicator'", "name": "./StatusIndicator", "type": "default", "module": "esm"}, {"statement": "export * from './Tag'", "name": "./Tag", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T16:40:43.440Z"}, {"path": "packages/shared/src/components/atoms/Tag.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Tag.stories.tsx", "size": 3646, "lines": 164, "complexity": 1, "imports": [{"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Tag } from './Tag'", "path": "./Tag", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const Primary", "name": "Primary", "type": "named", "module": "esm"}, {"statement": "export const Success", "name": "Success", "type": "named", "module": "esm"}, {"statement": "export const Warning", "name": "Warning", "type": "named", "module": "esm"}, {"statement": "export const Error", "name": "Error", "type": "named", "module": "esm"}, {"statement": "export const Removable", "name": "Removable", "type": "named", "module": "esm"}, {"statement": "export const Small", "name": "Small", "type": "named", "module": "esm"}, {"statement": "export const Large", "name": "Large", "type": "named", "module": "esm"}, {"statement": "export const Clickable", "name": "Clickable", "type": "named", "module": "esm"}, {"statement": "export const AllVariants", "name": "AllVariants", "type": "named", "module": "esm"}, {"statement": "export const RemovableTags", "name": "RemovableTags", "type": "named", "module": "esm"}], "dependencies": ["@storybook/react"], "lastModified": "2025-05-22T14:02:07.506Z"}, {"path": "packages/shared/src/components/atoms/Badge.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Badge.stories.tsx", "size": 5310, "lines": 263, "complexity": 1, "imports": [{"statement": "import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'", "path": "@storybook/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Badge } from './Badge'", "path": "./Badge", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from '../../theme/ThemeProvider'", "path": "../../theme/ThemeProvider", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const Primary", "name": "Primary", "type": "named", "module": "esm"}, {"statement": "export const Success", "name": "Success", "type": "named", "module": "esm"}, {"statement": "export const Warning", "name": "Warning", "type": "named", "module": "esm"}, {"statement": "export const Error", "name": "Error", "type": "named", "module": "esm"}, {"statement": "export const Solid", "name": "Solid", "type": "named", "module": "esm"}, {"statement": "export const Outlined", "name": "Outlined", "type": "named", "module": "esm"}, {"statement": "export const Rounded", "name": "Rounded", "type": "named", "module": "esm"}, {"statement": "export const Dot", "name": "Dot", "type": "named", "module": "esm"}, {"statement": "export const Counter", "name": "Counter", "type": "named", "module": "esm"}, {"statement": "export const CounterWithMax", "name": "CounterWithMax", "type": "named", "module": "esm"}, {"statement": "export const WithIcons", "name": "WithIcons", "type": "named", "module": "esm"}, {"statement": "export const Small", "name": "Small", "type": "named", "module": "esm"}, {"statement": "export const Large", "name": "Large", "type": "named", "module": "esm"}, {"statement": "export const Clickable", "name": "Clickable", "type": "named", "module": "esm"}, {"statement": "export const AllVariants", "name": "AllVariants", "type": "named", "module": "esm"}, {"statement": "export const AllStyles", "name": "AllStyles", "type": "named", "module": "esm"}], "dependencies": ["@storybook/react"], "lastModified": "2025-05-21T22:35:31.464Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/index.ts", "size": 712, "lines": 23, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { default as TradingDashboard }", "name": "default as TradingDashboard ", "type": "default", "module": "esm"}, {"statement": "export { default as useTradingDashboard }", "name": "default as useTradingDashboard ", "type": "default", "module": "esm"}, {"statement": "export { default as MetricsPanel }", "name": "default as MetricsPanel ", "type": "default", "module": "esm"}, {"statement": "export { default as PerformanceChart }", "name": "default as PerformanceChart ", "type": "default", "module": "esm"}, {"statement": "export { default as RecentTradesTable }", "name": "default as RecentTradesTable ", "type": "default", "module": "esm"}, {"statement": "export { default as SetupAnalysis }", "name": "default as SetupAnalysis ", "type": "default", "module": "esm"}, {"statement": "export * from './types'", "name": "./types", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:44:47.647Z"}, {"path": "packages/dashboard/src/features/trade-journal/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/index.ts", "size": 284, "lines": 11, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { default as TradeJournal }", "name": "default as TradeJournal ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeForm }", "name": "default as TradeForm ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:42:42.655Z"}, {"path": "packages/dashboard/src/features/trade-analysis/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/index.ts", "size": 1257, "lines": 39, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { TradeAnalysis }", "name": "TradeAnalysis ", "type": "default", "module": "esm"}, {"statement": "export { default as LegacyTradeAnalysis }", "name": "default as LegacyTradeAnalysis ", "type": "default", "module": "esm"}, {"statement": "export {\n  TradeAnalysisProvider as LegacyTradeAnalysisProvider,\n  useTradeAnalysis as useLegacyTradeAnalysis,\n}", "name": "TradeAnalysisProvider as LegacyTradeAnalysisProvider,\n  useTradeAnalysis as useLegacyTradeAnalysis,\n", "type": "default", "module": "esm"}, {"statement": "export { PerformanceSummary }", "name": "PerformanceSummary ", "type": "default", "module": "esm"}, {"statement": "export { TradesTable }", "name": "TradesTable ", "type": "default", "module": "esm"}, {"statement": "export { CategoryPerformanceChart }", "name": "CategoryPerformanceChart ", "type": "default", "module": "esm"}, {"statement": "export { TimePerformanceChart }", "name": "TimePerformanceChart ", "type": "default", "module": "esm"}, {"statement": "export { FilterPanel }", "name": "FilterPanel ", "type": "default", "module": "esm"}, {"statement": "export { TradeDetail }", "name": "TradeDetail ", "type": "default", "module": "esm"}, {"statement": "export * from './components/TradeAnalysisFilter'", "name": "./components/TradeAnalysisFilter", "type": "default", "module": "esm"}, {"statement": "export * from './components/TradeAnalysisTable'", "name": "./components/TradeAnalysisTable", "type": "default", "module": "esm"}, {"statement": "export * from './components/TradeAnalysisSummary'", "name": "./components/TradeAnalysisSummary", "type": "default", "module": "esm"}, {"statement": "export * from './components/TradeAnalysisCharts'", "name": "./components/TradeAnalysisCharts", "type": "default", "module": "esm"}, {"statement": "export * from './hooks/useTradeAnalysis'", "name": "./hooks/useTradeAnalysis", "type": "default", "module": "esm"}, {"statement": "export * from './state/tradeAnalysisState'", "name": "./state/tradeAnalysisState", "type": "default", "module": "esm"}, {"statement": "export * from './types'", "name": "./types", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T19:38:08.145Z"}, {"path": "packages/dashboard/src/features/settings/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/settings/index.ts", "size": 404, "lines": 12, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as Settings }", "name": "default as Settings ", "type": "default", "module": "esm"}, {"statement": "export { default as SettingsSection }", "name": "default as SettingsSection ", "type": "default", "module": "esm"}, {"statement": "export { default as SettingItem }", "name": "default as SettingItem ", "type": "default", "module": "esm"}, {"statement": "export { default as ToggleSwitch }", "name": "default as ToggleSwitch ", "type": "default", "module": "esm"}, {"statement": "export { useSettings }", "name": "useSettings ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T07:07:48.309Z"}, {"path": "packages/dashboard/src/features/performance-dashboard/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/performance-dashboard/index.ts", "size": 170, "lines": 8, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as Dashboard }", "name": "default as Dashboard ", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T06:54:05.641Z"}, {"path": "packages/dashboard/src/features/daily-guide/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/index.ts", "size": 563, "lines": 27, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as LegacyDailyGuide }", "name": "default as LegacyDailyGuide ", "type": "default", "module": "esm"}, {"statement": "export {\n  DailyGuideProvider as LegacyDailyGuideProvider,\n  useDailyGuide as useLegacyDailyGuide,\n}", "name": "DailyGuideProvider as LegacyDailyGuideProvider,\n  useDailyGuide as useLegacyDailyGuide,\n", "type": "default", "module": "esm"}, {"statement": "export { MarketNews }", "name": "MarketNews ", "type": "default", "module": "esm"}, {"statement": "export * from './components'", "name": "./components", "type": "default", "module": "esm"}, {"statement": "export * from './hooks'", "name": "./hooks", "type": "default", "module": "esm"}, {"statement": "export * from './state'", "name": "./state", "type": "default", "module": "esm"}, {"statement": "export * from './types'", "name": "./types", "type": "default", "module": "esm"}, {"statement": "export * from './components/ui'", "name": "./components/ui", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T19:16:49.564Z"}, {"path": "packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx", "size": 11355, "lines": 432, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ProfitLossCell } from './ProfitLossCell'", "path": "./ProfitLossCell", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const BigWin", "name": "BigWin", "type": "named", "module": "esm"}, {"statement": "export const SmallWin", "name": "SmallWin", "type": "named", "module": "esm"}, {"statement": "export const Breakeven", "name": "Breakeven", "type": "named", "module": "esm"}, {"statement": "export const SmallLoss", "name": "SmallLoss", "type": "named", "module": "esm"}, {"statement": "export const BigLoss", "name": "BigLoss", "type": "named", "module": "esm"}, {"statement": "export const Loading", "name": "Loading", "type": "named", "module": "esm"}, {"statement": "export const Interactive", "name": "Interactive", "type": "named", "module": "esm"}, {"statement": "export const SizeVariants", "name": "SizeVariants", "type": "named", "module": "esm"}, {"statement": "export const TradingScenarios", "name": "TradingScenarios", "type": "named", "module": "esm"}, {"statement": "export const TableContext", "name": "TableContext", "type": "named", "module": "esm"}, {"statement": "export const EdgeCases", "name": "EdgeCases", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T20:15:11.152Z"}, {"path": "packages/dashboard/src/features/trade-journal/constants/setupClassification.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/constants/setupClassification.ts", "size": 6375, "lines": 201, "complexity": 8, "imports": [{"statement": "import { \n  StructureSetupType, \n  SessionSetupType, \n  ModelSetupType, \n  LiquidityType, \n  FVGType, \n  DOLTargetType, \n  ParentPDArrayType \n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const SETUP_CATEGORY_OPTIONS", "name": "SETUP_CATEGORY_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const STRUCTURE_SETUP_OPTIONS", "name": "STRUCTURE_SETUP_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const SESSION_SETUP_OPTIONS", "name": "SESSION_SETUP_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const MODEL_SETUP_OPTIONS", "name": "MODEL_SETUP_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const LIQUIDITY_OPTIONS", "name": "LIQUIDITY_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const TIME_BASED_FVG_OPTIONS", "name": "TIME_BASED_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const CURRENT_SESSION_FVG_OPTIONS", "name": "CURRENT_SESSION_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const PREV_DAY_FVG_OPTIONS", "name": "PREV_DAY_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const THREE_DAY_FVG_OPTIONS", "name": "THREE_DAY_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const SPECIAL_FVG_OPTIONS", "name": "SPECIAL_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const ALL_FVG_OPTIONS", "name": "ALL_FVG_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const DOL_TARGET_OPTIONS", "name": "DOL_TARGET_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const PARENT_PD_ARRAY_OPTIONS", "name": "PARENT_PD_ARRAY_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const getSetupOptionsByCategory", "name": "getSetupOptionsByCategory", "type": "named", "module": "esm"}, {"statement": "export const getDOLTargetOptions", "name": "getDOLTargetOptions", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-22T22:28:14.075Z"}, {"path": "packages/dashboard/src/features/trade-journal/constants/patternQuality.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/constants/patternQuality.ts", "size": 5121, "lines": 147, "complexity": 14, "imports": [{"statement": "import { ScoreRange } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const SCORE_VALUES", "name": "SCORE_VALUES", "type": "named", "module": "esm"}, {"statement": "export const SCORE_RANGE_OPTIONS", "name": "SCORE_RANGE_OPTIONS", "type": "named", "module": "esm"}, {"statement": "export const PATTERN_QUALITY_CRITERIA", "name": "PATTERN_QUALITY_CRITERIA", "type": "named", "module": "esm"}, {"statement": "export const calculateTotalScore", "name": "calculateTotalScore", "type": "named", "module": "esm"}, {"statement": "export const convertScoreToRating", "name": "convertScoreToRating", "type": "named", "module": "esm"}, {"statement": "export const getRatingDescription", "name": "getRatingDescription", "type": "named", "module": "esm"}, {"statement": "export const getRatingColor", "name": "getRatingColor", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-22T23:02:43.836Z"}, {"path": "packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "size": 11446, "lines": 435, "complexity": 71, "imports": [{"statement": "import {\n  createStoreContext,\n  createSelector,\n  persistState,\n  Trade,\n  TradeFormData,\n  CompleteTradeData,\n} from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export enum TradeAnalysisActionTypes", "name": "TradeAnalysisActionTypes", "type": "named", "module": "esm"}, {"statement": "export interface SetFilterAction", "name": "SetFilterAction", "type": "named", "module": "esm"}, {"statement": "export interface ClearFiltersAction", "name": "ClearFiltersAction", "type": "named", "module": "esm"}, {"statement": "export interface SetSortAction", "name": "SetSortAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageAction", "name": "SetPageAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageSizeAction", "name": "SetPageSizeAction", "type": "named", "module": "esm"}, {"statement": "export interface SetTradesAction", "name": "SetTradesAction", "type": "named", "module": "esm"}, {"statement": "export interface SetLoadingAction", "name": "SetLoadingAction", "type": "named", "module": "esm"}, {"statement": "export interface SetErrorAction", "name": "SetErrorAction", "type": "named", "module": "esm"}, {"statement": "export type TradeAnalysisAction", "name": "TradeAnalysisAction", "type": "named", "module": "esm"}, {"statement": "export interface TradeFilter", "name": "TradeFilter", "type": "named", "module": "esm"}, {"statement": "export interface TradeSort", "name": "TradeSort", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysisState", "name": "TradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const initialTradeAnalysisState", "name": "initialTradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisReducer", "name": "tradeAnalysisReducer", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisActions", "name": "tradeAnalysisActions", "type": "named", "module": "esm"}, {"statement": "export const selectTrades", "name": "selectTrades", "type": "named", "module": "esm"}, {"statement": "export const selectFilter", "name": "selectFilter", "type": "named", "module": "esm"}, {"statement": "export const selectSort", "name": "selectSort", "type": "named", "module": "esm"}, {"statement": "export const selectPage", "name": "selectPage", "type": "named", "module": "esm"}, {"statement": "export const selectPageSize", "name": "selectPageSize", "type": "named", "module": "esm"}, {"statement": "export const selectIsLoading", "name": "selectIsLoading", "type": "named", "module": "esm"}, {"statement": "export const selectError", "name": "selectError", "type": "named", "module": "esm"}, {"statement": "export const selectFilteredTrades", "name": "selectFilteredTrades", "type": "named", "module": "esm"}, {"statement": "export const selectSortedTrades", "name": "selectSortedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectPaginatedTrades", "name": "selectPaginatedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectTotalPages", "name": "selectTotalPages", "type": "named", "module": "esm"}, {"statement": "export const selectTradeSummary", "name": "selectTradeSummary", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:46:44.711Z"}, {"path": "packages/dashboard/src/features/daily-guide/state/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/state/index.ts", "size": 180, "lines": 9, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './dailyGuideState'", "name": "./dailyGuideState", "type": "default", "module": "esm"}, {"statement": "export * from './dailyGuideSelectors'", "name": "./dailyGuideSelectors", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T19:09:21.673Z"}, {"path": "packages/dashboard/src/features/daily-guide/hooks/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/hooks/index.ts", "size": 124, "lines": 8, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './useDailyGuide'", "name": "./useDailyGuide", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T19:11:04.197Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/index.ts", "size": 225, "lines": 11, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './DailyGuide'", "name": "./DailyGuide", "type": "default", "module": "esm"}, {"statement": "export * from './MarketOverview'", "name": "./MarketOverview", "type": "default", "module": "esm"}, {"statement": "export * from './TradingPlan'", "name": "./TradingPlan", "type": "default", "module": "esm"}, {"statement": "export * from './KeyLevels'", "name": "./KeyLevels", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T19:16:23.991Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-list/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-list/index.ts", "size": 492, "lines": 14, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { default as TradeListHeader }", "name": "default as TradeListHeader ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeListRow }", "name": "default as TradeListRow ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeListExpandedRow }", "name": "default as TradeListExpandedRow ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeListEmpty }", "name": "default as TradeListEmpty ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeListLoading }", "name": "default as TradeListLoading ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:40:37.512Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-journal/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-journal/index.ts", "size": 388, "lines": 12, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { default as TradeJournalHeader }", "name": "default as TradeJournalHeader ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeJournalFilters }", "name": "default as TradeJournalFilters ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeJournalContent }", "name": "default as TradeJournalContent ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:38:17.086Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalContent.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalContent.tsx", "size": 2760, "lines": 97, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import TradeList from '../TradeList'", "path": "../TradeList", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import TradeJournalFilters from './TradeJournalFilters'", "path": "./TradeJournalFilters", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:37:32.332Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/index.ts", "size": 734, "lines": 17, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { default as TradeFormHeader }", "name": "default as TradeFormHeader ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormBasicFields }", "name": "default as TradeFormBasicFields ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormTimingFields }", "name": "default as TradeFormTimingFields ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormRiskFields }", "name": "default as TradeFormRiskFields ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormStrategyFields }", "name": "default as TradeFormStrategyFields ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormActions }", "name": "default as TradeFormActions ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormMessages }", "name": "default as TradeFormMessages ", "type": "default", "module": "esm"}, {"statement": "export { default as TradeFormLoading }", "name": "default as TradeFormLoading ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:37:13.583Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx", "size": 3189, "lines": 120, "complexity": 9, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ValidationErrors } from '../../hooks/useTradeValidation'", "path": "../../hooks/useTradeValidation", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks'", "path": "../../hooks", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import TimePicker from '../TimePicker'", "path": "../TimePicker", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import SelectDropdown from '../SelectDropdown'", "path": "../SelectDropdown", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:37:01.585Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx", "size": 4581, "lines": 166, "complexity": 5, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ValidationErrors } from '../../hooks/useTradeValidation'", "path": "../../hooks/useTradeValidation", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  MODEL_TYPE_OPTIONS,\n  SETUP_OPTIONS,\n  ENTRY_VERSION_OPTIONS,\n  PATTERN_QUALITY_OPTIONS,\n} from '../../hooks'", "path": "../../hooks", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import SelectDropdown from '../SelectDropdown'", "path": "../SelectDropdown", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import SetupClassificationSection from '../trade-setup-classification/SetupClassificationSection'", "path": "../trade-setup-classification/SetupClassificationSection", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:26:54.146Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx", "size": 3206, "lines": 125, "complexity": 5, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ValidationErrors } from '../../hooks/useTradeValidation'", "path": "../../hooks/useTradeValidation", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:36:20.732Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormMessages.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormMessages.tsx", "size": 1799, "lines": 61, "complexity": 7, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:36:07.437Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx", "size": 6052, "lines": 219, "complexity": 8, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeFormValues } from '../../types'", "path": "../../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ValidationErrors } from '../../hooks/useTradeValidation'", "path": "../../hooks/useTradeValidation", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:35:19.472Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/ui/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/ui/index.ts", "size": 182, "lines": 10, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './SectionCard'", "name": "./SectionCard", "type": "default", "module": "esm"}, {"statement": "export * from './SentimentBadge'", "name": "./SentimentBadge", "type": "default", "module": "esm"}, {"statement": "export * from './PriorityTag'", "name": "./PriorityTag", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T14:59:41.993Z"}], "services": [{"path": "packages/shared/src/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/index.ts", "size": 520, "lines": 34, "complexity": 1, "imports": [], "exports": [{"statement": "export * from './types'", "name": "./types", "type": "default", "module": "esm"}, {"statement": "export * from './api'", "name": "./api", "type": "default", "module": "esm"}, {"statement": "export * from './components'", "name": "./components", "type": "default", "module": "esm"}, {"statement": "export * from './hooks'", "name": "./hooks", "type": "default", "module": "esm"}, {"statement": "export * from './theme'", "name": "./theme", "type": "default", "module": "esm"}, {"statement": "export * from './state'", "name": "./state", "type": "default", "module": "esm"}, {"statement": "export * from './utils'", "name": "./utils", "type": "default", "module": "esm"}, {"statement": "export * from './monitoring'", "name": "./monitoring", "type": "default", "module": "esm"}, {"statement": "export * from './services'", "name": "./services", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T08:28:54.952Z"}, {"path": "packages/shared/src/types/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/types/index.ts", "size": 2500, "lines": 148, "complexity": 25, "imports": [], "exports": [{"statement": "export type ID", "name": "ID", "type": "named", "module": "esm"}, {"statement": "export interface ApiResponse", "name": "ApiResponse", "type": "named", "module": "esm"}, {"statement": "export interface ConfigurationResponse", "name": "ConfigurationResponse", "type": "named", "module": "esm"}, {"statement": "export interface PerformanceDataPoint", "name": "PerformanceDataPoint", "type": "named", "module": "esm"}, {"statement": "export interface NewsItem", "name": "NewsItem", "type": "named", "module": "esm"}, {"statement": "export interface DashboardDataResponse", "name": "DashboardDataResponse", "type": "named", "module": "esm"}, {"statement": "export interface TradingDataItem", "name": "TradingDataItem", "type": "named", "module": "esm"}, {"statement": "export interface TradingDataOptions", "name": "TradingDataOptions", "type": "named", "module": "esm"}, {"statement": "export interface TradingDataResponse", "name": "TradingDataResponse", "type": "named", "module": "esm"}, {"statement": "export interface PerformanceMetricsOptions", "name": "PerformanceMetricsOptions", "type": "named", "module": "esm"}, {"statement": "export interface PerformanceMetricsResponse", "name": "PerformanceMetricsResponse", "type": "named", "module": "esm"}, {"statement": "export interface MarketNewsOptions", "name": "MarketNewsOptions", "type": "named", "module": "esm"}, {"statement": "export interface MarketNewsResponse", "name": "MarketNewsResponse", "type": "named", "module": "esm"}, {"statement": "export interface UserPreferences", "name": "UserPreferences", "type": "named", "module": "esm"}, {"statement": "export interface UserPreferencesResponse", "name": "UserPreferencesResponse", "type": "named", "module": "esm"}, {"statement": "export * from './trading'", "name": "./trading", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T08:27:33.290Z"}, {"path": "packages/shared/src/state/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/state/index.ts", "size": 285, "lines": 11, "complexity": 1, "imports": [], "exports": [{"statement": "export { createSelector }", "name": "createSelector ", "type": "default", "module": "esm"}, {"statement": "export * from './createStoreContext'", "name": "./createStoreContext", "type": "default", "module": "esm"}, {"statement": "export * from '../services/persistState'", "name": "../services/persistState", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T18:21:33.683Z"}, {"path": "packages/shared/src/services/tradeStorageInterface.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/tradeStorageInterface.ts", "size": 905, "lines": 26, "complexity": 4, "imports": [], "exports": [{"statement": "export interface TradeStorageService", "name": "TradeStorageService", "type": "named", "module": "esm"}, {"statement": "export * from '../types/trading'", "name": "../types/trading", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T18:22:35.795Z"}, {"path": "packages/shared/src/services/tradeStorage.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/tradeStorage.ts", "size": 28474, "lines": 830, "complexity": 61, "imports": [{"statement": "import {\n  PerformanceMetrics,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n  CompleteTradeData,\n  TradeFilters,\n  Trade, // Legacy interface for backward compatibility\n} from '../types/trading'", "path": "../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const tradeStorage", "name": "tradeStorage", "type": "named", "module": "esm"}, {"statement": "export const tradeStorageService", "name": "tradeStorageService", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T16:56:57.395Z"}, {"path": "packages/shared/src/services/persistState.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/persistState.ts", "size": 3866, "lines": 145, "complexity": 21, "imports": [{"statement": "import { Reducer, Action } from './createStoreContext'", "path": "./createStoreContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface PersistStateOptions", "name": "PersistStateOptions", "type": "named", "module": "esm"}, {"statement": "export interface PersistStateResult", "name": "PersistStateResult", "type": "named", "module": "esm"}, {"statement": "export function persistState", "name": "persistState", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T18:19:05.253Z"}, {"path": "packages/shared/src/services/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/index.ts", "size": 236, "lines": 10, "complexity": 1, "imports": [], "exports": [{"statement": "export { default as tradeStorage, tradeStorageService }", "name": "default as tradeStorage, tradeStorageService ", "type": "default", "module": "esm"}, {"statement": "export * from './persistState'", "name": "./persistState", "type": "default", "module": "esm"}, {"statement": "export * from './tradeStorageInterface'", "name": "./tradeStorageInterface", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T18:22:22.590Z"}, {"path": "packages/shared/src/api/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/api/index.ts", "size": 603, "lines": 19, "complexity": 1, "imports": [], "exports": [{"statement": "export { tradeStorageService }", "name": "tradeStorageService ", "type": "default", "module": "esm"}, {"statement": "export * from '../types/trading'", "name": "../types/trading", "type": "default", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T19:20:19.583Z"}, {"path": "packages/shared/src/api/context/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/api/context/index.ts", "size": 188, "lines": 10, "complexity": 1, "imports": [], "exports": [], "dependencies": [], "lastModified": "2025-05-23T22:39:32.534Z"}, {"path": "packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "size": 13946, "lines": 415, "complexity": 57, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  TradeAnalysisData,\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const fetchTradeAnalysisData", "name": "fetchTradeAnalysisData", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:33:21.305Z"}, {"path": "packages/dashboard/src/features/daily-guide/api/dailyGuideApi.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/api/dailyGuideApi.ts", "size": 5595, "lines": 185, "complexity": 10, "imports": [{"statement": "import { DailyGuideData, MarketSentiment, TradingPlanPriority } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const fetchDailyGuideData", "name": "fetchDailyGuideData", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-25T09:22:17.185Z"}], "types": [{"path": "packages/shared/src/react-types.d.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/react-types.d.ts", "size": 1493, "lines": 53, "complexity": 8, "imports": [{"statement": "import \"react\"", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react"], "lastModified": "2025-05-21T14:50:05.529Z"}, {"path": "packages/shared/src/types/trading.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/types/trading.ts", "size": 9876, "lines": 464, "complexity": 117, "imports": [], "exports": [{"statement": "export enum TradeDirection", "name": "TradeDirection", "type": "named", "module": "esm"}, {"statement": "export enum TradeStatus", "name": "TradeStatus", "type": "named", "module": "esm"}, {"statement": "export enum OrderType", "name": "OrderType", "type": "named", "module": "esm"}, {"statement": "export enum OrderSide", "name": "OrderSide", "type": "named", "module": "esm"}, {"statement": "export enum OrderStatus", "name": "OrderStatus", "type": "named", "module": "esm"}, {"statement": "export enum TimeInForce", "name": "TimeInForce", "type": "named", "module": "esm"}, {"statement": "export interface Position", "name": "Position", "type": "named", "module": "esm"}, {"statement": "export interface Order", "name": "Order", "type": "named", "module": "esm"}, {"statement": "export interface MarketData", "name": "MarketData", "type": "named", "module": "esm"}, {"statement": "export interface PerformanceMetrics", "name": "PerformanceMetrics", "type": "named", "module": "esm"}, {"statement": "export interface TradingSession", "name": "TradingSession", "type": "named", "module": "esm"}, {"statement": "export interface TradeRecord", "name": "TradeRecord", "type": "named", "module": "esm"}, {"statement": "export interface TradeFvgDetails", "name": "TradeFvgDetails", "type": "named", "module": "esm"}, {"statement": "export interface TradeSetup", "name": "TradeSetup", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysis", "name": "TradeAnalysis", "type": "named", "module": "esm"}, {"statement": "export interface CompleteTradeData", "name": "CompleteTradeData", "type": "named", "module": "esm"}, {"statement": "export interface TradeFilters", "name": "TradeFilters", "type": "named", "module": "esm"}, {"statement": "export interface Trade", "name": "Trade", "type": "named", "module": "esm"}, {"statement": "export interface TradeFormData", "name": "TradeFormData", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T08:53:51.040Z"}, {"path": "packages/shared/src/theme/types.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/types.ts", "size": 2688, "lines": 162, "complexity": 1, "imports": [], "exports": [{"statement": "export interface ThemeColors", "name": "ThemeColors", "type": "named", "module": "esm"}, {"statement": "export interface ThemeSpacing", "name": "ThemeSpacing", "type": "named", "module": "esm"}, {"statement": "export interface ThemeBreakpoints", "name": "ThemeBreakpoints", "type": "named", "module": "esm"}, {"statement": "export interface ThemeFontSizes", "name": "ThemeFontSizes", "type": "named", "module": "esm"}, {"statement": "export interface ThemeFontWeights", "name": "ThemeFontWeights", "type": "named", "module": "esm"}, {"statement": "export interface ThemeLineHeights", "name": "ThemeLineHeights", "type": "named", "module": "esm"}, {"statement": "export interface ThemeFontFamilies", "name": "ThemeFontFamilies", "type": "named", "module": "esm"}, {"statement": "export interface ThemeBorderRadius", "name": "ThemeBorderRadius", "type": "named", "module": "esm"}, {"statement": "export interface ThemeShadows", "name": "ThemeShadows", "type": "named", "module": "esm"}, {"statement": "export interface ThemeTransitions", "name": "ThemeTransitions", "type": "named", "module": "esm"}, {"statement": "export interface ThemeZIndex", "name": "ThemeZIndex", "type": "named", "module": "esm"}, {"statement": "export interface Theme", "name": "Theme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-23T21:47:27.842Z"}, {"path": "packages/shared/src/theme/tokens.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/tokens.ts", "size": 5625, "lines": 284, "complexity": 1, "imports": [], "exports": [{"statement": "export interface BaseColors", "name": "BaseColors", "type": "named", "module": "esm"}, {"statement": "export interface ColorMode", "name": "ColorMode", "type": "named", "module": "esm"}, {"statement": "export const baseColors", "name": "baseColors", "type": "named", "module": "esm"}, {"statement": "export const darkModeColors", "name": "darkModeColors", "type": "named", "module": "esm"}, {"statement": "export const lightModeColors", "name": "lightModeColors", "type": "named", "module": "esm"}, {"statement": "export const spacing", "name": "spacing", "type": "named", "module": "esm"}, {"statement": "export const fontSizes", "name": "fontSizes", "type": "named", "module": "esm"}, {"statement": "export const fontWeights", "name": "fontWeights", "type": "named", "module": "esm"}, {"statement": "export const lineHeights", "name": "lineHeights", "type": "named", "module": "esm"}, {"statement": "export const fontFamilies", "name": "fontFamilies", "type": "named", "module": "esm"}, {"statement": "export const breakpoints", "name": "breakpoints", "type": "named", "module": "esm"}, {"statement": "export const borderRadius", "name": "borderRadius", "type": "named", "module": "esm"}, {"statement": "export const shadows", "name": "shadows", "type": "named", "module": "esm"}, {"statement": "export const transitions", "name": "transitions", "type": "named", "module": "esm"}, {"statement": "export const zIndex", "name": "zIndex", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T11:16:22.414Z"}, {"path": "packages/shared/src/theme/theme.types.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/theme/theme.types.ts", "size": 2597, "lines": 161, "complexity": 1, "imports": [], "exports": [{"statement": "export interface ColorPalette", "name": "ColorPalette", "type": "named", "module": "esm"}, {"statement": "export interface Spacing", "name": "Spacing", "type": "named", "module": "esm"}, {"statement": "export interface Breakpoints", "name": "Breakpoints", "type": "named", "module": "esm"}, {"statement": "export interface FontSizes", "name": "FontSizes", "type": "named", "module": "esm"}, {"statement": "export interface BorderRadius", "name": "BorderRadius", "type": "named", "module": "esm"}, {"statement": "export interface Shadows", "name": "Shadows", "type": "named", "module": "esm"}, {"statement": "export interface Transitions", "name": "Transitions", "type": "named", "module": "esm"}, {"statement": "export interface ZIndex", "name": "ZIndex", "type": "named", "module": "esm"}, {"statement": "export interface FontWeights", "name": "FontWeights", "type": "named", "module": "esm"}, {"statement": "export interface LineHeights", "name": "LineHeights", "type": "named", "module": "esm"}, {"statement": "export interface FontFamilies", "name": "FontFamilies", "type": "named", "module": "esm"}, {"statement": "export interface Theme", "name": "Theme", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T01:04:41.126Z"}, {"path": "packages/shared/src/components/molecules/TradeTableColumns.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTableColumns.tsx", "size": 11103, "lines": 403, "complexity": 53, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Badge } from '../atoms/Badge'", "path": "../atoms/Badge", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CompleteTradeData, TradeRecord } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const TRADE_COLUMN_IDS", "name": "TRADE_COLUMN_IDS", "type": "named", "module": "esm"}, {"statement": "export interface TableColumn", "name": "TableColumn", "type": "named", "module": "esm"}, {"statement": "export const formatCurrency", "name": "formatCurrency", "type": "named", "module": "esm"}, {"statement": "export const formatPercentage", "name": "formatPercentage", "type": "named", "module": "esm"}, {"statement": "export const formatDate", "name": "formatDate", "type": "named", "module": "esm"}, {"statement": "export const formatTime", "name": "formatTime", "type": "named", "module": "esm"}, {"statement": "export const getTradeTableColumns", "name": "getTradeTableColumns", "type": "named", "module": "esm"}, {"statement": "export const getCompactTradeTableColumns", "name": "getCompactTradeTableColumns", "type": "named", "module": "esm"}, {"statement": "export const getPerformanceTradeTableColumns", "name": "getPerformanceTradeTableColumns", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T16:18:51.760Z"}, {"path": "packages/dashboard/src/features/trade-analysis/types.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/types.ts", "size": 4324, "lines": 171, "complexity": 14, "imports": [{"statement": "import { Trade } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type TradeDirection", "name": "TradeDirection", "type": "named", "module": "esm"}, {"statement": "export type TradeStatus", "name": "TradeStatus", "type": "named", "module": "esm"}, {"statement": "export type TradeTimeframe", "name": "TradeTimeframe", "type": "named", "module": "esm"}, {"statement": "export type TradingSession", "name": "TradingSession", "type": "named", "module": "esm"}, {"statement": "export interface PerformanceMetrics", "name": "PerformanceMetrics", "type": "named", "module": "esm"}, {"statement": "export interface EquityPoint", "name": "EquityPoint", "type": "named", "module": "esm"}, {"statement": "export interface DistributionBar", "name": "DistributionBar", "type": "named", "module": "esm"}, {"statement": "export interface TradeSummary", "name": "TradeSummary", "type": "named", "module": "esm"}, {"statement": "export interface CategoryPerformance", "name": "CategoryPerformance", "type": "named", "module": "esm"}, {"statement": "export interface TimePerformance", "name": "TimePerformance", "type": "named", "module": "esm"}, {"statement": "export interface TradeFilters", "name": "TradeFilters", "type": "named", "module": "esm"}, {"statement": "export interface UserPreferences", "name": "UserPreferences", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysisData", "name": "TradeAnalysisData", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysisState", "name": "TradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export type TradeAnalysisAction", "name": "TradeAnalysisAction", "type": "named", "module": "esm"}, {"statement": "export { Trade }", "name": "Trade ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:16:25.431Z"}, {"path": "packages/dashboard/src/features/daily-guide/types.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/types.ts", "size": 4527, "lines": 214, "complexity": 13, "imports": [], "exports": [{"statement": "export type MarketSentiment", "name": "MarketSentiment", "type": "named", "module": "esm"}, {"statement": "export type TradingPlanPriority", "name": "TradingPlanPriority", "type": "named", "module": "esm"}, {"statement": "export interface MarketIndex", "name": "MarketIndex", "type": "named", "module": "esm"}, {"statement": "export interface EconomicEvent", "name": "EconomicEvent", "type": "named", "module": "esm"}, {"statement": "export interface MarketNewsItem", "name": "MarketNewsItem", "type": "named", "module": "esm"}, {"statement": "export interface MarketOverview", "name": "MarketOverview", "type": "named", "module": "esm"}, {"statement": "export interface TradingPlanItem", "name": "TradingPlanItem", "type": "named", "module": "esm"}, {"statement": "export interface RiskManagement", "name": "RiskManagement", "type": "named", "module": "esm"}, {"statement": "export interface TradingPlan", "name": "TradingPlan", "type": "named", "module": "esm"}, {"statement": "export interface WatchlistItem", "name": "WatchlistItem", "type": "named", "module": "esm"}, {"statement": "export interface KeyPriceLevel", "name": "KeyPriceLevel", "type": "named", "module": "esm"}, {"statement": "export interface DailyGuideData", "name": "DailyGuideData", "type": "named", "module": "esm"}, {"statement": "export interface DailyGuideState", "name": "DailyGuideState", "type": "named", "module": "esm"}, {"statement": "export interface DailyGuidePreferences", "name": "DailyGuidePreferences", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-21T19:08:03.267Z"}, {"path": "packages/dashboard/src/features/trading-dashboard/types/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trading-dashboard/types/index.ts", "size": 1406, "lines": 75, "complexity": 3, "imports": [{"statement": "import { Trade, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface PerformanceMetric", "name": "PerformanceMetric", "type": "named", "module": "esm"}, {"statement": "export interface ChartDataPoint", "name": "ChartDataPoint", "type": "named", "module": "esm"}, {"statement": "export interface SetupPerformance", "name": "SetupPerformance", "type": "named", "module": "esm"}, {"statement": "export interface SessionPerformance", "name": "SessionPerformance", "type": "named", "module": "esm"}, {"statement": "export interface DashboardState", "name": "DashboardState", "type": "named", "module": "esm"}, {"statement": "export { Trade, PerformanceMetrics }", "name": "Trade, PerformanceMetrics ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:17:18.795Z"}, {"path": "packages/dashboard/src/features/trade-journal/types/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/types/index.ts", "size": 5652, "lines": 289, "complexity": 2, "imports": [{"statement": "import { Trade } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type ModelType", "name": "ModelType", "type": "named", "module": "esm"}, {"statement": "export type SessionType", "name": "SessionType", "type": "named", "module": "esm"}, {"statement": "export type SetupType", "name": "SetupType", "type": "named", "module": "esm"}, {"statement": "export type MarketType", "name": "MarketType", "type": "named", "module": "esm"}, {"statement": "export type EntryVersionType", "name": "EntryVersionType", "type": "named", "module": "esm"}, {"statement": "export type SetupCategoryType", "name": "SetupCategoryType", "type": "named", "module": "esm"}, {"statement": "export type StructureSetupType", "name": "StructureSetupType", "type": "named", "module": "esm"}, {"statement": "export type SessionSetupType", "name": "SessionSetupType", "type": "named", "module": "esm"}, {"statement": "export type ModelSetupType", "name": "ModelSetupType", "type": "named", "module": "esm"}, {"statement": "export type LiquidityType", "name": "LiquidityType", "type": "named", "module": "esm"}, {"statement": "export type FVGType", "name": "FVGType", "type": "named", "module": "esm"}, {"statement": "export type DOLTargetType", "name": "DOLTargetType", "type": "named", "module": "esm"}, {"statement": "export type ParentPDArrayType", "name": "ParentPDArrayType", "type": "named", "module": "esm"}, {"statement": "export type ScoreRange", "name": "ScoreRange", "type": "named", "module": "esm"}, {"statement": "export interface PatternQualityCriteria", "name": "PatternQualityCriteria", "type": "named", "module": "esm"}, {"statement": "export interface PatternQualityScore", "name": "PatternQualityScore", "type": "named", "module": "esm"}, {"statement": "export type DOLType", "name": "DOLType", "type": "named", "module": "esm"}, {"statement": "export type DOLStrength", "name": "DOLStrength", "type": "named", "module": "esm"}, {"statement": "export type DOLReaction", "name": "DOLReaction", "type": "named", "module": "esm"}, {"statement": "export type DOLContext", "name": "DOLContext", "type": "named", "module": "esm"}, {"statement": "export interface DOLAnalysis", "name": "DOLAnalysis", "type": "named", "module": "esm"}, {"statement": "export interface TradeJournalState", "name": "TradeJournalState", "type": "named", "module": "esm"}, {"statement": "export interface FilterState", "name": "FilterState", "type": "named", "module": "esm"}, {"statement": "export {\n  Trade,\n  TradeFormData as TradeFormValues, // Alias for backward compatibility\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n  CompleteTradeData,\n  TradeFilters,\n  PerformanceMetrics,\n  Position,\n  Order,\n  MarketData,\n  TradingSession,\n}", "name": "Trade,\n  TradeFormData as TradeFormValues, // Alias for backward compatibility\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n  CompleteTradeData,\n  TradeFilters,\n  PerformanceMetrics,\n  Position,\n  Order,\n  MarketData,\n  TradingSession,\n", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:16:20.494Z"}, {"path": "packages/dashboard/src/features/trade-journal/hooks/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/hooks/index.ts", "size": 1010, "lines": 33, "complexity": 1, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export { useTradeForm }", "name": "useTradeForm ", "type": "default", "module": "esm"}, {"statement": "export {\n  MODEL_TYPE_OPTIONS,\n  SESSION_OPTIONS,\n  SETUP_OPTIONS,\n  MARKET_OPTIONS,\n  ENTRY_VERSION_OPTIONS,\n  PATTERN_QUALITY_OPTIONS,\n}", "name": "MODEL_TYPE_OPTIONS,\n  SESSION_OPTIONS,\n  SETUP_OPTIONS,\n  MARKET_OPTIONS,\n  ENTRY_VERSION_OPTIONS,\n  PATTERN_QUALITY_OPTIONS,\n", "type": "default", "module": "esm"}, {"statement": "export { useTradeFormData }", "name": "useTradeFormData ", "type": "default", "module": "esm"}, {"statement": "export { useTradeValidation }", "name": "useTradeValidation ", "type": "default", "module": "esm"}, {"statement": "export { useTradeCalculations }", "name": "useTradeCalculations ", "type": "default", "module": "esm"}, {"statement": "export { useTradeSubmission }", "name": "useTradeSubmission ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:40:48.657Z"}, {"path": "packages/dashboard/src/features/trade-analysis/types/index.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/types/index.ts", "size": 487, "lines": 24, "complexity": 2, "imports": [{"statement": "import { Trade, TradeFormData, PerformanceMetrics } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface EquityPoint", "name": "EquityPoint", "type": "named", "module": "esm"}, {"statement": "export interface DistributionBar", "name": "DistributionBar", "type": "named", "module": "esm"}, {"statement": "export { Trade, TradeFormData, PerformanceMetrics }", "name": "Trade, TradeFormData, PerformanceMetrics ", "type": "default", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:16:48.353Z"}], "tests": [{"path": "e2e/trade-analysis.spec.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/e2e/trade-analysis.spec.ts", "size": 3623, "lines": 95, "complexity": 1, "imports": [{"statement": "import { test, expect } from '@playwright/test'", "path": "@playwright/test", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@playwright/test", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:31:48.539Z"}, {"path": "e2e/shared-components.spec.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/e2e/shared-components.spec.ts", "size": 1768, "lines": 49, "complexity": 1, "imports": [{"statement": "import { test, expect } from '@playwright/test'", "path": "@playwright/test", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@playwright/test"], "lastModified": "2025-05-21T16:53:50.882Z"}, {"path": "e2e/daily-guide.spec.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/e2e/daily-guide.spec.ts", "size": 1667, "lines": 49, "complexity": 1, "imports": [{"statement": "import { test, expect } from '@playwright/test'", "path": "@playwright/test", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@playwright/test"], "lastModified": "2025-05-21T16:53:16.261Z"}, {"path": "tests/e2e/trade-workflows.spec.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/tests/e2e/trade-workflows.spec.ts", "size": 13999, "lines": 326, "complexity": 8, "imports": [{"statement": "import { test, expect } from '@playwright/test'", "path": "@playwright/test", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@playwright/test"], "lastModified": "2025-05-24T10:27:47.553Z"}, {"path": "packages/dashboard/src/routes/routes.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/routes/routes.test.tsx", "size": 4626, "lines": 152, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { describe, it, expect, vi, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MemoryRouter } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { f1Theme } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { AppRoutes } from '../routes'", "path": "../routes", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-23T14:52:32.868Z"}, {"path": "packages/shared/src/state/__tests__/createStoreContext.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/state/__tests__/createStoreContext.test.tsx", "size": 5811, "lines": 208, "complexity": 11, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { createStoreContext } from '../createStoreContext'", "path": "../createStoreContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react"], "lastModified": "2025-05-21T18:35:57.752Z"}, {"path": "packages/shared/src/services/__tests__/tradeStorage.test.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/__tests__/tradeStorage.test.ts", "size": 18427, "lines": 617, "complexity": 15, "imports": [{"statement": "import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { tradeStorageService } from '../tradeStorage'", "path": "../tradeStorage", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  CompleteTradeData,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n} from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["vitest"], "lastModified": "2025-05-24T19:07:09.177Z"}, {"path": "packages/shared/src/hooks/__tests__/useErrorHandler.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/__tests__/useErrorHandler.test.tsx", "size": 2291, "lines": 80, "complexity": 1, "imports": [{"statement": "import { renderHook, act } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useErrorHandler } from '../useErrorHandler'", "path": "../useErrorHandler", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@testing-library/react", "vitest"], "lastModified": "2025-05-22T14:55:08.272Z"}, {"path": "packages/dashboard/src/features/trade-journal/TradeForm.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/TradeForm.test.tsx", "size": 6799, "lines": 234, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent, waitFor } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { describe, it, expect, vi, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { MemoryRouter, Routes, Route } from 'react-router-dom'", "path": "react-router-dom", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { f1Theme } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import TradeForm from './TradeForm'", "path": "./TradeForm", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest", "react-router-dom", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T18:26:42.520Z"}, {"path": "packages/dashboard/src/components/__tests__/FeatureErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/__tests__/FeatureErrorBoundary.test.tsx", "size": 3842, "lines": 118, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import FeatureErrorBoundary from '../FeatureErrorBoundary'", "path": "../FeatureErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "@adhd-trading-dashboard/shared", "vitest"], "lastModified": "2025-05-22T15:10:10.728Z"}, {"path": "packages/dashboard/src/components/__tests__/AppErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/__tests__/AppErrorBoundary.test.tsx", "size": 2932, "lines": 93, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import AppErrorBoundary from '../AppErrorBoundary'", "path": "../AppErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "@adhd-trading-dashboard/shared", "vitest"], "lastModified": "2025-05-22T15:11:20.303Z"}, {"path": "packages/shared/src/components/molecules/__tests__/UnifiedErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/__tests__/UnifiedErrorBoundary.test.tsx", "size": 5226, "lines": 171, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { describe, it, expect, vi, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  UnifiedErrorBoundary,\n  AppErrorBoundary,\n  FeatureErrorBoundary,\n} from '../UnifiedErrorBoundary'", "path": "../UnifiedErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest"], "lastModified": "2025-05-24T19:18:52.779Z"}, {"path": "packages/shared/src/components/molecules/__tests__/ErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/__tests__/ErrorBoundary.test.tsx", "size": 2948, "lines": 103, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '../../../theme'", "path": "../../../theme", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ErrorBoundary } from '../ErrorBoundary'", "path": "../ErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest"], "lastModified": "2025-05-24T19:09:14.622Z"}, {"path": "packages/dashboard/src/features/trade-journal/tests/useTradeSubmission.test.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/tests/useTradeSubmission.test.ts", "size": 13020, "lines": 444, "complexity": 1, "imports": [{"statement": "import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { renderHook, act } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useTradeSubmission } from '../hooks/useTradeSubmission'", "path": "../hooks/useTradeSubmission", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeFormValues, Trade } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { tradeStorageService } from '@adhd-trading-dashboard/shared/services'", "path": "@adhd-trading-dashboard/shared/services", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["vitest", "@testing-library/react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T10:26:01.453Z"}, {"path": "packages/dashboard/src/features/trade-journal/components/TabPanel.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/TabPanel.test.tsx", "size": 3905, "lines": 123, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { describe, it, expect, vi } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import TabPanel from './TabPanel'", "path": "./TabPanel", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { f1Theme } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-23T14:51:35.706Z"}, {"path": "packages/dashboard/src/features/trade-analysis/trade-tests/TradeAnalysis.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/trade-tests/TradeAnalysis.test.tsx", "size": 7208, "lines": 330, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, waitFor, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeAnalysis } from '../index'", "path": "../index", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { fetchTradeAnalysisData } from '../services/tradeAnalysisApi'", "path": "../services/tradeAnalysisApi", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:32:07.209Z"}, {"path": "packages/dashboard/src/features/daily-guide/__tests__/DailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/__tests__/DailyGuide.test.tsx", "size": 5225, "lines": 214, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, waitFor, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DailyGuide } from '../index'", "path": "../index", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { fetchDailyGuideData } from '../api/dailyGuideApi'", "path": "../api/dailyGuideApi", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "styled-components"], "lastModified": "2025-05-25T09:22:17.184Z"}, {"path": "packages/dashboard/src/features/trade-analysis/components/trade-tests/TradeAnalysis.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/components/trade-tests/TradeAnalysis.test.tsx", "size": 2356, "lines": 93, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradeAnalysis } from '../TradeAnalysis'", "path": "../TradeAnalysis", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ApiProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T18:34:51.305Z"}, {"path": "packages/dashboard/src/features/daily-guide/hooks/__tests__/useDailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/hooks/__tests__/useDailyGuide.test.tsx", "size": 3970, "lines": 136, "complexity": 1, "imports": [{"statement": "import { renderHook, act } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useDailyGuide } from '../useDailyGuide'", "path": "../useDailyGuide", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { DailyGuideProvider } from '../../state'", "path": "../../state", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@testing-library/react", "react", "vitest"], "lastModified": "2025-05-24T19:22:51.521Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/__tests__/DailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/__tests__/DailyGuide.test.tsx", "size": 3890, "lines": 151, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DailyGuide } from '../DailyGuide'", "path": "../DailyGuide", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { theme } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T19:17:17.255Z"}, {"path": "packages/shared/src/state/__tests__/createStoreContext.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/state/__tests__/createStoreContext.test.tsx", "size": 5811, "lines": 208, "complexity": 11, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { createStoreContext } from '../createStoreContext'", "path": "../createStoreContext", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react"], "lastModified": "2025-05-21T18:35:57.752Z"}, {"path": "packages/shared/src/services/__tests__/tradeStorage.test.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/__tests__/tradeStorage.test.ts", "size": 18427, "lines": 617, "complexity": 15, "imports": [{"statement": "import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { tradeStorageService } from '../tradeStorage'", "path": "../tradeStorage", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  CompleteTradeData,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n} from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["vitest"], "lastModified": "2025-05-24T19:07:09.177Z"}, {"path": "packages/shared/src/hooks/__tests__/useErrorHandler.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/hooks/__tests__/useErrorHandler.test.tsx", "size": 2291, "lines": 80, "complexity": 1, "imports": [{"statement": "import { renderHook, act } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useErrorHandler } from '../useErrorHandler'", "path": "../useErrorHandler", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@testing-library/react", "vitest"], "lastModified": "2025-05-22T14:55:08.272Z"}, {"path": "packages/dashboard/src/components/__tests__/FeatureErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/__tests__/FeatureErrorBoundary.test.tsx", "size": 3842, "lines": 118, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import FeatureErrorBoundary from '../FeatureErrorBoundary'", "path": "../FeatureErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "@adhd-trading-dashboard/shared", "vitest"], "lastModified": "2025-05-22T15:10:10.728Z"}, {"path": "packages/dashboard/src/components/__tests__/AppErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/__tests__/AppErrorBoundary.test.tsx", "size": 2932, "lines": 93, "complexity": 6, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import AppErrorBoundary from '../AppErrorBoundary'", "path": "../AppErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "@adhd-trading-dashboard/shared", "vitest"], "lastModified": "2025-05-22T15:11:20.303Z"}, {"path": "packages/shared/src/components/molecules/__tests__/UnifiedErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/__tests__/UnifiedErrorBoundary.test.tsx", "size": 5226, "lines": 171, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { describe, it, expect, vi, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  UnifiedErrorBoundary,\n  AppErrorBoundary,\n  FeatureErrorBoundary,\n} from '../UnifiedErrorBoundary'", "path": "../UnifiedErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest"], "lastModified": "2025-05-24T19:18:52.779Z"}, {"path": "packages/shared/src/components/molecules/__tests__/ErrorBoundary.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/__tests__/ErrorBoundary.test.tsx", "size": 2948, "lines": 103, "complexity": 3, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from '../../../theme'", "path": "../../../theme", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ErrorBoundary } from '../ErrorBoundary'", "path": "../ErrorBoundary", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "vitest"], "lastModified": "2025-05-24T19:09:14.622Z"}, {"path": "packages/dashboard/src/features/daily-guide/__tests__/DailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/__tests__/DailyGuide.test.tsx", "size": 5225, "lines": 214, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen, waitFor, fireEvent } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DailyGuide } from '../index'", "path": "../index", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { fetchDailyGuideData } from '../api/dailyGuideApi'", "path": "../api/dailyGuideApi", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [], "dependencies": ["react", "@testing-library/react", "styled-components"], "lastModified": "2025-05-25T09:22:17.184Z"}, {"path": "packages/dashboard/src/features/daily-guide/hooks/__tests__/useDailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/hooks/__tests__/useDailyGuide.test.tsx", "size": 3970, "lines": 136, "complexity": 1, "imports": [{"statement": "import { renderHook, act } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { useDailyGuide } from '../useDailyGuide'", "path": "../useDailyGuide", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { DailyGuideProvider } from '../../state'", "path": "../../state", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { vi, describe, it, expect, beforeEach } from 'vitest'", "path": "vitest", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["@testing-library/react", "react", "vitest"], "lastModified": "2025-05-24T19:22:51.521Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/__tests__/DailyGuide.test.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/__tests__/DailyGuide.test.tsx", "size": 3890, "lines": 151, "complexity": 2, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { render, screen } from '@testing-library/react'", "path": "@testing-library/react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { DailyGuide } from '../DailyGuide'", "path": "../DailyGuide", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { ThemeProvider } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { theme } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [], "dependencies": ["react", "@testing-library/react", "styled-components", "@adhd-trading-dashboard/shared"], "lastModified": "2025-05-21T19:17:17.255Z"}], "configs": [{"path": "vitest.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/vitest.config.ts"}, {"path": "playwright.config.ts", "type": "unknown", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/playwright.config.ts"}, {"path": "babel.config.js", "type": "babel", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/babel.config.js"}, {"path": "scripts/scripts.config.js", "type": "unknown", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/scripts.config.js"}, {"path": "scripts/utils/scripts.config.js", "type": "unknown", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/scripts/utils/scripts.config.js"}, {"path": "packages/shared/webpack.config.js", "type": "webpack", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/webpack.config.js"}, {"path": "packages/shared/vite.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/vite.config.ts"}, {"path": "packages/dashboard/vite.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/vite.config.ts"}, {"path": ".eslintrc.js", "type": "eslint", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/.eslintrc.js"}, {"path": "tsconfig.json", "type": "typescript", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/tsconfig.json"}, {"path": "packages/shared/tsconfig.json", "type": "typescript", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/tsconfig.json"}, {"path": "packages/shared/tsconfig.build.json", "type": "typescript", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/tsconfig.build.json"}, {"path": "packages/dashboard/tsconfig.json", "type": "typescript", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/tsconfig.json"}, {"path": "babel.config.js", "type": "babel", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/babel.config.js"}, {"path": "packages/shared/vite.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/vite.config.ts"}, {"path": "packages/dashboard/vite.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/vite.config.ts"}, {"path": "packages/shared/webpack.config.js", "type": "webpack", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/webpack.config.js"}, {"path": "vitest.config.ts", "type": "vite", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/vitest.config.ts"}], "dependencies": {}, "metrics": {"totalFiles": 222, "totalLines": 28942, "averageComplexity": 10.5, "largestFiles": [{"path": "packages/shared/src/services/tradeStorage.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/tradeStorage.ts", "size": 28474, "lines": 830, "complexity": 61, "imports": [{"statement": "import {\n  PerformanceMetrics,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n  CompleteTradeData,\n  TradeFilters,\n  Trade, // Legacy interface for backward compatibility\n} from '../types/trading'", "path": "../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const tradeStorage", "name": "tradeStorage", "type": "named", "module": "esm"}, {"statement": "export const tradeStorageService", "name": "tradeStorageService", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T16:56:57.395Z"}, {"path": "code-health/dynamic-scripts-cleanup.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/code-health/dynamic-scripts-cleanup.js", "size": 22355, "lines": 785, "complexity": 88, "imports": [{"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { execSync } from 'child_process'", "path": "child_process", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import ScriptAnalyzer from './enhanced-refactor-analyzer.js'", "path": "./enhanced-refactor-analyzer.js", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { program } from 'commander'", "path": "commander", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export {\n  // Add function exports here\n}", "name": "// Add function exports here\n", "type": "default", "module": "esm"}], "dependencies": ["fs", "path", "child_process", "commander"], "lastModified": "2025-05-23T11:09:23.237Z"}, {"path": "packages/shared/src/components/molecules/Table.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Table.tsx", "size": 13184, "lines": 474, "complexity": 75, "imports": [{"statement": "import React, { useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TableColumn", "name": "TableColumn", "type": "named", "module": "esm"}, {"statement": "export interface TableProps", "name": "TableProps", "type": "named", "module": "esm"}, {"statement": "export function Table", "name": "Table", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T08:35:40.197Z"}, {"path": "packages/shared/src/components/molecules/TradeTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTable.tsx", "size": 12911, "lines": 439, "complexity": 96, "imports": [{"statement": "import React, { useMemo, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CompleteTradeData, TradeFilters } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  TableColumn,\n  getTradeTableColumns,\n  getCompactTradeTableColumns,\n  getPerformanceTradeTableColumns,\n} from './TradeTableColumns'", "path": "./TradeTableColumns", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableRow } from './TradeTableRow'", "path": "./TradeTableRow", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableFilters } from './TradeTableFilters'", "path": "./TradeTableFilters", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableProps", "name": "TradeTableProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTable", "name": "TradeTable", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:25:39.080Z"}, {"path": "packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "size": 11446, "lines": 435, "complexity": 71, "imports": [{"statement": "import {\n  createStoreContext,\n  createSelector,\n  persistState,\n  Trade,\n  TradeFormData,\n  CompleteTradeData,\n} from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export enum TradeAnalysisActionTypes", "name": "TradeAnalysisActionTypes", "type": "named", "module": "esm"}, {"statement": "export interface SetFilterAction", "name": "SetFilterAction", "type": "named", "module": "esm"}, {"statement": "export interface ClearFiltersAction", "name": "ClearFiltersAction", "type": "named", "module": "esm"}, {"statement": "export interface SetSortAction", "name": "SetSortAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageAction", "name": "SetPageAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageSizeAction", "name": "SetPageSizeAction", "type": "named", "module": "esm"}, {"statement": "export interface SetTradesAction", "name": "SetTradesAction", "type": "named", "module": "esm"}, {"statement": "export interface SetLoadingAction", "name": "SetLoadingAction", "type": "named", "module": "esm"}, {"statement": "export interface SetErrorAction", "name": "SetErrorAction", "type": "named", "module": "esm"}, {"statement": "export type TradeAnalysisAction", "name": "TradeAnalysisAction", "type": "named", "module": "esm"}, {"statement": "export interface TradeFilter", "name": "TradeFilter", "type": "named", "module": "esm"}, {"statement": "export interface TradeSort", "name": "TradeSort", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysisState", "name": "TradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const initialTradeAnalysisState", "name": "initialTradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisReducer", "name": "tradeAnalysisReducer", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisActions", "name": "tradeAnalysisActions", "type": "named", "module": "esm"}, {"statement": "export const selectTrades", "name": "selectTrades", "type": "named", "module": "esm"}, {"statement": "export const selectFilter", "name": "selectFilter", "type": "named", "module": "esm"}, {"statement": "export const selectSort", "name": "selectSort", "type": "named", "module": "esm"}, {"statement": "export const selectPage", "name": "selectPage", "type": "named", "module": "esm"}, {"statement": "export const selectPageSize", "name": "selectPageSize", "type": "named", "module": "esm"}, {"statement": "export const selectIsLoading", "name": "selectIsLoading", "type": "named", "module": "esm"}, {"statement": "export const selectError", "name": "selectError", "type": "named", "module": "esm"}, {"statement": "export const selectFilteredTrades", "name": "selectFilteredTrades", "type": "named", "module": "esm"}, {"statement": "export const selectSortedTrades", "name": "selectSortedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectPaginatedTrades", "name": "selectPaginatedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectTotalPages", "name": "selectTotalPages", "type": "named", "module": "esm"}, {"statement": "export const selectTradeSummary", "name": "selectTradeSummary", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:46:44.711Z"}, {"path": "packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx", "size": 11355, "lines": 432, "complexity": 1, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { ProfitLossCell } from './ProfitLossCell'", "path": "./ProfitLossCell", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const Default", "name": "<PERSON><PERSON><PERSON>", "type": "named", "module": "esm"}, {"statement": "export const BigWin", "name": "BigWin", "type": "named", "module": "esm"}, {"statement": "export const SmallWin", "name": "SmallWin", "type": "named", "module": "esm"}, {"statement": "export const Breakeven", "name": "Breakeven", "type": "named", "module": "esm"}, {"statement": "export const SmallLoss", "name": "SmallLoss", "type": "named", "module": "esm"}, {"statement": "export const BigLoss", "name": "BigLoss", "type": "named", "module": "esm"}, {"statement": "export const Loading", "name": "Loading", "type": "named", "module": "esm"}, {"statement": "export const Interactive", "name": "Interactive", "type": "named", "module": "esm"}, {"statement": "export const SizeVariants", "name": "SizeVariants", "type": "named", "module": "esm"}, {"statement": "export const TradingScenarios", "name": "TradingScenarios", "type": "named", "module": "esm"}, {"statement": "export const TableContext", "name": "TableContext", "type": "named", "module": "esm"}, {"statement": "export const EdgeCases", "name": "EdgeCases", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T20:15:11.152Z"}, {"path": "packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "size": 13946, "lines": 415, "complexity": 57, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  TradeAnalysisData,\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const fetchTradeAnalysisData", "name": "fetchTradeAnalysisData", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:33:21.305Z"}, {"path": "packages/shared/src/components/atoms/Input.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Input.tsx", "size": 9450, "lines": 373, "complexity": 78, "imports": [{"statement": "import React, { useState, useRef } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface InputProps", "name": "InputProps", "type": "named", "module": "esm"}, {"statement": "export const Input", "name": "Input", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:59:57.382Z"}, {"path": "packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx", "size": 10971, "lines": 371, "complexity": 27, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Card, Badge, Button, Input, FormField } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { TradingPlan as TradingPlanType, TradingPlanItem, TradingPlanPriority } from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import styled from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface TradingPlanProps", "name": "TradingPlanProps", "type": "named", "module": "esm"}, {"statement": "export const TradingPlan", "name": "TradingPlan", "type": "named", "module": "esm"}], "dependencies": ["react", "@adhd-trading-dashboard/shared", "styled-components"], "lastModified": "2025-05-25T08:25:57.845Z"}, {"path": "packages/shared/src/components/atoms/Select.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Select.tsx", "size": 9863, "lines": 358, "complexity": 61, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface SelectOption", "name": "SelectOption", "type": "named", "module": "esm"}, {"statement": "export interface SelectProps", "name": "SelectProps", "type": "named", "module": "esm"}, {"statement": "export const Select", "name": "Select", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.815Z"}], "mostComplexFiles": [{"path": "packages/shared/src/components/molecules/TradeTable.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTable.tsx", "size": 12911, "lines": 439, "complexity": 96, "imports": [{"statement": "import React, { useMemo, useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { CompleteTradeData, TradeFilters } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import {\n  TableColumn,\n  getTradeTableColumns,\n  getCompactTradeTableColumns,\n  getPerformanceTradeTableColumns,\n} from './TradeTableColumns'", "path": "./TradeTableColumns", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableRow } from './TradeTableRow'", "path": "./TradeTableRow", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TradeTableFilters } from './TradeTableFilters'", "path": "./TradeTableFilters", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableProps", "name": "TradeTableProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTable", "name": "TradeTable", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:25:39.080Z"}, {"path": "code-health/dynamic-scripts-cleanup.js", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/code-health/dynamic-scripts-cleanup.js", "size": 22355, "lines": 785, "complexity": 88, "imports": [{"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { execSync } from 'child_process'", "path": "child_process", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import ScriptAnalyzer from './enhanced-refactor-analyzer.js'", "path": "./enhanced-refactor-analyzer.js", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import fs from 'fs/promises'", "path": "fs/promises", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { program } from 'commander'", "path": "commander", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import path from 'path'", "path": "path", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export {\n  // Add function exports here\n}", "name": "// Add function exports here\n", "type": "default", "module": "esm"}], "dependencies": ["fs", "path", "child_process", "commander"], "lastModified": "2025-05-23T11:09:23.237Z"}, {"path": "packages/shared/src/components/atoms/Input.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Input.tsx", "size": 9450, "lines": 373, "complexity": 78, "imports": [{"statement": "import React, { useState, useRef } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface InputProps", "name": "InputProps", "type": "named", "module": "esm"}, {"statement": "export const Input", "name": "Input", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:59:57.382Z"}, {"path": "packages/shared/src/components/molecules/Table.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/Table.tsx", "size": 13184, "lines": 474, "complexity": 75, "imports": [{"statement": "import React, { useMemo } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { Button } from '../atoms/Button'", "path": "../atoms/Button", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TableColumn", "name": "TableColumn", "type": "named", "module": "esm"}, {"statement": "export interface TableProps", "name": "TableProps", "type": "named", "module": "esm"}, {"statement": "export function Table", "name": "Table", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T08:35:40.197Z"}, {"path": "packages/shared/src/components/molecules/TradeTableRow.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/molecules/TradeTableRow.tsx", "size": 8477, "lines": 289, "complexity": 74, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import { CompleteTradeData } from '../../types/trading'", "path": "../../types/trading", "type": "esm", "isRelative": true, "isExternal": false}, {"statement": "import { TableColumn } from './TradeTableColumns'", "path": "./TradeTableColumns", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export interface TradeTableRowProps", "name": "TradeTableRowProps", "type": "named", "module": "esm"}, {"statement": "export const TradeTableRow", "name": "TradeTableRow", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-24T09:26:23.474Z"}, {"path": "packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "size": 11446, "lines": 435, "complexity": 71, "imports": [{"statement": "import {\n  createStoreContext,\n  createSelector,\n  persistState,\n  Trade,\n  TradeFormData,\n  CompleteTradeData,\n} from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export enum TradeAnalysisActionTypes", "name": "TradeAnalysisActionTypes", "type": "named", "module": "esm"}, {"statement": "export interface SetFilterAction", "name": "SetFilterAction", "type": "named", "module": "esm"}, {"statement": "export interface ClearFiltersAction", "name": "ClearFiltersAction", "type": "named", "module": "esm"}, {"statement": "export interface SetSortAction", "name": "SetSortAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageAction", "name": "SetPageAction", "type": "named", "module": "esm"}, {"statement": "export interface SetPageSizeAction", "name": "SetPageSizeAction", "type": "named", "module": "esm"}, {"statement": "export interface SetTradesAction", "name": "SetTradesAction", "type": "named", "module": "esm"}, {"statement": "export interface SetLoadingAction", "name": "SetLoadingAction", "type": "named", "module": "esm"}, {"statement": "export interface SetErrorAction", "name": "SetErrorAction", "type": "named", "module": "esm"}, {"statement": "export type TradeAnalysisAction", "name": "TradeAnalysisAction", "type": "named", "module": "esm"}, {"statement": "export interface TradeFilter", "name": "TradeFilter", "type": "named", "module": "esm"}, {"statement": "export interface TradeSort", "name": "TradeSort", "type": "named", "module": "esm"}, {"statement": "export interface TradeAnalysisState", "name": "TradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const initialTradeAnalysisState", "name": "initialTradeAnalysisState", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisReducer", "name": "tradeAnalysisReducer", "type": "named", "module": "esm"}, {"statement": "export const tradeAnalysisActions", "name": "tradeAnalysisActions", "type": "named", "module": "esm"}, {"statement": "export const selectTrades", "name": "selectTrades", "type": "named", "module": "esm"}, {"statement": "export const selectFilter", "name": "selectFilter", "type": "named", "module": "esm"}, {"statement": "export const selectSort", "name": "selectSort", "type": "named", "module": "esm"}, {"statement": "export const selectPage", "name": "selectPage", "type": "named", "module": "esm"}, {"statement": "export const selectPageSize", "name": "selectPageSize", "type": "named", "module": "esm"}, {"statement": "export const selectIsLoading", "name": "selectIsLoading", "type": "named", "module": "esm"}, {"statement": "export const selectError", "name": "selectError", "type": "named", "module": "esm"}, {"statement": "export const selectFilteredTrades", "name": "selectFilteredTrades", "type": "named", "module": "esm"}, {"statement": "export const selectSortedTrades", "name": "selectSortedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectPaginatedTrades", "name": "selectPaginatedTrades", "type": "named", "module": "esm"}, {"statement": "export const selectTotalPages", "name": "selectTotalPages", "type": "named", "module": "esm"}, {"statement": "export const selectTradeSummary", "name": "selectTradeSummary", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:46:44.711Z"}, {"path": "packages/shared/src/components/atoms/Badge.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Badge.tsx", "size": 7337, "lines": 265, "complexity": 63, "imports": [{"statement": "import React from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export type BadgeVariant", "name": "BadgeVariant", "type": "named", "module": "esm"}, {"statement": "export type BadgeSize", "name": "BadgeSize", "type": "named", "module": "esm"}, {"statement": "export interface BadgeProps", "name": "BadgeProps", "type": "named", "module": "esm"}, {"statement": "export const Badge", "name": "Badge", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-22T13:56:41.405Z"}, {"path": "packages/shared/src/services/tradeStorage.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/services/tradeStorage.ts", "size": 28474, "lines": 830, "complexity": 61, "imports": [{"statement": "import {\n  PerformanceMetrics,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysis,\n  CompleteTradeData,\n  TradeFilters,\n  Trade, // Legacy interface for backward compatibility\n} from '../types/trading'", "path": "../types/trading", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const tradeStorage", "name": "tradeStorage", "type": "named", "module": "esm"}, {"statement": "export const tradeStorageService", "name": "tradeStorageService", "type": "named", "module": "esm"}], "dependencies": [], "lastModified": "2025-05-24T16:56:57.395Z"}, {"path": "packages/shared/src/components/atoms/Select.tsx", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/shared/src/components/atoms/Select.tsx", "size": 9863, "lines": 358, "complexity": 61, "imports": [{"statement": "import React, { useState } from 'react'", "path": "react", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import styled, { css } from 'styled-components'", "path": "styled-components", "type": "esm", "isRelative": false, "isExternal": true}], "exports": [{"statement": "export interface SelectOption", "name": "SelectOption", "type": "named", "module": "esm"}, {"statement": "export interface SelectProps", "name": "SelectProps", "type": "named", "module": "esm"}, {"statement": "export const Select", "name": "Select", "type": "named", "module": "esm"}], "dependencies": ["react", "styled-components"], "lastModified": "2025-05-23T21:47:27.815Z"}, {"path": "packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "fullPath": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "size": 13946, "lines": 415, "complexity": 57, "imports": [{"statement": "import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared'", "path": "@adhd-trading-dashboard/shared", "type": "esm", "isRelative": false, "isExternal": true}, {"statement": "import {\n  TradeAnalysisData,\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n} from '../types'", "path": "../types", "type": "esm", "isRelative": true, "isExternal": false}], "exports": [{"statement": "export const fetchTradeAnalysisData", "name": "fetchTradeAnalysisData", "type": "named", "module": "esm"}], "dependencies": ["@adhd-trading-dashboard/shared"], "lastModified": "2025-05-24T16:33:21.305Z"}], "testCoverage": 14, "moduleTypes": {"esm": 176, "cjs": 7, "mixed": 0}, "categoryBreakdown": {"components": 116, "hooks": 15, "utilities": 80, "services": 11, "tests": 30}}, "recommendations": [{"type": "refactoring", "priority": "high", "title": "Split large files", "description": "Several files exceed 300 lines and should be split into smaller modules", "files": ["packages/shared/src/services/tradeStorage.ts", "code-health/dynamic-scripts-cleanup.js", "packages/shared/src/components/molecules/Table.tsx", "packages/shared/src/components/molecules/TradeTable.tsx", "packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts", "packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx", "packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts", "packages/shared/src/components/atoms/Input.tsx", "packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx", "packages/shared/src/components/atoms/Select.tsx"]}, {"type": "testing", "priority": "high", "title": "Increase test coverage", "description": "Test coverage is 14%. Aim for at least 70%."}]}