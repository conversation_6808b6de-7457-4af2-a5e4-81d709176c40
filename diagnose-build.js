#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import chalk from 'chalk';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log(chalk.blue.bold('🔍 ADHD Trading Dashboard Build Diagnostic Tool'));
console.log(chalk.blue('=====================================================\n'));

// Helper functions
const log = {
  success: (msg) => console.log(chalk.green('✅ ' + msg)),
  error: (msg) => console.log(chalk.red('❌ ' + msg)),
  warning: (msg) => console.log(chalk.yellow('⚠️  ' + msg)),
  info: (msg) => console.log(chalk.blue('ℹ️  ' + msg)),
  section: (msg) => console.log(chalk.magenta.bold('\n📋 ' + msg + '\n' + '='.repeat(50)))
};

const runCommand = (cmd, cwd = process.cwd(), suppressError = false) => {
  try {
    const result = execSync(cmd, { 
      cwd, 
      encoding: 'utf8', 
      stdio: suppressError ? 'pipe' : 'inherit' 
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout };
  }
};

const checkFileExists = (filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch {
    return false;
  }
};

const readJsonFile = (filePath) => {
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch {
    return null;
  }
};

// Main diagnostic functions
async function checkProjectStructure() {
  log.section('Project Structure Analysis');
  
  const requiredFiles = [
    'package.json',
    'packages/shared/package.json',
    'packages/dashboard/package.json',
    'packages/shared/vite.config.ts',
    'packages/shared/src/index.ts'
  ];

  requiredFiles.forEach(file => {
    if (checkFileExists(file)) {
      log.success(`Found: ${file}`);
    } else {
      log.error(`Missing: ${file}`);
    }
  });
}

async function analyzeDependencies() {
  log.section('Dependency Analysis');
  
  const rootPkg = readJsonFile('package.json');
  const sharedPkg = readJsonFile('packages/shared/package.json');
  
  if (!rootPkg || !sharedPkg) {
    log.error('Could not read package.json files');
    return;
  }

  log.info('Root TypeScript version: ' + (rootPkg.devDependencies?.typescript || 'Not found'));
  log.info('Shared TypeScript version: ' + (sharedPkg.devDependencies?.typescript || 'Not found'));
  
  // Check for vite-plugin-dts
  const hasVitePluginDts = rootPkg.devDependencies?.['vite-plugin-dts'] || 
                          sharedPkg.devDependencies?.['vite-plugin-dts'];
  
  if (hasVitePluginDts) {
    log.success(`vite-plugin-dts found: ${hasVitePluginDts}`);
  } else {
    log.error('vite-plugin-dts not found');
  }

  // Check styled-components versions
  const rootStyled = rootPkg.dependencies?.['styled-components'];
  const sharedStyled = sharedPkg.dependencies?.['styled-components'] || sharedPkg.peerDependencies?.['styled-components'];
  
  log.info(`Root styled-components: ${rootStyled || 'Not found'}`);
  log.info(`Shared styled-components: ${sharedStyled || 'Not found'}`);
  
  if (rootStyled && sharedStyled && rootStyled !== sharedStyled) {
    log.warning('styled-components version mismatch detected!');
  }
}

async function checkViteConfig() {
  log.section('Vite Configuration Analysis');
  
  const viteConfigPath = 'packages/shared/vite.config.ts';
  if (!checkFileExists(viteConfigPath)) {
    log.error('Vite config not found');
    return;
  }

  const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
  
  // Check for key configuration elements
  const checks = [
    { pattern: /import.*dts.*from.*vite-plugin-dts/, name: 'dts plugin import' },
    { pattern: /dts\(/, name: 'dts plugin usage' },
    { pattern: /skipDiagnostics:\s*true/, name: 'skipDiagnostics: true' },
    { pattern: /insertTypesEntry:\s*true/, name: 'insertTypesEntry: true' },
    { pattern: /rollupTypes:\s*true/, name: 'rollupTypes: true' }
  ];

  checks.forEach(check => {
    if (check.pattern.test(viteConfig)) {
      log.success(check.name);
    } else {
      log.warning(`Missing or incorrect: ${check.name}`);
    }
  });
}

async function testViteBuild() {
  log.section('Vite Build Test');
  
  log.info('Testing Vite build in shared package...');
  
  // Clean dist first
  runCommand('rm -rf dist', 'packages/shared', true);
  
  const buildResult = runCommand('yarn build', 'packages/shared', true);
  
  if (buildResult.success) {
    log.success('Vite build completed');
    
    // Check what was generated
    log.info('Checking generated files...');
    try {
      const distFiles = fs.readdirSync('packages/shared/dist');
      distFiles.forEach(file => {
        const fullPath = path.join('packages/shared/dist', file);
        const stats = fs.statSync(fullPath);
        log.info(`Generated: ${file} (${stats.isDirectory() ? 'directory' : `${stats.size} bytes`})`);
      });
      
      // Specifically check for declaration files
      const declarationFiles = distFiles.filter(file => file.endsWith('.d.ts'));
      if (declarationFiles.length > 0) {
        log.success(`Declaration files found: ${declarationFiles.join(', ')}`);
      } else {
        log.error('No declaration files (.d.ts) found in dist');
      }
    } catch (err) {
      log.error('Could not read dist directory');
    }
    
  } else {
    log.error('Vite build failed');
    console.log('Build output:');
    console.log(buildResult.output);
  }
}

async function testDirectDeclaration() {
  log.section('Direct Declaration Generation Test');
  
  log.info('Testing direct tsc declaration generation...');
  
  const tscResult = runCommand(
    'npx tsc --declaration --emitDeclarationOnly --skipLibCheck --outDir dist',
    'packages/shared',
    true
  );

  if (tscResult.success) {
    log.success('Direct tsc declaration generation worked!');
    log.info('This means we can use tsc as a fallback');
  } else {
    log.error('Direct tsc declaration generation also failed');
    console.log('Error output:');
    console.log(tscResult.output?.split('\n').slice(0, 10).join('\n'));
  }
}

// Run all diagnostics
async function runDiagnostics() {
  try {
    await checkProjectStructure();
    await analyzeDependencies();
    await checkViteConfig();
    await testViteBuild();
    await testDirectDeclaration();
    
    log.section('Summary & Next Steps');
    log.info('Check the results above to identify the specific issue');
    
  } catch (error) {
    log.error(`Diagnostic failed: ${error.message}`);
  }
}

// Run diagnostics
runDiagnostics();
