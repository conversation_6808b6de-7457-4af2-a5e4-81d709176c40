#!/usr/bin/env node

/**
 * Enhanced React Codebase Structure Analyzer
 *
 * Analyzes a React codebase to understand its structure, patterns, and important elements
 * Generates architecture diagrams, identifies refactoring opportunities, and provides actionable insights
 *
 * Usage:
 * node codebase-analyzer.js [directory]
 *
 * If no directory specified, analyzes current directory
 */

import fs from 'fs';
import path from 'path';

class EnhancedCodebaseAnalyzer {
  constructor(rootDir = '.') {
    this.rootDir = path.resolve(rootDir);
    this.stats = {
      totalFiles: 0,
      components: [],
      hooks: [],
      services: [],
      types: [],
      tests: [],
      configs: [],
      styles: [],
      directories: new Map(),
      technologies: new Set(),
      patterns: {
        stateManagement: new Set(),
        styling: new Set(),
        testing: new Set(),
        bundling: new Set(),
      },
      dependencies: new Map(), // Component dependencies
      performance: {
        largeBundles: [],
        memoryleaks: [],
        unnecessaryRerenders: [],
        heavyComponents: [],
      },
      refactoring: {
        critical: [],
        high: [],
        medium: [],
        low: [],
      },
      architecture: {
        layers: new Map(),
        dataFlow: [],
        relationships: new Map(),
      },
    };
  }

  analyze() {
    console.log(`🔍 ANALYZING REACT CODEBASE: ${this.rootDir}`);
    console.log('='.repeat(80));

    this.scanDirectory(this.rootDir);
    this.identifyTechnologies();
    this.analyzeArchitecture();
    this.identifyRefactoringOpportunities();
    this.analyzePerformance();
    this.generateComprehensiveReport();
  }

  scanDirectory(dir, depth = 0) {
    if (depth > 10) return; // Prevent infinite recursion

    let items;
    try {
      items = fs.readdirSync(dir);
    } catch (error) {
      return;
    }

    const dirName = path.basename(dir);
    const relativePath = path.relative(this.rootDir, dir);

    // Skip certain directories
    if (this.shouldSkipDirectory(dirName)) return;

    let dirStats = {
      path: relativePath || '.',
      files: 0,
      components: 0,
      hooks: 0,
      types: 0,
      tests: 0,
      subdirs: 0,
    };

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        dirStats.subdirs++;
        this.scanDirectory(fullPath, depth + 1);
      } else if (stat.isFile()) {
        dirStats.files++;
        this.stats.totalFiles++;
        this.analyzeFile(fullPath, relativePath, dirStats);
      }
    }

    this.stats.directories.set(relativePath || '.', dirStats);
  }

  shouldSkipDirectory(dirName) {
    const skipDirs = [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.next',
      'coverage',
      '.nyc_output',
      'tmp',
      '.cache',
    ];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  analyzeFile(filePath, relativeDirPath, dirStats) {
    const fileName = path.basename(filePath);
    const ext = path.extname(fileName);
    const baseName = path.basename(fileName, ext);

    // Read file content for analysis
    let content = '';
    try {
      content = fs.readFileSync(filePath, 'utf8');
    } catch {
      return; // Skip files we can't read
    }

    const fileInfo = {
      name: fileName,
      path: path.relative(this.rootDir, filePath),
      size: content.length,
      lines: content.split('\n').length,
      type: this.getFileType(fileName, content),
      exports: this.findExports(content),
      imports: this.findImports(content),
      complexity: this.calculateComplexity(content),
      responsibilities: this.analyzeResponsibilities(content),
      reusabilityScore: this.calculateReusabilityScore(content, fileName),
      performanceIssues: this.detectPerformanceIssues(content),
      couplingScore: this.calculateCouplingScore(content),
      testCoverage: this.estimateTestCoverage(filePath),
    };

    // Categorize files
    if (this.isReactComponent(fileName, content)) {
      this.stats.components.push(fileInfo);
      dirStats.components++;
      this.analyzeComponentDependencies(fileInfo);
    } else if (this.isHook(fileName, content)) {
      this.stats.hooks.push(fileInfo);
      dirStats.hooks++;
    } else if (this.isService(fileName, content)) {
      this.stats.services.push(fileInfo);
    } else if (this.isTypeFile(fileName, content)) {
      this.stats.types.push(fileInfo);
      dirStats.types++;
    } else if (this.isTestFile(fileName)) {
      this.stats.tests.push(fileInfo);
      dirStats.tests++;
    } else if (this.isConfigFile(fileName)) {
      this.stats.configs.push(fileInfo);
    } else if (this.isStyleFile(fileName)) {
      this.stats.styles.push(fileInfo);
    }

    // Detect patterns in content
    this.detectPatterns(content);
  }

  getFileType(fileName, content) {
    const ext = path.extname(fileName);
    if (['.tsx', '.jsx'].includes(ext)) return 'Component';
    if (['.ts', '.js'].includes(ext)) {
      if (content.includes('export default') || content.includes('export const')) {
        return 'Module';
      }
      return 'Script';
    }
    if (['.css', '.scss', '.sass', '.less'].includes(ext)) return 'Style';
    if (fileName.includes('test') || fileName.includes('spec')) return 'Test';
    if (fileName.includes('config')) return 'Config';
    return 'Other';
  }

  isReactComponent(fileName, content) {
    return (
      ((fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) &&
        (content.includes('export default') || content.includes('export const')) &&
        (content.includes('return (') || content.includes('return<') || content.includes('jsx'))) ||
      content.includes('React.') ||
      content.includes('useState') ||
      content.includes('useEffect') ||
      content.includes('</') // JSX closing tag
    );
  }

  isHook(fileName, content) {
    return (
      fileName.startsWith('use') ||
      content.includes('export const use') ||
      content.includes('export default use') ||
      (content.includes('useState') && content.includes('function use'))
    );
  }

  isService(fileName, content) {
    return (
      fileName.toLowerCase().includes('service') ||
      fileName.toLowerCase().includes('api') ||
      fileName.toLowerCase().includes('storage') ||
      (content.includes('class') && content.includes('service')) ||
      (content.includes('export') && fileName.toLowerCase().includes('client'))
    );
  }

  isTypeFile(fileName, content) {
    return (
      fileName.includes('types') ||
      fileName.includes('interfaces') ||
      fileName.endsWith('.d.ts') ||
      (content.includes('interface ') && content.includes('export')) ||
      (content.includes('type ') && content.includes('='))
    );
  }

  isTestFile(fileName) {
    return (
      fileName.includes('.test.') || fileName.includes('.spec.') || fileName.includes('__tests__')
    );
  }

  isConfigFile(fileName) {
    const configFiles = [
      'package.json',
      'tsconfig.json',
      'vite.config',
      'webpack.config',
      '.eslintrc',
      'prettier.config',
      'tailwind.config',
      'next.config',
    ];
    return configFiles.some((config) => fileName.includes(config));
  }

  isStyleFile(fileName) {
    return /\.(css|scss|sass|less|styl)$/.test(fileName);
  }

  findExports(content) {
    const exports = [];
    const patterns = [
      /export\s+default\s+(\w+)/g,
      /export\s+const\s+(\w+)/g,
      /export\s+function\s+(\w+)/g,
      /export\s+class\s+(\w+)/g,
      /export\s*{\s*([^}]+)\s*}/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        exports.push(match[1]);
      }
    });

    return exports;
  }

  findImports(content) {
    const imports = [];
    const importPattern = /import.*?from\s+['"]([^'"]+)['"]/g;
    let match;

    while ((match = importPattern.exec(content)) !== null) {
      imports.push(match[1]);
    }

    return imports;
  }

  calculateComplexity(content) {
    const complexityIndicators = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g,
      /\?.*:/g, // ternary
      /useEffect/g,
      /useState/g,
    ];

    let complexity = 1; // Base complexity

    complexityIndicators.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  analyzeResponsibilities(content) {
    const responsibilities = [];

    if (content.includes('useState') || content.includes('useReducer')) {
      responsibilities.push('State Management');
    }
    if (content.includes('useEffect') || content.includes('useLayoutEffect')) {
      responsibilities.push('Side Effects');
    }
    if (content.includes('fetch') || content.includes('axios') || content.includes('api')) {
      responsibilities.push('Data Fetching');
    }
    if (
      content.includes('onClick') ||
      content.includes('onSubmit') ||
      content.includes('onChange')
    ) {
      responsibilities.push('Event Handling');
    }
    if (content.includes('return (') && content.includes('</')) {
      responsibilities.push('UI Rendering');
    }
    if (content.includes('validate') || content.includes('validation')) {
      responsibilities.push('Validation');
    }
    if (content.includes('localStorage') || content.includes('sessionStorage')) {
      responsibilities.push('Data Persistence');
    }

    return responsibilities;
  }

  calculateReusabilityScore(content, fileName) {
    let score = 5; // Base score

    // Positive factors
    if (content.includes('interface ') || content.includes('type ')) score += 2;
    if (content.includes('PropTypes') || fileName.endsWith('.tsx')) score += 1;
    if (content.includes('defaultProps')) score += 1;
    if (!content.includes('hardcoded') && !content.includes('TODO')) score += 1;
    if (content.includes('export')) score += 1;

    // Negative factors
    if (content.includes('window.') || content.includes('document.')) score -= 2;
    if (content.includes('process.env')) score -= 1;
    if (content.includes('localStorage') || content.includes('sessionStorage')) score -= 1;
    if (content.split('\n').length > 200) score -= 2;

    return Math.max(1, Math.min(10, score));
  }

  detectPerformanceIssues(content) {
    const issues = [];

    // Potential performance problems
    if (content.includes('useEffect') && !content.includes('[]')) {
      issues.push('Missing dependency array in useEffect');
    }
    if (content.includes('map(') && content.includes('onClick')) {
      issues.push('Inline function in map - potential re-renders');
    }
    if (content.includes('new Date()') || content.includes('Math.random()')) {
      issues.push('Non-deterministic values in render');
    }
    if (content.includes('console.log')) {
      issues.push('Console statements in production code');
    }
    if ((content.match(/useEffect/g) || []).length > 5) {
      issues.push('Too many useEffect hooks');
    }
    if (content.includes('dangerouslySetInnerHTML')) {
      issues.push('Potential XSS vulnerability');
    }

    return issues;
  }

  calculateCouplingScore(content) {
    const imports = this.findImports(content);
    const relativeImports = imports.filter((imp) => imp.startsWith('.'));
    const externalImports = imports.filter((imp) => !imp.startsWith('.'));

    // Higher coupling score means more tightly coupled
    let score = relativeImports.length * 2 + externalImports.length;

    if (content.includes('../../')) score += 2; // Deep relative imports
    if (content.includes('../../../')) score += 3; // Very deep imports

    return Math.min(20, score);
  }

  estimateTestCoverage(filePath) {
    const fileName = path.basename(filePath, path.extname(filePath));
    const dir = path.dirname(filePath);

    // Look for corresponding test files
    const possibleTestPaths = [
      path.join(dir, `${fileName}.test.js`),
      path.join(dir, `${fileName}.test.tsx`),
      path.join(dir, `${fileName}.spec.js`),
      path.join(dir, `${fileName}.spec.tsx`),
      path.join(dir, '__tests__', `${fileName}.test.js`),
      path.join(dir, '__tests__', `${fileName}.test.tsx`),
    ];

    return possibleTestPaths.some((testPath) => fs.existsSync(testPath))
      ? 'Covered'
      : 'Not Covered';
  }

  analyzeComponentDependencies(fileInfo) {
    const dependencies = [];

    fileInfo.imports.forEach((imp) => {
      if (imp.startsWith('.')) {
        dependencies.push({
          from: fileInfo.name,
          to: imp,
          type: 'local',
        });
      } else if (!imp.includes('node_modules')) {
        dependencies.push({
          from: fileInfo.name,
          to: imp,
          type: 'external',
        });
      }
    });

    if (!this.stats.dependencies.has(fileInfo.name)) {
      this.stats.dependencies.set(fileInfo.name, []);
    }
    this.stats.dependencies.get(fileInfo.name).push(...dependencies);
  }

  detectPatterns(content) {
    // State management patterns
    if (content.includes('useState') || content.includes('useReducer')) {
      this.stats.patterns.stateManagement.add('React Hooks');
    }
    if (content.includes('redux') || content.includes('useDispatch')) {
      this.stats.patterns.stateManagement.add('Redux');
    }
    if (content.includes('zustand')) {
      this.stats.patterns.stateManagement.add('Zustand');
    }
    if (content.includes('recoil')) {
      this.stats.patterns.stateManagement.add('Recoil');
    }

    // Styling patterns
    if (content.includes('styled-components') || content.includes('styled.')) {
      this.stats.patterns.styling.add('Styled Components');
    }
    if ((content.includes('className=') && content.includes('bg-')) || content.includes('text-')) {
      this.stats.patterns.styling.add('Tailwind CSS');
    }
    if (content.includes('@emotion')) {
      this.stats.patterns.styling.add('Emotion');
    }
    if (content.includes('makeStyles') || content.includes('@mui')) {
      this.stats.patterns.styling.add('Material-UI');
    }

    // Testing patterns
    if (content.includes('jest') || content.includes('describe(') || content.includes('it(')) {
      this.stats.patterns.testing.add('Jest');
    }
    if (content.includes('@testing-library')) {
      this.stats.patterns.testing.add('Testing Library');
    }
    if (content.includes('enzyme')) {
      this.stats.patterns.testing.add('Enzyme');
    }
  }

  identifyTechnologies() {
    // Check package.json for technologies
    const packageJsonPath = path.join(this.rootDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

        Object.keys(deps).forEach((dep) => {
          if (dep.includes('react')) this.stats.technologies.add('React');
          if (dep.includes('typescript')) this.stats.technologies.add('TypeScript');
          if (dep.includes('vite')) this.stats.technologies.add('Vite');
          if (dep.includes('webpack')) this.stats.technologies.add('Webpack');
          if (dep.includes('next')) this.stats.technologies.add('Next.js');
          if (dep.includes('tailwind')) this.stats.technologies.add('Tailwind CSS');
          if (dep.includes('eslint')) this.stats.technologies.add('ESLint');
          if (dep.includes('prettier')) this.stats.technologies.add('Prettier');
        });
      } catch (error) {
        console.warn('Could not parse package.json');
      }
    }
  }

  analyzeArchitecture() {
    // Identify architectural layers
    this.stats.components.forEach((comp) => {
      const pathParts = comp.path.split('/');
      const layer = this.identifyLayer(pathParts, comp);

      if (!this.stats.architecture.layers.has(layer)) {
        this.stats.architecture.layers.set(layer, []);
      }
      this.stats.architecture.layers.get(layer).push(comp);
    });

    // Analyze data flow
    this.analyzeDataFlow();
  }

  identifyLayer(pathParts, comp) {
    const path = pathParts.join('/').toLowerCase();

    if (path.includes('component') || path.includes('ui')) return 'UI Layer';
    if (path.includes('page') || path.includes('screen')) return 'Page Layer';
    if (path.includes('service') || path.includes('api')) return 'Service Layer';
    if (path.includes('util') || path.includes('helper')) return 'Utility Layer';
    if (path.includes('hook')) return 'Hook Layer';
    if (path.includes('context') || path.includes('provider')) return 'Context Layer';

    return 'Business Logic Layer';
  }

  analyzeDataFlow() {
    // Analyze prop drilling and state flow
    this.stats.components.forEach((comp) => {
      const propsCount = (comp.content || '').match(/props\./g)?.length || 0;
      const stateCount = (comp.content || '').match(/useState|useReducer/g)?.length || 0;

      if (propsCount > 5) {
        this.stats.architecture.dataFlow.push({
          component: comp.name,
          issue: 'Potential prop drilling',
          severity: 'Medium',
        });
      }

      if (stateCount > 3) {
        this.stats.architecture.dataFlow.push({
          component: comp.name,
          issue: 'Complex state management',
          severity: 'High',
        });
      }
    });
  }

  identifyRefactoringOpportunities() {
    // Critical refactoring opportunities
    this.stats.components.forEach((comp) => {
      if (comp.lines > 300) {
        this.stats.refactoring.critical.push({
          file: comp.name,
          issue: 'Extremely large component',
          lines: comp.lines,
          effort: '3-5 days',
          impact: 'High',
          suggestion: 'Split into multiple components or extract custom hooks',
        });
      }

      if (comp.complexity > 30) {
        this.stats.refactoring.critical.push({
          file: comp.name,
          issue: 'Very high complexity',
          complexity: comp.complexity,
          effort: '2-3 days',
          impact: 'High',
          suggestion: 'Extract complex logic into custom hooks or utility functions',
        });
      }

      if (comp.responsibilities.length > 4) {
        this.stats.refactoring.high.push({
          file: comp.name,
          issue: 'Multiple responsibilities',
          responsibilities: comp.responsibilities,
          effort: '1-2 days',
          impact: 'Medium',
          suggestion: 'Apply Single Responsibility Principle - split concerns',
        });
      }

      if (comp.couplingScore > 10) {
        this.stats.refactoring.high.push({
          file: comp.name,
          issue: 'High coupling',
          couplingScore: comp.couplingScore,
          effort: '1-2 days',
          impact: 'Medium',
          suggestion: 'Reduce dependencies and use dependency injection',
        });
      }

      if (comp.reusabilityScore < 4) {
        this.stats.refactoring.medium.push({
          file: comp.name,
          issue: 'Low reusability',
          score: comp.reusabilityScore,
          effort: '4-8 hours',
          impact: 'Low',
          suggestion: 'Make component more generic and configurable',
        });
      }

      if (comp.testCoverage === 'Not Covered') {
        this.stats.refactoring.medium.push({
          file: comp.name,
          issue: 'No test coverage',
          effort: '2-4 hours',
          impact: 'Medium',
          suggestion: 'Add unit tests for critical functionality',
        });
      }
    });

    // Code duplication detection (simplified)
    this.detectCodeDuplication();
  }

  detectCodeDuplication() {
    const componentNames = this.stats.components.map((c) => c.name.toLowerCase());
    const duplicatePatterns = {};

    componentNames.forEach((name) => {
      const baseName = name.replace(/component|page|screen|container/g, '');
      if (baseName.length > 3) {
        if (!duplicatePatterns[baseName]) duplicatePatterns[baseName] = [];
        duplicatePatterns[baseName].push(name);
      }
    });

    Object.entries(duplicatePatterns).forEach(([pattern, names]) => {
      if (names.length > 1) {
        this.stats.refactoring.medium.push({
          issue: 'Potential code duplication',
          files: names,
          effort: '1-2 days',
          impact: 'Medium',
          suggestion: 'Extract common functionality into shared components or hooks',
        });
      }
    });
  }

  analyzePerformance() {
    // Identify performance issues
    this.stats.components.forEach((comp) => {
      if (comp.performanceIssues.length > 0) {
        this.stats.performance.unnecessaryRerenders.push({
          component: comp.name,
          issues: comp.performanceIssues,
        });
      }

      if (comp.lines > 500) {
        this.stats.performance.largeBundles.push({
          component: comp.name,
          size: comp.lines,
          recommendation: 'Consider code splitting or lazy loading',
        });
      }

      if (comp.complexity > 25) {
        this.stats.performance.heavyComponents.push({
          component: comp.name,
          complexity: comp.complexity,
          recommendation: 'Optimize rendering logic and extract heavy computations',
        });
      }
    });
  }

  generateMermaidDiagram() {
    let diagram = 'graph TD\n';

    // Add layers
    this.stats.architecture.layers.forEach((components, layer) => {
      const layerId = layer.replace(/\s+/g, '');
      diagram += `  ${layerId}[${layer}]\n`;

      components.slice(0, 3).forEach((comp) => {
        const compId = comp.name.replace(/[^a-zA-Z0-9]/g, '');
        diagram += `  ${compId}[${comp.name}]\n`;
        diagram += `  ${layerId} --> ${compId}\n`;
      });
    });

    // Add key relationships
    this.stats.dependencies.forEach((deps, component) => {
      deps.slice(0, 2).forEach((dep) => {
        if (dep.type === 'local') {
          const fromId = component.replace(/[^a-zA-Z0-9]/g, '');
          const toId = dep.to.replace(/[^a-zA-Z0-9]/g, '');
          diagram += `  ${fromId} --> ${toId}\n`;
        }
      });
    });

    return diagram;
  }

  generateComprehensiveReport() {
    console.log('\n📊 COMPREHENSIVE CODEBASE ANALYSIS REPORT');
    console.log('='.repeat(80));

    // 1. ARCHITECTURE OVERVIEW
    console.log('\n🏗️  1. ARCHITECTURE OVERVIEW');
    console.log(`Total Files: ${this.stats.totalFiles}`);
    console.log(`Technologies: ${Array.from(this.stats.technologies).join(', ')}`);

    console.log('\n📋 Mermaid Architecture Diagram:');
    console.log('```mermaid');
    console.log(this.generateMermaidDiagram());
    console.log('```');

    console.log('\n🔄 Architectural Layers:');
    this.stats.architecture.layers.forEach((components, layer) => {
      console.log(`   ${layer}: ${components.length} components`);
    });

    // 2. CRITICAL REFACTORING OPPORTUNITIES
    console.log('\n🚨 2. CRITICAL REFACTORING OPPORTUNITIES');

    if (this.stats.refactoring.critical.length > 0) {
      console.log('\n❗ CRITICAL (Immediate attention required):');
      this.stats.refactoring.critical.slice(0, 5).forEach((item, i) => {
        console.log(`   ${i + 1}. ${item.file}: ${item.issue}`);
        console.log(`      Impact: ${item.impact} | Effort: ${item.effort}`);
        console.log(`      → ${item.suggestion}`);
      });
    }

    if (this.stats.refactoring.high.length > 0) {
      console.log('\n⚠️  HIGH Priority:');
      this.stats.refactoring.high.slice(0, 5).forEach((item, i) => {
        console.log(`   ${i + 1}. ${item.file}: ${item.issue}`);
        console.log(`      Impact: ${item.impact} | Effort: ${item.effort}`);
        console.log(`      → ${item.suggestion}`);
      });
    }

    // 3. COMPONENT ANALYSIS
    console.log('\n⚛️  3. COMPONENT ANALYSIS');
    console.log(`Total Components: ${this.stats.components.length}`);

    const avgComplexity =
      this.stats.components.reduce((sum, c) => sum + c.complexity, 0) /
      this.stats.components.length;
    const avgReusability =
      this.stats.components.reduce((sum, c) => sum + c.reusabilityScore, 0) /
      this.stats.components.length;

    console.log(`Average Complexity: ${avgComplexity.toFixed(1)}`);
    console.log(`Average Reusability Score: ${avgReusability.toFixed(1)}/10`);

    const topComplexComponents = this.stats.components
      .sort((a, b) => b.complexity - a.complexity)
      .slice(0, 5);

    if (topComplexComponents.length > 0) {
      console.log('\n🧠 Most Complex Components:');
      topComplexComponents.forEach((comp) => {
        console.log(`   ${comp.name}: complexity ${comp.complexity}, ${comp.lines} lines`);
        console.log(`      Responsibilities: ${comp.responsibilities.join(', ')}`);
        console.log(`      Reusability: ${comp.reusabilityScore}/10`);
      });
    }

    // 4. STATE MANAGEMENT ASSESSMENT
    console.log('\n🔄 4. STATE MANAGEMENT ASSESSMENT');
    if (this.stats.patterns.stateManagement.size > 0) {
      console.log(`Patterns: ${Array.from(this.stats.patterns.stateManagement).join(', ')}`);
    }

    if (this.stats.architecture.dataFlow.length > 0) {
      console.log('\nData Flow Issues:');
      this.stats.architecture.dataFlow.forEach((issue) => {
        console.log(`   ${issue.component}: ${issue.issue} (${issue.severity})`);
      });
    }

    // 5. PERFORMANCE ANALYSIS
    console.log('\n⚡ 5. PERFORMANCE ANALYSIS');

    if (this.stats.performance.heavyComponents.length > 0) {
      console.log('\nHeavy Components:');
      this.stats.performance.heavyComponents.slice(0, 3).forEach((comp) => {
        console.log(`   ${comp.component}: complexity ${comp.complexity}`);
        console.log(`      → ${comp.recommendation}`);
      });
    }

    if (this.stats.performance.unnecessaryRerenders.length > 0) {
      console.log('\nPotential Re-render Issues:');
      this.stats.performance.unnecessaryRerenders.slice(0, 3).forEach((comp) => {
        console.log(`   ${comp.component}: ${comp.issues.join(', ')}`);
      });
    }

    // 6. ACTIONABLE RECOMMENDATIONS
    console.log('\n💡 6. ACTIONABLE RECOMMENDATIONS');

    console.log('\n🏃‍♂️ Quick Wins (< 1 day):');
    const quickWins = [
      ...this.stats.refactoring.low.slice(0, 3),
      ...this.stats.refactoring.medium.filter((r) => r.effort.includes('hours')).slice(0, 2),
    ];
    quickWins.forEach((item, i) => {
      console.log(`   ${i + 1}. ${item.file || 'General'}: ${item.issue}`);
      console.log(`      → ${item.suggestion}`);
    });

    console.log('\n🚀 Medium-term (1-5 days):');
    this.stats.refactoring.high.slice(0, 3).forEach((item, i) => {
      console.log(`   ${i + 1}. ${item.file}: ${item.issue}`);
      console.log(`      → ${item.suggestion}`);
    });

    console.log('\n🏗️  Major Refactors (> 1 week):');
    this.stats.refactoring.critical.slice(0, 2).forEach((item, i) => {
      console.log(`   ${i + 1}. ${item.file}: ${item.issue}`);
      console.log(`      → ${item.suggestion}`);
    });

    // Test Coverage Summary
    const testedComponents = this.stats.components.filter(
      (c) => c.testCoverage === 'Covered'
    ).length;
    const testCoveragePercentage = (
      (testedComponents / this.stats.components.length) *
      100
    ).toFixed(1);

    console.log('\n🧪 TEST COVERAGE SUMMARY');
    console.log(
      `Component Test Coverage: ${testCoveragePercentage}% (${testedComponents}/${this.stats.components.length})`
    );

    // Final Summary
    console.log('\n📋 EXECUTIVE SUMMARY');
    console.log(
      `   • ${this.stats.refactoring.critical.length} critical issues requiring immediate attention`
    );
    console.log(
      `   • ${this.stats.refactoring.high.length} high-priority refactoring opportunities`
    );
    console.log(
      `   • ${
        this.stats.components.filter((c) => c.reusabilityScore >= 7).length
      } highly reusable components`
    );
    console.log(
      `   • ${this.stats.performance.heavyComponents.length} components with performance concerns`
    );
    console.log(`   • ${testCoveragePercentage}% test coverage on components`);

    console.log('\n🎯 NEXT STEPS');
    console.log('   1. Address critical refactoring issues first');
    console.log('   2. Implement quick wins to build momentum');
    console.log('   3. Add tests for uncovered critical components');
    console.log('   4. Consider architectural improvements for scalability');
  }
}

// CLI usage
const targetDir = process.argv[2] || '.';
const analyzer = new EnhancedCodebaseAnalyzer(targetDir);
analyzer.analyze();
