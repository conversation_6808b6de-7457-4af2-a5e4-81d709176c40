import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { ProfitLossCell } from './ProfitLossCell';
// Styled wrapper for story layouts
const StoryWrapper = styled.div `
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;
const StorySection = styled.div `
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
const StoryTitle = styled.h3 `
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: 8px;
`;
const StoryRow = styled.div `
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;
const StoryLabel = styled.span `
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  min-width: 120px;
`;
const TableDemo = styled.table `
  width: 100%;
  border-collapse: collapse;
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
`;
const TableHeader = styled.th `
  background: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: 12px;
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
`;
const TableCell = styled.td `
  padding: 12px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const meta = {
    title: 'Trading/ProfitLossCell',
    component: ProfitLossCell,
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: `
# ProfitLossCell Component

A specialized component for displaying profit/loss amounts in trading applications with:

- **Color-coded visual feedback** (Green for profits, Red for losses, Gray for breakeven)
- **ADHD-friendly design** with clear visual states and consistent formatting
- **Accessibility support** with proper ARIA labels and keyboard navigation
- **Multiple size variants** for different contexts (table cells, cards, summaries)
- **Loading states** with shimmer animation
- **Responsive hover effects** for better user interaction

## Use Cases
- Trade tables showing P&L for individual trades
- Dashboard summary cards displaying total profits/losses
- Real-time position monitoring with live P&L updates
- Historical performance analysis views

## Trading Scenarios Covered
- Big wins ($247.50+)
- Small wins ($12.75)
- Breakeven trades ($0.00)
- Small losses (-$25.50)
- Big losses (-$156.25+)
- Loading/pending states
        `,
            },
        },
    },
    argTypes: {
        amount: {
            control: { type: 'number', min: -1000, max: 1000, step: 0.01 },
            description: 'The profit/loss amount to display',
        },
        currency: {
            control: { type: 'text' },
            description: 'Currency symbol (default: $)',
        },
        size: {
            control: { type: 'select' },
            options: ['small', 'medium', 'large'],
            description: 'Size variant for different contexts',
        },
        showPositiveSign: {
            control: { type: 'boolean' },
            description: 'Whether to show + sign for positive amounts',
        },
        isLoading: {
            control: { type: 'boolean' },
            description: 'Loading state with shimmer animation',
        },
    },
    tags: ['autodocs'],
};
export default meta;
// Basic Stories
export const Default = {
    args: {
        amount: 247.5,
    },
};
export const BigWin = {
    args: {
        amount: 247.5,
        showPositiveSign: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Large profitable trade - the kind that makes your day! Shows clear green styling for quick recognition.',
            },
        },
    },
};
export const SmallWin = {
    args: {
        amount: 12.75,
    },
    parameters: {
        docs: {
            description: {
                story: 'Small but consistent profits add up. Still shows positive green styling.',
            },
        },
    },
};
export const Breakeven = {
    args: {
        amount: 0.0,
    },
    parameters: {
        docs: {
            description: {
                story: 'No profit, no loss. Neutral gray styling indicates breakeven trades.',
            },
        },
    },
};
export const SmallLoss = {
    args: {
        amount: -25.5,
    },
    parameters: {
        docs: {
            description: {
                story: 'Minor loss - part of trading. Red styling provides clear visual feedback.',
            },
        },
    },
};
export const BigLoss = {
    args: {
        amount: -156.25,
    },
    parameters: {
        docs: {
            description: {
                story: 'Significant loss that needs attention. Bold red styling for immediate recognition.',
            },
        },
    },
};
export const Loading = {
    args: {
        amount: null,
        isLoading: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Loading state with shimmer animation while P&L is being calculated.',
            },
        },
    },
};
// Interactive Playground
export const Interactive = {
    args: {
        amount: 125.75,
        currency: '$',
        size: 'medium',
        showPositiveSign: false,
        isLoading: false,
    },
    parameters: {
        docs: {
            description: {
                story: 'Interactive playground - adjust the controls to see how the component responds to different values and settings.',
            },
        },
    },
};
// Size Variants
export const SizeVariants = {
    render: () => (_jsx(StoryWrapper, { children: _jsxs(StorySection, { children: [_jsx(StoryTitle, { children: "Size Variants" }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Small:" }), _jsx(ProfitLossCell, { amount: 247.5, size: "small" })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Medium:" }), _jsx(ProfitLossCell, { amount: 247.5, size: "medium" })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Large:" }), _jsx(ProfitLossCell, { amount: 247.5, size: "large" })] })] }) })),
    parameters: {
        docs: {
            description: {
                story: 'Different size variants for various contexts - small for dense tables, medium for standard use, large for emphasis.',
            },
        },
    },
};
// Trading Scenarios
export const TradingScenarios = {
    render: () => (_jsx(StoryWrapper, { children: _jsxs(StorySection, { children: [_jsx(StoryTitle, { children: "Real Trading Scenarios" }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Scalp Win:" }), _jsx(ProfitLossCell, { amount: 8.25, showPositiveSign: true })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Day Trade:" }), _jsx(ProfitLossCell, { amount: 156.75, showPositiveSign: true })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Swing Trade:" }), _jsx(ProfitLossCell, { amount: 487.5, showPositiveSign: true })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Stop Loss:" }), _jsx(ProfitLossCell, { amount: -45.25 })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Blown Account:" }), _jsx(ProfitLossCell, { amount: -1247.8 })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Commission:" }), _jsx(ProfitLossCell, { amount: -2.5 })] })] }) })),
    parameters: {
        docs: {
            description: {
                story: 'Real-world trading scenarios with typical profit/loss amounts traders encounter daily.',
            },
        },
    },
};
// Table Context Demo
export const TableContext = {
    render: () => (_jsx(StoryWrapper, { children: _jsxs(StorySection, { children: [_jsx(StoryTitle, { children: "Trading Table Context" }), _jsxs(TableDemo, { children: [_jsx("thead", { children: _jsxs("tr", { children: [_jsx(TableHeader, { children: "Date" }), _jsx(TableHeader, { children: "Symbol" }), _jsx(TableHeader, { children: "Direction" }), _jsx(TableHeader, { children: "Quantity" }), _jsx(TableHeader, { children: "P&L" })] }) }), _jsxs("tbody", { children: [_jsxs("tr", { children: [_jsx(TableCell, { children: "2024-01-15" }), _jsx(TableCell, { children: "MNQ" }), _jsx(TableCell, { children: "Long" }), _jsx(TableCell, { children: "2" }), _jsx(TableCell, { children: _jsx(ProfitLossCell, { amount: 247.5, size: "small" }) })] }), _jsxs("tr", { children: [_jsx(TableCell, { children: "2024-01-15" }), _jsx(TableCell, { children: "ES" }), _jsx(TableCell, { children: "Short" }), _jsx(TableCell, { children: "1" }), _jsx(TableCell, { children: _jsx(ProfitLossCell, { amount: -156.25, size: "small" }) })] }), _jsxs("tr", { children: [_jsx(TableCell, { children: "2024-01-15" }), _jsx(TableCell, { children: "NQ" }), _jsx(TableCell, { children: "Long" }), _jsx(TableCell, { children: "0.5" }), _jsx(TableCell, { children: _jsx(ProfitLossCell, { amount: 0.0, size: "small" }) })] }), _jsxs("tr", { children: [_jsx(TableCell, { children: "2024-01-16" }), _jsx(TableCell, { children: "MNQ" }), _jsx(TableCell, { children: "Long" }), _jsx(TableCell, { children: "1" }), _jsx(TableCell, { children: _jsx(ProfitLossCell, { amount: null, isLoading: true, size: "small" }) })] })] })] })] }) })),
    parameters: {
        docs: {
            description: {
                story: 'How the component looks in a real trading table context with mixed results and loading states.',
            },
        },
    },
};
// Edge Cases
export const EdgeCases = {
    render: () => (_jsx(StoryWrapper, { children: _jsxs(StorySection, { children: [_jsx(StoryTitle, { children: "Edge Cases & Error Handling" }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Very Large Win:" }), _jsx(ProfitLossCell, { amount: 12547.89 })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Very Large Loss:" }), _jsx(ProfitLossCell, { amount: -9876.54 })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Tiny Amount:" }), _jsx(ProfitLossCell, { amount: 0.01 })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Null Amount:" }), _jsx(ProfitLossCell, { amount: null })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Undefined:" }), _jsx(ProfitLossCell, { amount: undefined })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "Different Currency:" }), _jsx(ProfitLossCell, { amount: 247.5, currency: "\u20AC" })] }), _jsxs(StoryRow, { children: [_jsx(StoryLabel, { children: "No Currency:" }), _jsx(ProfitLossCell, { amount: 247.5, currency: "" })] })] }) })),
    parameters: {
        docs: {
            description: {
                story: 'Edge cases including very large numbers, null/undefined values, and different currencies.',
            },
        },
    },
};
//# sourceMappingURL=ProfitLossCell.stories.js.map