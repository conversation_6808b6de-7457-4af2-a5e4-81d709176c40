/**
 * ProfitLossCell Storybook Stories
 *
 * Comprehensive stories demonstrating all states and use cases for the ProfitLossCell component.
 * Perfect for ADHD trading dashboard - provides visual consistency and quick testing.
 */
import type { Meta, StoryObj } from '@storybook/react';
import { ProfitLossCell } from './ProfitLossCell';
declare const meta: Meta<typeof ProfitLossCell>;
export default meta;
type Story = StoryObj<typeof meta>;
export declare const Default: Story;
export declare const BigWin: Story;
export declare const SmallWin: Story;
export declare const Breakeven: Story;
export declare const SmallLoss: Story;
export declare const BigLoss: Story;
export declare const Loading: Story;
export declare const Interactive: Story;
export declare const SizeVariants: Story;
export declare const TradingScenarios: Story;
export declare const TableContext: Story;
export declare const EdgeCases: Story;
//# sourceMappingURL=ProfitLossCell.stories.d.ts.map