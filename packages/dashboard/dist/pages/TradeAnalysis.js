import { jsx as _jsx } from "react/jsx-runtime";
import { TradeAnalysis as TradeAnalysisFeature } from '../features/trade-analysis';
import FeatureErrorBoundary from '../components/FeatureErrorBoundary';
import { useNavigate } from 'react-router-dom';
/**
 * TradeAnalysis Page
 *
 * Displays analysis of trading performance
 */
const TradeAnalysis = () => {
    const navigate = useNavigate();
    const handleError = (error) => {
        // Log the error to the console with additional context
        console.error('Trade Analysis Feature Error:', error);
        // Here you could also log to an error tracking service
        // if (window.Sentry) {
        //   window.Sentry.captureException(error, {
        //     tags: { feature: 'trade-analysis' }
        //   });
        // }
    };
    const handleSkip = () => {
        // Navigate to the dashboard if the user skips this feature
        navigate('/');
    };
    return (_jsx(FeatureErrorBoundary, { featureName: "Trade Analysis", onError: handleError, onSkip: handleSkip, children: _jsx(TradeAnalysisFeature, {}) }));
};
export default TradeAnalysis;
//# sourceMappingURL=TradeAnalysis.js.map