import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Trade Form Page
 *
 * Page component that renders the TradeForm feature component.
 * This serves as a wrapper to maintain separation between pages and features.
 */
import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import TradeFormFeature from '../features/trade-journal/TradeForm';
/**
 * TradeForm Page
 *
 * Renders the TradeForm feature component which handles adding or editing trades.
 * The actual implementation is in the features/trade-journal directory.
 *
 * This component also handles route parameter validation to ensure proper navigation.
 */
const TradeForm = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    // Debug logging for route parameters
    console.log(`TradeForm page component mounted with ID param: "${id}"`);
    console.log(`Current URL path: ${window.location.pathname}`);
    // Validate the route parameter
    useEffect(() => {
        // For hash-based routing, we need to check the hash part of the URL
        const currentPath = window.location.hash.substring(1); // Remove the leading #
        console.log(`Current hash path: ${currentPath}`);
        // If we're in edit mode but don't have a valid ID, redirect to journal
        if (currentPath.includes('/trade/edit/') && (!id || id === 'undefined')) {
            console.error('Invalid trade ID for edit mode, redirecting to journal');
            navigate('/journal');
        }
        // Log the route parameters for debugging
        console.log(`Route parameters:`, {
            id,
            isEditMode: currentPath.includes('/trade/edit/'),
            isNewTrade: id === 'new' || currentPath.includes('/trade/new'),
            currentPath,
        });
    }, [id, navigate]);
    return _jsx(TradeFormFeature, {});
};
export default TradeForm;
//# sourceMappingURL=TradeForm.js.map