import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from "styled-components";
const SettingContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;
const SettingRow = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
const SettingLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const SettingDescription = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
/**
 * SettingItem Component
 *
 * A single setting item with label, description, and control
 */
const SettingItem = ({ label, description, control, }) => {
    return (_jsx(SettingContainer, { children: _jsxs(SettingRow, { children: [_jsxs("div", { children: [_jsx(SettingLabel, { children: label }), description && (_jsx(SettingDescription, { children: description }))] }), control] }) }));
};
export default SettingItem;
//# sourceMappingURL=SettingItem.js.map