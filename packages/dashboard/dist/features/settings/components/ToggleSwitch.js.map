{"version": 3, "file": "ToggleSwitch.js", "sourceRoot": "", "sources": ["../../../../src/features/settings/components/ToggleSwitch.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAQvC,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAA;;;;;CAK1B,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAA;;;;;;wBAMR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;;;;;CAM1D,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAA;;;;;;;sBAOV,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;gBACxC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;;;;;;;;;;;kBAWrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;;;CAGxD,CAAC;AAEF;;;;GAIG;AACH,MAAM,YAAY,GAAgC,CAAC,EACjD,OAAO,EACP,QAAQ,EACR,EAAE,GACH,EAAE,EAAE;IACH,OAAO,CACL,MAAC,MAAM,eACL,KAAC,WAAW,IACV,EAAE,EAAE,EAAE,EACN,IAAI,EAAC,UAAU,EACf,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAC3C,EACF,KAAC,YAAY,KAAG,IACT,CACV,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,YAAY,CAAC"}