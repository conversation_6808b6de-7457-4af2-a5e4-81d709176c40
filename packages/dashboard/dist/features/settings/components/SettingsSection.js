import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from "styled-components";
const Section = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h2 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
/**
 * SettingsSection Component
 *
 * A container for grouped settings with a title
 */
const SettingsSection = ({ title, children, }) => {
    return (_jsxs(Section, { children: [_jsx(SectionTitle, { children: title }), children] }));
};
export default SettingsSection;
//# sourceMappingURL=SettingsSection.js.map