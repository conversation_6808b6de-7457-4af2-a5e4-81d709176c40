import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from "styled-components";
const Toggle = styled.label `
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
`;
const ToggleInput = styled.input `
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: ${({ theme }) => theme.colors.primary};
  }

  &:checked + span:before {
    transform: translateX(26px);
  }
`;
const ToggleSlider = styled.span `
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.border};
  transition: ${({ theme }) => theme.transitions.normal};
  border-radius: 34px;

  &:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: ${({ theme }) => theme.transitions.normal};
    border-radius: 50%;
  }
`;
/**
 * ToggleSwitch Component
 *
 * A toggle switch component for boolean settings
 */
const ToggleSwitch = ({ checked, onChange, id, }) => {
    return (_jsxs(Toggle, { children: [_jsx(ToggleInput, { id: id, type: "checkbox", checked: checked, onChange: (e) => onChange(e.target.checked) }), _jsx(ToggleSlider, {})] }));
};
export default ToggleSwitch;
//# sourceMappingURL=ToggleSwitch.js.map