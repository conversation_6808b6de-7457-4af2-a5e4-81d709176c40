{"version": 3, "file": "useSettings.js", "sourceRoot": "", "sources": ["../../../../src/features/settings/hooks/useSettings.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAU1D;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,EAAE;IAC9B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;IAEvC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAW;QACjD,KAAK,EAAE,KAAK,CAAC,IAAI;QACjB,eAAe,EAAE,CAAC;QAClB,iBAAiB,EAAE,IAAI;QACvB,qBAAqB,EAAE,KAAK;QAC5B,eAAe,EAAE,IAAI;KACtB,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,KAAU,EAAE,EAAE;QAChD,WAAW,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACrB,GAAG,IAAI;YACP,CAAC,IAAI,CAAC,EAAE,KAAK;SACd,CAAC,CAAC,CAAC;QAEJ,iCAAiC;QACjC,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;IACH,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACzC,iEAAiE;QAEjE,oCAAoC;IACtC,CAAC,CAAC;IAEF,OAAO;QACL,QAAQ;QACR,YAAY;QACZ,UAAU;KACX,CAAC;AACJ,CAAC,CAAC"}