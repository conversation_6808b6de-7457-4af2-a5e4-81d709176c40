import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trade Journal Component
 *
 * This component displays a list of recorded trades with filtering options
 * and allows adding new trades.
 */
import { useState } from 'react';
import styled from 'styled-components';
import { useTradeJournal } from './hooks/useTradeJournal';
import { useTradeFilters } from './hooks/useTradeFilters';
import { TradeJournalHeader, TradeJournalContent } from './components/trade-journal';
const PageContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
/**
 * TradeJournal Component
 *
 * Main component for the Trade Journal feature that displays trade history
 * with filtering options and provides navigation to add new trades.
 */
const TradeJournal = () => {
    const { trades, isLoading, error, refreshTrades } = useTradeJournal();
    const [showFilters, setShowFilters] = useState(false);
    const { filters, handleFilterChange, resetFilters, filteredTrades, uniqueSetups, uniqueModelTypes, uniquePrimarySetupTypes, uniqueSecondarySetupTypes, uniqueLiquidityTypes, uniqueDOLTypes, } = useTradeFilters(trades);
    return (_jsxs(PageContainer, { children: [_jsx(TradeJournalHeader, { refreshTrades: refreshTrades, showFilters: showFilters, setShowFilters: setShowFilters }), _jsx(TradeJournalContent, { error: error, showFilters: showFilters, filteredTrades: filteredTrades, isLoading: isLoading, filters: filters, handleFilterChange: handleFilterChange, resetFilters: resetFilters, uniqueSetups: uniqueSetups, uniqueModelTypes: uniqueModelTypes, uniquePrimarySetupTypes: uniquePrimarySetupTypes, uniqueSecondarySetupTypes: uniqueSecondarySetupTypes, uniqueLiquidityTypes: uniqueLiquidityTypes, uniqueDOLTypes: uniqueDOLTypes })] }));
};
export default TradeJournal;
//# sourceMappingURL=TradeJournal.js.map