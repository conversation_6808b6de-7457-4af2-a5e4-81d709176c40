/**
 * Trade Form Basic Fields Component
 *
 * Displays the basic information fields for the trade form
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
interface TradeFormBasicFieldsProps {
    formValues: TradeFormValues;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    validationErrors: ValidationErrors;
    calculateProfitLoss?: () => void;
}
/**
 * Trade Form Basic Fields Component
 */
declare const TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps>;
export default TradeFormBasicFields;
//# sourceMappingURL=TradeFormBasicFields.d.ts.map