import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
import { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks';
import TimePicker from '../TimePicker';
import SelectDropdown from '../SelectDropdown';
const FormRow = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const ValidationError = styled.span `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;
/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields = ({ formValues, handleChange, validationErrors, }) => {
    return (_jsxs(_Fragment, { children: [_jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(TimePicker, { id: "rdTime", name: "rdTime", label: "Risk/Decision Time", value: formValues.rdTime || '', onChange: handleChange }), validationErrors.rdTime && _jsx(ValidationError, { children: validationErrors.rdTime })] }), _jsxs(FormGroup, { children: [_jsx(TimePicker, { id: "entryTime", name: "entryTime", label: "Entry Time", value: formValues.entryTime || '', onChange: handleChange }), validationErrors.entryTime && (_jsx(ValidationError, { children: validationErrors.entryTime }))] }), _jsxs(FormGroup, { children: [_jsx(TimePicker, { id: "exitTime", name: "exitTime", label: "Exit Time", value: formValues.exitTime || '', onChange: handleChange }), validationErrors.exitTime && (_jsx(ValidationError, { children: validationErrors.exitTime }))] })] }), _jsxs(FormRow, { children: [_jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "session", name: "session", label: "Session (Time Block)", value: formValues.session || '', onChange: handleChange, options: SESSION_OPTIONS, placeholder: "Select Session" }) }), _jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "market", name: "market", label: "Market", value: formValues.market || 'Stocks', onChange: handleChange, options: MARKET_OPTIONS }) })] })] }));
};
export default TradeFormTimingFields;
//# sourceMappingURL=TradeFormTimingFields.js.map