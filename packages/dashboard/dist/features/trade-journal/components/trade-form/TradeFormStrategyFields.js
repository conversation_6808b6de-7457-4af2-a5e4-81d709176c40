import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
import { MODEL_TYPE_OPTIONS, SETUP_OPTIONS, ENTRY_VERSION_OPTIONS, PATTERN_QUALITY_OPTIONS, } from '../../hooks';
import SelectDropdown from '../SelectDropdown';
import SetupClassificationSection from '../trade-setup-classification/SetupClassificationSection';
const FormRow = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const TextArea = styled.textarea `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 100px;

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;
`;
const Divider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
/**
 * Trade Form Strategy Fields Component
 */
const TradeFormStrategyFields = ({ formValues, handleChange, validationErrors, setFormValues, }) => {
    return (_jsxs(_Fragment, { children: [_jsx(SectionTitle, { children: "Basic Strategy" }), _jsxs(FormRow, { children: [_jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "modelType", name: "modelType", label: "Model Type", value: formValues.modelType || '', onChange: handleChange, options: MODEL_TYPE_OPTIONS, placeholder: "Select Model Type" }) }), _jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "setup", name: "setup", label: "Setup", value: formValues.setup || '', onChange: handleChange, options: SETUP_OPTIONS, placeholder: "Select Setup" }) })] }), _jsxs(FormRow, { children: [_jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "entryVersion", name: "entryVersion", label: "Entry Version", value: formValues.entryVersion || 'First Entry', onChange: handleChange, options: ENTRY_VERSION_OPTIONS }) }), _jsx(FormGroup, { children: _jsx(SelectDropdown, { id: "patternQuality", name: "patternQuality", label: "Pattern Quality (1-10)", value: formValues.patternQuality || '5', onChange: handleChange, options: PATTERN_QUALITY_OPTIONS }) })] }), _jsx(Divider, {}), _jsx(SectionTitle, { children: "Setup Classification" }), _jsx(SetupClassificationSection, { formValues: formValues, onChange: (field, value) => {
                    setFormValues((prev) => ({
                        ...prev,
                        [field]: value,
                    }));
                }, validationErrors: validationErrors }), _jsx(Divider, {}), _jsx(SectionTitle, { children: "Notes" }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "notes", children: "Trade Notes" }), _jsx(TextArea, { id: "notes", name: "notes", value: formValues.notes, onChange: handleChange })] })] }));
};
export default TradeFormStrategyFields;
//# sourceMappingURL=TradeFormStrategyFields.js.map