/**
 * Trade Form Timing Fields Component
 *
 * Displays the timing fields for the trade form
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
interface TradeFormTimingFieldsProps {
    formValues: TradeFormValues;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    validationErrors: ValidationErrors;
}
/**
 * Trade Form Timing Fields Component
 */
declare const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps>;
export default TradeFormTimingFields;
//# sourceMappingURL=TradeFormTimingFields.d.ts.map