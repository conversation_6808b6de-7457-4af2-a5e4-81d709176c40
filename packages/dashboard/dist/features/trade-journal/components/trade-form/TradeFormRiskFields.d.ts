/**
 * Trade Form Risk Fields Component
 *
 * Displays the risk management fields for the trade form
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
interface TradeFormRiskFieldsProps {
    formValues: TradeFormValues;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    validationErrors: ValidationErrors;
}
/**
 * Trade Form Risk Fields Component
 */
declare const TradeFormRiskFields: React.FC<TradeFormRiskFieldsProps>;
export default TradeFormRiskFields;
//# sourceMappingURL=TradeFormRiskFields.d.ts.map