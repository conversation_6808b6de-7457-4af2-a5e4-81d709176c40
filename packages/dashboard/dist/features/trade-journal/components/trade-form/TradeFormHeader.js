import { jsx as _jsx } from "react/jsx-runtime";
import styled from 'styled-components';
const PageHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
/**
 * Trade Form Header Component
 */
const TradeFormHeader = ({ isNewTrade, formValues }) => {
    return (_jsx(PageHeader, { children: _jsx(Title, { children: isNewTrade ? 'Add New Trade' : `Edit Trade: ${formValues.symbol} (${formValues.date})` }) }));
};
export default TradeFormHeader;
//# sourceMappingURL=TradeFormHeader.js.map