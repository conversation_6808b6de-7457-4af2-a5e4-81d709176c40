import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const ErrorMessage = styled.div `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight || 'rgba(244, 67, 54, 0.1)'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;
const SuccessMessage = styled.div `
  color: ${({ theme }) => theme.colors.success};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;
const TabInfo = styled.div `
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
`;
/**
 * Trade Form Messages Component
 */
const TradeFormMessages = ({ error, success, showTabInfo = true, }) => {
    return (_jsxs(_Fragment, { children: [error && _jsx(ErrorMessage, { children: error }), success && _jsx(SuccessMessage, { children: success }), showTabInfo && _jsx(TabInfo, { children: "* Required tab for saving trade" })] }));
};
export default TradeFormMessages;
//# sourceMappingURL=TradeFormMessages.js.map