import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
const FormRow = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const Input = styled.input `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const Select = styled.select `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const ValidationError = styled.span `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;
/**
 * Trade Form Basic Fields Component
 */
const TradeFormBasicFields = ({ formValues, handleChange, validationErrors, calculateProfitLoss, }) => {
    // Handle changes that should trigger profit calculation
    const handlePriceChange = (e) => {
        handleChange(e);
        if (calculateProfitLoss) {
            // Use setTimeout to ensure the form values are updated before calculation
            setTimeout(calculateProfitLoss, 0);
        }
    };
    return (_jsxs(_Fragment, { children: [_jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "date", children: "Date" }), _jsx(Input, { id: "date", name: "date", type: "date", value: formValues.date, onChange: handleChange, required: true }), validationErrors.date && _jsx(ValidationError, { children: validationErrors.date })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "symbol", children: "Symbol" }), _jsx(Input, { id: "symbol", name: "symbol", type: "text", value: formValues.symbol, onChange: handleChange, required: true }), validationErrors.symbol && _jsx(ValidationError, { children: validationErrors.symbol })] })] }), _jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "direction", children: "Direction" }), _jsxs(Select, { id: "direction", name: "direction", value: formValues.direction, onChange: handleChange, required: true, children: [_jsx("option", { value: "long", children: "Long" }), _jsx("option", { value: "short", children: "Short" })] })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "result", children: "Result" }), _jsxs(Select, { id: "result", name: "result", value: formValues.result, onChange: handleChange, required: true, children: [_jsx("option", { value: "win", children: "Win" }), _jsx("option", { value: "loss", children: "Loss" }), _jsx("option", { value: "breakeven", children: "Breakeven" })] })] })] }), _jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "entryPrice", children: "Entry Price" }), _jsx(Input, { id: "entryPrice", name: "entryPrice", type: "number", step: "0.01", value: formValues.entryPrice, onChange: handlePriceChange, required: true }), validationErrors.entryPrice && (_jsx(ValidationError, { children: validationErrors.entryPrice }))] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "exitPrice", children: "Exit Price" }), _jsx(Input, { id: "exitPrice", name: "exitPrice", type: "number", step: "0.01", value: formValues.exitPrice, onChange: handlePriceChange, required: true }), validationErrors.exitPrice && (_jsx(ValidationError, { children: validationErrors.exitPrice }))] })] }), _jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "quantity", children: "Quantity" }), _jsx(Input, { id: "quantity", name: "quantity", type: "number", value: formValues.quantity, onChange: handlePriceChange, required: true }), validationErrors.quantity && (_jsx(ValidationError, { children: validationErrors.quantity }))] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "profit", children: "Profit/Loss ($)" }), _jsx(Input, { id: "profit", name: "profit", type: "number", step: "0.01", value: formValues.profit, onChange: handleChange, required: true })] })] })] }));
};
export default TradeFormBasicFields;
//# sourceMappingURL=TradeFormBasicFields.js.map