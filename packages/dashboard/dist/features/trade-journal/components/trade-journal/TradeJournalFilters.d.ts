/**
 * Trade Journal Filters Component
 *
 * Displays the filters for the trade journal
 */
import React from 'react';
import { FilterState } from '../../types';
interface TradeJournalFiltersProps {
    filters: FilterState;
    handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    resetFilters: () => void;
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
}
/**
 * Trade Journal Filters Component
 */
declare const TradeJournalFilters: React.FC<TradeJournalFiltersProps>;
export default TradeJournalFilters;
//# sourceMappingURL=TradeJournalFilters.d.ts.map