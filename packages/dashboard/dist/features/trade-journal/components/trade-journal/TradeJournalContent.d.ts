/**
 * Trade Journal Content Component
 *
 * Displays the content section of the trade journal
 */
import React from 'react';
import { Trade } from '@adhd-trading-dashboard/shared';
interface TradeJournalContentProps {
    error: string | null;
    showFilters: boolean;
    filteredTrades: Trade[];
    isLoading: boolean;
    filters: any;
    handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    resetFilters: () => void;
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
}
/**
 * Trade Journal Content Component
 */
declare const TradeJournalContent: React.FC<TradeJournalContentProps>;
export default TradeJournalContent;
//# sourceMappingURL=TradeJournalContent.d.ts.map