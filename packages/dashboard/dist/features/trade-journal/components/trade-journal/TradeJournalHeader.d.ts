/**
 * Trade Journal Header Component
 *
 * Displays the header for the trade journal with title and actions
 */
import React from 'react';
interface TradeJournalHeaderProps {
    refreshTrades?: () => void;
    showFilters: boolean;
    setShowFilters: (show: boolean) => void;
}
/**
 * Trade Journal Header Component
 */
declare const TradeJournalHeader: React.FC<TradeJournalHeaderProps>;
export default TradeJournalHeader;
//# sourceMappingURL=TradeJournalHeader.d.ts.map