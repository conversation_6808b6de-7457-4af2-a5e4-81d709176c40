import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Link } from 'react-router-dom';
import styled from 'styled-components';
const PageHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const HeaderActions = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
`;
const ActionButton = styled.button `
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const RefreshButton = styled(ActionButton) `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const AddTradeButton = styled(Link) `
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 500;
  text-decoration: none;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;
/**
 * Trade Journal Header Component
 */
const TradeJournalHeader = ({ refreshTrades, showFilters, setShowFilters, }) => {
    return (_jsxs(PageHeader, { children: [_jsx(Title, { children: "Trade Journal" }), _jsxs(HeaderActions, { children: [_jsx(RefreshButton, { onClick: () => refreshTrades && refreshTrades(), children: "\u21BB Refresh" }), _jsx(ActionButton, { onClick: () => setShowFilters(!showFilters), children: showFilters ? 'Hide Filters' : 'Show Filters' }), _jsx(AddTradeButton, { to: "/trade/new", children: "+ Add Trade" })] })] }));
};
export default TradeJournalHeader;
//# sourceMappingURL=TradeJournalHeader.js.map