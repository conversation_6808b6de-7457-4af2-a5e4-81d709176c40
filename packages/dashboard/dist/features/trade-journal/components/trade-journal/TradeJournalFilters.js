import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const FilterSection = styled.div `
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const FilterGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const FilterLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const FilterSelect = styled.select `
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-width: 150px;
`;
const FilterInput = styled.input `
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const FilterButton = styled.button `
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  align-self: flex-end;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
/**
 * Trade Journal Filters Component
 */
const TradeJournalFilters = ({ filters, handleFilterChange, resetFilters, uniqueSetups, uniqueModelTypes, uniquePrimarySetupTypes, uniqueSecondarySetupTypes, uniqueLiquidityTypes, uniqueDOLTypes, }) => {
    return (_jsxs(FilterSection, { children: [_jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "symbol", children: "Symbol" }), _jsx(FilterInput, { id: "symbol", name: "symbol", value: filters.symbol, onChange: handleFilterChange, placeholder: "AAPL, MSFT, etc." })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "direction", children: "Direction" }), _jsxs(FilterSelect, { id: "direction", name: "direction", value: filters.direction, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), _jsx("option", { value: "Long", children: "Long" }), _jsx("option", { value: "Short", children: "Short" })] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "setup", children: "Setup" }), _jsxs(FilterSelect, { id: "setup", name: "setup", value: filters.setup, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniqueSetups.map((setup) => (_jsx("option", { value: setup, children: setup }, setup)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "modelType", children: "Model Type" }), _jsxs(FilterSelect, { id: "modelType", name: "modelType", value: filters.modelType, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniqueModelTypes.map((modelType) => (_jsx("option", { value: modelType, children: modelType }, modelType)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "result", children: "Result" }), _jsxs(FilterSelect, { id: "result", name: "result", value: filters.result, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), _jsx("option", { value: "win", children: "Wins" }), _jsx("option", { value: "loss", children: "Losses" })] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "dateFrom", children: "From Date" }), _jsx(FilterInput, { id: "dateFrom", name: "dateFrom", type: "date", value: filters.dateFrom, onChange: handleFilterChange })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "dateTo", children: "To Date" }), _jsx(FilterInput, { id: "dateTo", name: "dateTo", type: "date", value: filters.dateTo, onChange: handleFilterChange })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "primarySetupType", children: "Primary Setup" }), _jsxs(FilterSelect, { id: "primarySetupType", name: "primarySetupType", value: filters.primarySetupType, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniquePrimarySetupTypes.map((setupType) => (_jsx("option", { value: setupType, children: setupType }, setupType)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "secondarySetupType", children: "Secondary Setup" }), _jsxs(FilterSelect, { id: "secondarySetupType", name: "secondarySetupType", value: filters.secondarySetupType, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniqueSecondarySetupTypes.map((setupType) => (_jsx("option", { value: setupType, children: setupType }, setupType)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "liquidityTaken", children: "Liquidity Taken" }), _jsxs(FilterSelect, { id: "liquidityTaken", name: "liquidityTaken", value: filters.liquidityTaken, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniqueLiquidityTypes.map((liquidityType) => (_jsx("option", { value: liquidityType, children: liquidityType }, liquidityType)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "patternQualityMin", children: "Pattern Quality Min" }), _jsxs(FilterSelect, { id: "patternQualityMin", name: "patternQualityMin", value: filters.patternQualityMin, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "Any" }), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (_jsx("option", { value: rating, children: rating }, `min-${rating}`)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "patternQualityMax", children: "Pattern Quality Max" }), _jsxs(FilterSelect, { id: "patternQualityMax", name: "patternQualityMax", value: filters.patternQualityMax, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "Any" }), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (_jsx("option", { value: rating, children: rating }, `max-${rating}`)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "dolType", children: "DOL Type" }), _jsxs(FilterSelect, { id: "dolType", name: "dolType", value: filters.dolType, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "All" }), uniqueDOLTypes.map((dolType) => (_jsx("option", { value: dolType, children: dolType }, dolType)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "dolEffectivenessMin", children: "DOL Effectiveness Min" }), _jsxs(FilterSelect, { id: "dolEffectivenessMin", name: "dolEffectivenessMin", value: filters.dolEffectivenessMin, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "Any" }), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (_jsx("option", { value: rating, children: rating }, `dol-min-${rating}`)))] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { htmlFor: "dolEffectivenessMax", children: "DOL Effectiveness Max" }), _jsxs(FilterSelect, { id: "dolEffectivenessMax", name: "dolEffectivenessMax", value: filters.dolEffectivenessMax, onChange: handleFilterChange, children: [_jsx("option", { value: "", children: "Any" }), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (_jsx("option", { value: rating, children: rating }, `dol-max-${rating}`)))] })] }), _jsx(FilterButton, { onClick: resetFilters, children: "Reset Filters" })] }));
};
export default TradeJournalFilters;
//# sourceMappingURL=TradeJournalFilters.js.map