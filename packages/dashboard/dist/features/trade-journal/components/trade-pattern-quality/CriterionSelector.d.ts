/**
 * Criterion Selector Component
 *
 * Component for selecting a score for a specific pattern quality criterion
 */
import React from 'react';
import { ScoreRange } from '../../types';
import { PATTERN_QUALITY_CRITERIA } from '../../constants/patternQuality';
interface CriterionSelectorProps {
    criterion: keyof typeof PATTERN_QUALITY_CRITERIA;
    value: ScoreRange | '';
    onChange: (field: string, value: string) => void;
    fieldName: string;
}
declare const CriterionSelector: React.FC<CriterionSelectorProps>;
export default CriterionSelector;
//# sourceMappingURL=CriterionSelector.d.ts.map