import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Pattern Quality Assessment Component
 *
 * Main component for the pattern quality assessment section
 */
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import CriterionSelector from './CriterionSelector';
import { PATTERN_QUALITY_CRITERIA, calculateTotalScore, convertScoreToRating, getRatingDescription, getRatingColor, } from '../../constants/patternQuality';
const AssessmentContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
const Introduction = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const IntroTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;
const IntroText = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;
const Divider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
const ScoreSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const ScoreTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const ScoreDetails = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xl};
`;
const ScoreValue = styled.div `
  font-size: 3rem;
  font-weight: 700;
  color: ${({ color }) => color};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid ${({ color }) => color};
`;
const ScoreInfo = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const ScoreDescription = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const ScoreBreakdown = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const NotesSection = styled.div `
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const NotesLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const NotesTextarea = styled.textarea `
  width: 100%;
  min-height: 100px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.background};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
const PatternQualityAssessment = ({ formValues, onChange, }) => {
    const [totalScore, setTotalScore] = useState(0);
    const [rating, setRating] = useState(0);
    // Calculate score and rating when criteria change
    useEffect(() => {
        const criteria = {
            clarity: (formValues.patternQualityClarity || ''),
            confluence: (formValues.patternQualityConfluence || ''),
            context: (formValues.patternQualityContext || ''),
            risk: (formValues.patternQualityRisk || ''),
            reward: (formValues.patternQualityReward || ''),
            timeframe: (formValues.patternQualityTimeframe || ''),
            volume: (formValues.patternQualityVolume || ''),
        };
        // Only calculate if all criteria are filled
        const allCriteriaFilled = Object.values(criteria).every((value) => value !== '');
        if (allCriteriaFilled) {
            const score = calculateTotalScore(criteria);
            const calculatedRating = convertScoreToRating(score);
            setTotalScore(score);
            setRating(calculatedRating);
            // Update the patternQuality field with the calculated rating
            onChange('patternQuality', calculatedRating.toString());
        }
    }, [
        formValues.patternQualityClarity,
        formValues.patternQualityConfluence,
        formValues.patternQualityContext,
        formValues.patternQualityRisk,
        formValues.patternQualityReward,
        formValues.patternQualityTimeframe,
        formValues.patternQualityVolume,
        onChange,
    ]);
    // Handle notes change
    const handleNotesChange = (e) => {
        onChange('patternQualityNotes', e.target.value);
    };
    return (_jsxs(AssessmentContainer, { children: [_jsxs(Introduction, { children: [_jsx(IntroTitle, { children: "Pattern Quality Assessment" }), _jsx(IntroText, { children: "Evaluate the quality of your trade setup by rating each criterion below. This assessment will help you objectively analyze your trade patterns and improve your decision-making process." })] }), _jsx(Divider, {}), _jsx(CriterionSelector, { criterion: "clarity", value: formValues.patternQualityClarity || '', onChange: onChange, fieldName: "patternQualityClarity" }), _jsx(CriterionSelector, { criterion: "confluence", value: formValues.patternQualityConfluence || '', onChange: onChange, fieldName: "patternQualityConfluence" }), _jsx(CriterionSelector, { criterion: "context", value: formValues.patternQualityContext || '', onChange: onChange, fieldName: "patternQualityContext" }), _jsx(CriterionSelector, { criterion: "risk", value: formValues.patternQualityRisk || '', onChange: onChange, fieldName: "patternQualityRisk" }), _jsx(CriterionSelector, { criterion: "reward", value: formValues.patternQualityReward || '', onChange: onChange, fieldName: "patternQualityReward" }), _jsx(CriterionSelector, { criterion: "timeframe", value: formValues.patternQualityTimeframe || '', onChange: onChange, fieldName: "patternQualityTimeframe" }), _jsx(CriterionSelector, { criterion: "volume", value: formValues.patternQualityVolume || '', onChange: onChange, fieldName: "patternQualityVolume" }), rating > 0 && (_jsxs(ScoreSection, { children: [_jsx(ScoreTitle, { children: "Pattern Quality Score" }), _jsxs(ScoreDetails, { children: [_jsx(ScoreValue, { color: getRatingColor(rating), children: rating }), _jsxs(ScoreInfo, { children: [_jsx(ScoreDescription, { children: getRatingDescription(rating) }), _jsxs(ScoreBreakdown, { children: ["Total Score: ", totalScore, " out of ", Object.keys(PATTERN_QUALITY_CRITERIA).length * 5] })] })] })] })), _jsxs(NotesSection, { children: [_jsx(NotesLabel, { htmlFor: "patternQualityNotes", children: "Additional Notes" }), _jsx(NotesTextarea, { id: "patternQualityNotes", name: "patternQualityNotes", value: formValues.patternQualityNotes || '', onChange: handleNotesChange, placeholder: "Add any additional notes about the pattern quality..." })] })] }));
};
export default PatternQualityAssessment;
//# sourceMappingURL=PatternQualityAssessment.js.map