/**
 * Pattern Quality Assessment Component
 *
 * Main component for the pattern quality assessment section
 */
import React from 'react';
interface PatternQualityAssessmentProps {
    formValues: {
        patternQualityClarity?: string;
        patternQualityConfluence?: string;
        patternQualityContext?: string;
        patternQualityRisk?: string;
        patternQualityReward?: string;
        patternQualityTimeframe?: string;
        patternQualityVolume?: string;
        patternQualityNotes?: string;
        patternQuality?: string;
    };
    onChange: (field: string, value: string) => void;
}
declare const PatternQualityAssessment: React.FC<PatternQualityAssessmentProps>;
export default PatternQualityAssessment;
//# sourceMappingURL=PatternQualityAssessment.d.ts.map