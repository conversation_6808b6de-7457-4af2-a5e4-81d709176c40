import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trade List Component
 *
 * Displays a list of trades with expandable rows
 */
import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useTradeList } from '../hooks/useTradeList';
import { TradeListHeader, TradeListRow, TradeListExpandedRow, TradeListEmpty, TradeListLoading, } from './trade-list';
const TradeListContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const TradeDetail = styled.div `
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme, profit, loss }) => profit ? theme.colors.success : loss ? theme.colors.danger : theme.colors.textPrimary};
  font-weight: ${({ profit, loss }) => (profit || loss ? 600 : 'normal')};
`;
const Badge = styled.span `
  display: inline-block;
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${({ theme, type }) => type === 'long'
    ? theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'
    : type === 'short'
        ? theme.colors.dangerLight || 'rgba(244, 67, 54, 0.1)'
        : theme.colors.background};
  color: ${({ theme, type }) => type === 'long'
    ? theme.colors.success
    : type === 'short'
        ? theme.colors.danger
        : theme.colors.textPrimary};
`;
/**
 * Trade List Component
 */
const TradeList = ({ trades, isLoading = false, expandable = false, onEditTrade, }) => {
    const navigate = useNavigate();
    const { sortedTrades, toggleRowExpansion, isRowExpanded } = useTradeList(trades, expandable);
    // Handle edit trade
    const handleEditTrade = (tradeId) => {
        if (onEditTrade) {
            onEditTrade(tradeId);
        }
        else {
            // Default navigation to edit page
            navigate(`/trade/edit/${tradeId}`);
        }
    };
    // Define columns for the trade list
    const columns = useMemo(() => [
        {
            id: 'date',
            label: 'Date',
            accessor: (trade) => _jsx(TradeDetail, { children: trade.trade.date }),
        },
        {
            id: 'symbol',
            label: 'Symbol',
            accessor: (trade) => _jsx(TradeDetail, { children: trade.trade.market || 'MNQ' }),
        },
        {
            id: 'direction',
            label: 'Direction',
            accessor: (trade) => (_jsx(TradeDetail, { children: _jsx(Badge, { type: trade.trade.direction.toLowerCase(), children: trade.trade.direction }) })),
        },
        {
            id: 'setup',
            label: 'Setup',
            accessor: (trade) => _jsx(TradeDetail, { children: trade.setup?.primary_setup || 'N/A' }),
        },
        {
            id: 'entry',
            label: 'Entry',
            accessor: (trade) => (_jsxs(TradeDetail, { children: ["$", (trade.trade.entry_price || 0).toFixed(2)] })),
        },
        {
            id: 'exit',
            label: 'Exit',
            accessor: (trade) => _jsxs(TradeDetail, { children: ["$", (trade.trade.exit_price || 0).toFixed(2)] }),
        },
        {
            id: 'size',
            label: 'Size',
            accessor: (trade) => _jsx(TradeDetail, { children: trade.trade.no_of_contracts || 0 }),
        },
        {
            id: 'profitLoss',
            label: 'P/L',
            accessor: (trade) => (_jsxs(TradeDetail, { profit: (trade.trade.achieved_pl || 0) > 0, loss: (trade.trade.achieved_pl || 0) < 0, children: ["$", (trade.trade.achieved_pl || 0).toFixed(2)] })),
        },
        {
            id: 'actions',
            label: 'Actions',
            accessor: (trade) => (_jsx(TradeDetail, { children: _jsx("button", { onClick: () => handleEditTrade(trade.trade.id), style: {
                        padding: '4px 8px',
                        fontSize: '12px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        background: 'white',
                        cursor: 'pointer',
                    }, children: "Edit" }) })),
        },
    ], [handleEditTrade]);
    // Set grid template columns based on number of columns
    const gridTemplateColumns = `repeat(${columns.length}, 1fr)`;
    // If loading, show loading state
    if (isLoading) {
        return _jsx(TradeListLoading, { rowCount: 5 });
    }
    // If no trades, show empty state
    if (!sortedTrades || sortedTrades.length === 0) {
        return _jsx(TradeListEmpty, { filtered: trades && trades.length > 0 });
    }
    return (_jsxs(TradeListContainer, { style: { '--grid-template-columns': gridTemplateColumns }, children: [_jsx(TradeListHeader, { visibleColumns: columns }), sortedTrades.map((trade) => (_jsxs(React.Fragment, { children: [_jsx(TradeListRow, { trade: trade, visibleColumns: columns, expanded: isRowExpanded(trade.trade.id), toggleRowExpansion: toggleRowExpansion }), isRowExpanded(trade.trade.id) && _jsx(TradeListExpandedRow, { trade: trade })] }, trade.trade.id)))] }));
};
export default TradeList;
//# sourceMappingURL=TradeList.js.map