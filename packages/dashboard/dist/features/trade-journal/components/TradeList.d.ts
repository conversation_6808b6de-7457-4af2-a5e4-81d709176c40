/**
 * Trade List Component
 *
 * Displays a list of trades with expandable rows
 */
import React from 'react';
import { CompleteTradeData } from '../types';
export interface TradeColumn {
    id: string;
    label: string;
    accessor: (trade: CompleteTradeData) => React.ReactNode;
}
interface TradeListProps {
    trades: CompleteTradeData[];
    isLoading?: boolean;
    expandable?: boolean;
    onEditTrade?: (tradeId: number) => void;
}
/**
 * Trade List Component
 */
declare const TradeList: React.FC<TradeListProps>;
export default TradeList;
//# sourceMappingURL=TradeList.d.ts.map