import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { LIQUIDITY_OPTIONS } from '../../constants/setupClassification';
const SelectorContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const GuidanceNote = styled.div `
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const Select = styled.select `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const LiquiditySelector = ({ value, onChange }) => {
    // Handle liquidity change
    const handleLiquidityChange = (e) => {
        onChange('liquidityTaken', e.target.value);
    };
    return (_jsxs(SelectorContainer, { children: [_jsx(SectionTitle, { children: "Liquidity Taken" }), _jsx(GuidanceNote, { children: "If your trade took/swept a specific type of liquidity, select it below." }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "liquidityTaken", children: "Liquidity Taken/Swept:" }), _jsx(Select, { id: "liquidityTaken", name: "liquidityTaken", value: value || '', onChange: handleLiquidityChange, children: LIQUIDITY_OPTIONS.map((option) => (_jsx("option", { value: option.value, children: option.label }, option.value))) })] })] }));
};
export default LiquiditySelector;
//# sourceMappingURL=LiquiditySelector.js.map