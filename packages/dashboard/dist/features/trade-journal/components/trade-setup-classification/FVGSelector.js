import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { TIME_BASED_FVG_OPTIONS, CURRENT_SESSION_FVG_OPTIONS, PREV_DAY_FVG_OPTIONS, THREE_DAY_FVG_OPTIONS, SPECIAL_FVG_OPTIONS } from '../../constants/setupClassification';
const SelectorContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const GuidanceNote = styled.div `
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const CheckboxGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const CategoryGroup = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const CategoryTitle = styled.h4 `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.sm} 0;
  padding-bottom: ${({ theme }) => theme.spacing.xs};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const CheckboxOption = styled.div `
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const CheckboxInput = styled.input `
  margin-right: ${({ theme }) => theme.spacing.sm};
`;
const CheckboxLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const FVGSelector = ({ value, onChange }) => {
    // Handle checkbox change
    const handleCheckboxChange = (e) => {
        const fvgType = e.target.value;
        const isChecked = e.target.checked;
        let newValue;
        if (isChecked) {
            // Add to array if checked
            newValue = [...value, fvgType];
        }
        else {
            // Remove from array if unchecked
            newValue = value.filter(item => item !== fvgType);
        }
        onChange('additionalFVGs', newValue);
    };
    return (_jsxs(SelectorContainer, { children: [_jsx(SectionTitle, { children: "Additional FVGs Present" }), _jsx(GuidanceNote, { children: "Select all additional FVG types that were present in this trade setup." }), _jsxs(CheckboxGroup, { children: [_jsxs(CategoryGroup, { children: [_jsx(CategoryTitle, { children: "Time-frame FVGs:" }), TIME_BASED_FVG_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `fvg_${option.value}`, name: "additionalFVGs", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `fvg_${option.value}`, children: option.label })] }, option.value)))] }), _jsxs(CategoryGroup, { children: [_jsx(CategoryTitle, { children: "Current Session FPFVGs:" }), CURRENT_SESSION_FVG_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `fvg_${option.value}`, name: "additionalFVGs", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `fvg_${option.value}`, children: option.label })] }, option.value)))] }), _jsxs(CategoryGroup, { children: [_jsx(CategoryTitle, { children: "Previous Day FPFVGs:" }), PREV_DAY_FVG_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `fvg_${option.value}`, name: "additionalFVGs", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `fvg_${option.value}`, children: option.label })] }, option.value)))] }), _jsxs(CategoryGroup, { children: [_jsx(CategoryTitle, { children: "3-Day FPFVGs:" }), THREE_DAY_FVG_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `fvg_${option.value}`, name: "additionalFVGs", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `fvg_${option.value}`, children: option.label })] }, option.value)))] }), _jsxs(CategoryGroup, { children: [_jsx(CategoryTitle, { children: "Special FVG Types:" }), SPECIAL_FVG_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `fvg_${option.value}`, name: "additionalFVGs", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `fvg_${option.value}`, children: option.label })] }, option.value)))] })] })] }));
};
export default FVGSelector;
//# sourceMappingURL=FVGSelector.js.map