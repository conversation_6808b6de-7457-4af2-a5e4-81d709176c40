/**
 * Primary Setup Selector Component
 *
 * Component for selecting the primary setup category and type
 */
import React from 'react';
interface PrimarySetupSelectorProps {
    value: {
        category?: string;
        type?: string;
    };
    onChange: (field: string, value: string) => void;
}
declare const PrimarySetupSelector: React.FC<PrimarySetupSelectorProps>;
export default PrimarySetupSelector;
//# sourceMappingURL=PrimarySetupSelector.d.ts.map