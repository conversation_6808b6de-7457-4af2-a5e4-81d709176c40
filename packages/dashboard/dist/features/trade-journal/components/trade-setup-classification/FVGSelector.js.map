{"version": 3, "file": "FVGSelector.js", "sourceRoot": "", "sources": ["../../../../../src/features/trade-journal/components/trade-setup-classification/FVGSelector.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,EACL,sBAAsB,EACtB,2BAA2B,EAC3B,oBAAoB,EACpB,qBAAqB,EACrB,mBAAmB,EACpB,MAAM,qCAAqC,CAAC;AAE7C,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAG3B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,EAAE,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;;CAEjD,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAA;sBACT,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;2BACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;aACjD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;mBACzB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;eAEnC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;mBACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,EAAE,CAAA;eAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;YACxC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;oBACvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;6BACtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;CAC9D,CAAC;AAEF,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAA;;;aAGpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;mBACzB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;iCACtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI;;;wBAG9C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;;CAE7D,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAA;kBAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAChD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAA;eACnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;CACjD,CAAC;AAOF,MAAM,WAAW,GAA+B,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IACtE,yBAAyB;IACzB,MAAM,oBAAoB,GAAG,CAAC,CAAsC,EAAE,EAAE;QACtE,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;QAC/B,MAAM,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;QAEnC,IAAI,QAAkB,CAAC;QAEvB,IAAI,SAAS,EAAE;YACb,0BAA0B;YAC1B,QAAQ,GAAG,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,CAAC;SAChC;aAAM;YACL,iCAAiC;YACjC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;SACnD;QAED,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,iBAAiB,eAChB,KAAC,YAAY,0CAAuC,EAEpD,KAAC,YAAY,yFAEE,EAEf,MAAC,aAAa,eAEZ,MAAC,aAAa,eACZ,KAAC,aAAa,mCAAiC,EAC9C,sBAAsB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CACtC,MAAC,cAAc,eACb,KAAC,aAAa,IACZ,IAAI,EAAC,UAAU,EACf,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,EACzB,IAAI,EAAC,gBAAgB,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EACrC,QAAQ,EAAE,oBAAoB,GAC9B,EACF,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,YAC1C,MAAM,CAAC,KAAK,GACC,KAXG,MAAM,CAAC,KAAK,CAYhB,CAClB,CAAC,IACY,EAGhB,MAAC,aAAa,eACZ,KAAC,aAAa,0CAAwC,EACrD,2BAA2B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAC3C,MAAC,cAAc,eACb,KAAC,aAAa,IACZ,IAAI,EAAC,UAAU,EACf,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,EACzB,IAAI,EAAC,gBAAgB,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EACrC,QAAQ,EAAE,oBAAoB,GAC9B,EACF,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,YAC1C,MAAM,CAAC,KAAK,GACC,KAXG,MAAM,CAAC,KAAK,CAYhB,CAClB,CAAC,IACY,EAGhB,MAAC,aAAa,eACZ,KAAC,aAAa,uCAAqC,EAClD,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CACpC,MAAC,cAAc,eACb,KAAC,aAAa,IACZ,IAAI,EAAC,UAAU,EACf,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,EACzB,IAAI,EAAC,gBAAgB,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EACrC,QAAQ,EAAE,oBAAoB,GAC9B,EACF,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,YAC1C,MAAM,CAAC,KAAK,GACC,KAXG,MAAM,CAAC,KAAK,CAYhB,CAClB,CAAC,IACY,EAGhB,MAAC,aAAa,eACZ,KAAC,aAAa,gCAA8B,EAC3C,qBAAqB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CACrC,MAAC,cAAc,eACb,KAAC,aAAa,IACZ,IAAI,EAAC,UAAU,EACf,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,EACzB,IAAI,EAAC,gBAAgB,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EACrC,QAAQ,EAAE,oBAAoB,GAC9B,EACF,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,YAC1C,MAAM,CAAC,KAAK,GACC,KAXG,MAAM,CAAC,KAAK,CAYhB,CAClB,CAAC,IACY,EAGhB,MAAC,aAAa,eACZ,KAAC,aAAa,qCAAmC,EAChD,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CACnC,MAAC,cAAc,eACb,KAAC,aAAa,IACZ,IAAI,EAAC,UAAU,EACf,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,EACzB,IAAI,EAAC,gBAAgB,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EACrC,QAAQ,EAAE,oBAAoB,GAC9B,EACF,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,YAC1C,MAAM,CAAC,KAAK,GACC,KAXG,MAAM,CAAC,KAAK,CAYhB,CAClB,CAAC,IACY,IACF,IACE,CACrB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,WAAW,CAAC"}