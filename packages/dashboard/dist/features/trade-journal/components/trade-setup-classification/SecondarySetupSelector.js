import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Secondary Setup Selector Component
 *
 * Component for selecting the secondary setup category and type
 */
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { SETUP_CATEGORY_OPTIONS, getSetupOptionsByCategory } from '../../constants/setupClassification';
const SelectorContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const GuidanceNote = styled.div `
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const Select = styled.select `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const RadioGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
  padding-left: ${({ theme }) => theme.spacing.md};
  border-left: 3px solid ${({ theme }) => theme.colors.border};
`;
const RadioOption = styled.div `
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const RadioInput = styled.input `
  margin-right: ${({ theme }) => theme.spacing.sm};
`;
const RadioLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const ValidationError = styled.span `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;
const SecondarySetupSelector = ({ value, primarySetup, onChange, error }) => {
    const [setupOptions, setSetupOptions] = useState([]);
    // Update setup options when category changes
    useEffect(() => {
        if (value.category) {
            setSetupOptions(getSetupOptionsByCategory(value.category));
        }
        else {
            setSetupOptions([]);
        }
    }, [value.category]);
    // Handle category change
    const handleCategoryChange = (e) => {
        const newCategory = e.target.value;
        onChange('secondarySetupCategory', newCategory);
        // Reset setup type when category changes
        onChange('secondarySetupType', '');
    };
    // Handle setup type change
    const handleSetupTypeChange = (e) => {
        onChange('secondarySetupType', e.target.value);
    };
    return (_jsxs(SelectorContainer, { children: [_jsx(SectionTitle, { children: "Secondary Setup Classification" }), _jsx(GuidanceNote, { children: "This is the supporting element that added context or confirmation to your entry decision. It should be different from your Primary Setup Type." }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "secondarySetupCategory", children: "Select Secondary Category:" }), _jsxs(Select, { id: "secondarySetupCategory", name: "secondarySetupCategory", value: value.category || '', onChange: handleCategoryChange, children: [_jsx("option", { value: "", children: "-- Select Secondary Category --" }), SETUP_CATEGORY_OPTIONS.map((option) => (_jsx("option", { value: option.value, children: option.label }, option.value)))] })] }), value.category && setupOptions.length > 0 && (_jsx(RadioGroup, { children: setupOptions.map((option) => (_jsxs(RadioOption, { children: [_jsx(RadioInput, { type: "radio", id: `secondary_${option.value}`, name: "secondarySetupType", value: option.value, checked: value.type === option.value, onChange: handleSetupTypeChange }), _jsx(RadioLabel, { htmlFor: `secondary_${option.value}`, children: option.label })] }, option.value))) })), error && _jsx(ValidationError, { children: error })] }));
};
export default SecondarySetupSelector;
//# sourceMappingURL=SecondarySetupSelector.js.map