/**
 * DOL Target Selector Component
 *
 * Component for selecting the DOL target type and specific type
 */
import React from 'react';
interface DOLTargetSelectorProps {
    value: {
        targetType?: string;
        specificType?: string;
    };
    onChange: (field: string, value: string) => void;
}
declare const DOLTargetSelector: React.FC<DOLTargetSelectorProps>;
export default DOLTargetSelector;
//# sourceMappingURL=DOLTargetSelector.d.ts.map