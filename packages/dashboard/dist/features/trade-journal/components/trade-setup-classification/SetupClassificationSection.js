import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import PrimarySetupSelector from './PrimarySetupSelector';
import SecondarySetupSelector from './SecondarySetupSelector';
import LiquiditySelector from './LiquiditySelector';
import FVGSelector from './FVGSelector';
import DOLTargetSelector from './DOLTargetSelector';
import ParentPDArraySelector from './ParentPDArraySelector';
const SectionContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
const Divider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
const ValidationError = styled.div `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const SetupClassificationSection = ({ formValues, onChange, validationErrors, }) => {
    // Handle field change
    const handleFieldChange = (field, value) => {
        onChange(field, value);
    };
    return (_jsxs(SectionContainer, { children: [validationErrors.setupClassification && (_jsx(ValidationError, { children: validationErrors.setupClassification })), _jsx(PrimarySetupSelector, { value: {
                    category: formValues.primarySetupCategory,
                    type: formValues.primarySetupType,
                }, onChange: handleFieldChange }), _jsx(Divider, {}), _jsx(SecondarySetupSelector, { value: {
                    category: formValues.secondarySetupCategory,
                    type: formValues.secondarySetupType,
                }, primarySetup: {
                    category: formValues.primarySetupCategory,
                    type: formValues.primarySetupType,
                }, onChange: handleFieldChange, error: validationErrors.secondarySetupType }), _jsx(Divider, {}), _jsx(LiquiditySelector, { value: formValues.liquidityTaken || '', onChange: handleFieldChange }), _jsx(Divider, {}), _jsx(FVGSelector, { value: formValues.additionalFVGs || [], onChange: handleFieldChange }), _jsx(Divider, {}), _jsx(DOLTargetSelector, { value: {
                    targetType: formValues.dolTargetType,
                    specificType: formValues.specificDOLType,
                }, onChange: handleFieldChange }), _jsx(Divider, {}), _jsx(ParentPDArraySelector, { value: formValues.parentPDArray || '', onChange: handleFieldChange })] }));
};
export default SetupClassificationSection;
//# sourceMappingURL=SetupClassificationSection.js.map