/**
 * Setup Classification Section Component
 *
 * Main component for the setup classification section that combines all the individual components
 */
import React from 'react';
interface SetupClassificationSectionProps {
    formValues: {
        primarySetupCategory?: string;
        primarySetupType?: string;
        secondarySetupCategory?: string;
        secondarySetupType?: string;
        liquidityTaken?: string;
        additionalFVGs?: string[];
        dolTargetType?: string;
        specificDOLType?: string;
        parentPDArray?: string;
    };
    onChange: (field: string, value: any) => void;
    validationErrors: {
        [key: string]: string;
    };
}
declare const SetupClassificationSection: React.FC<SetupClassificationSectionProps>;
export default SetupClassificationSection;
//# sourceMappingURL=SetupClassificationSection.d.ts.map