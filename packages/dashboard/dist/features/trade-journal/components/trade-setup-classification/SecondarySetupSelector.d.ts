/**
 * Secondary Setup Selector Component
 *
 * Component for selecting the secondary setup category and type
 */
import React from 'react';
interface SecondarySetupSelectorProps {
    value: {
        category?: string;
        type?: string;
    };
    primarySetup: {
        category?: string;
        type?: string;
    };
    onChange: (field: string, value: string) => void;
    error?: string;
}
declare const SecondarySetupSelector: React.FC<SecondarySetupSelectorProps>;
export default SecondarySetupSelector;
//# sourceMappingURL=SecondarySetupSelector.d.ts.map