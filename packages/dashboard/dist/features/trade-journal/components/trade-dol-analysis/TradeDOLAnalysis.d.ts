/**
 * DOL Analysis Component
 *
 * Main component for the DOL analysis section that combines all the individual components
 */
import React from 'react';
interface DOLAnalysisProps {
    formValues: {
        dolType?: string;
        dolStrength?: string;
        dolReaction?: string;
        dolContext?: string[];
        dolPriceAction?: string;
        dolVolumeProfile?: string;
        dolTimeOfDay?: string;
        dolMarketStructure?: string;
        dolEffectiveness?: string;
        dolNotes?: string;
    };
    onChange: (field: string, value: any) => void;
    validationErrors: {
        [key: string]: string;
    };
}
declare const DOLAnalysis: React.FC<DOLAnalysisProps>;
export default DOLAnalysis;
//# sourceMappingURL=TradeDOLAnalysis.d.ts.map