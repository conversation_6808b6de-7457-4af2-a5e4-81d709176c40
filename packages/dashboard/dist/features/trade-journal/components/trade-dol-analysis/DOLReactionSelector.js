import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { DOL_REACTION_OPTIONS } from '../../constants/dolAnalysis';
const SelectorContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const Description = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;
const RadioGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;
const RadioOption = styled.div `
  display: flex;
  align-items: flex-start;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const RadioInput = styled.input `
  margin-right: ${({ theme }) => theme.spacing.sm};
  margin-top: 3px;
`;
const RadioLabelContainer = styled.div `
  display: flex;
  flex-direction: column;
`;
const RadioLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const RadioDescription = styled.span `
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: 2px;
`;
const DOLReactionSelector = ({ value, onChange }) => {
    // Handle DOL reaction change
    const handleDOLReactionChange = (e) => {
        onChange('dolReaction', e.target.value);
    };
    return (_jsxs(SelectorContainer, { children: [_jsx(SectionTitle, { children: "DOL Reaction" }), _jsx(Description, { children: "Describe how price reacted after the liquidity interaction." }), _jsx(RadioGroup, { children: DOL_REACTION_OPTIONS.map((option) => {
                    const [label, description] = option.label.split(' - ');
                    return (_jsxs(RadioOption, { children: [_jsx(RadioInput, { type: "radio", id: `dolReaction_${option.value}`, name: "dolReaction", value: option.value, checked: value === option.value, onChange: handleDOLReactionChange }), _jsxs(RadioLabelContainer, { children: [_jsx(RadioLabel, { htmlFor: `dolReaction_${option.value}`, children: label }), _jsx(RadioDescription, { children: description })] })] }, option.value));
                }) })] }));
};
export default DOLReactionSelector;
//# sourceMappingURL=DOLReactionSelector.js.map