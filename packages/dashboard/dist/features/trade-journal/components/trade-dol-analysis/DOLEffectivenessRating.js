import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { getDOLEffectivenessColor, getDOLEffectivenessDescription } from '../../constants/dolAnalysis';
const RatingContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const Description = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;
const RatingSliderContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const SliderContainer = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`;
const Slider = styled.input `
  flex: 1;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: ${({ theme }) => theme.colors.background};
  outline: none;
  border-radius: 4px;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors.primary};
    cursor: pointer;
  }
  
  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors.primary};
    cursor: pointer;
  }
`;
const RatingValue = styled.div `
  font-size: 2rem;
  font-weight: 700;
  color: ${({ color }) => color};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid ${({ color }) => color};
`;
const RatingDescription = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-top: ${({ theme }) => theme.spacing.sm};
  text-align: center;
`;
const NotesContainer = styled.div `
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const NotesLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const NotesTextarea = styled.textarea `
  width: 100%;
  min-height: 100px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.background};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
const DOLEffectivenessRating = ({ value, notes, onChange }) => {
    // Handle rating change
    const handleRatingChange = (e) => {
        onChange('dolEffectiveness', e.target.value);
    };
    // Handle notes change
    const handleNotesChange = (e) => {
        onChange('dolNotes', e.target.value);
    };
    // Get rating value as number
    const ratingValue = value ? parseInt(value) : 5;
    // Get color based on rating
    const ratingColor = getDOLEffectivenessColor(ratingValue);
    // Get description based on rating
    const ratingDescription = getDOLEffectivenessDescription(ratingValue);
    return (_jsxs(RatingContainer, { children: [_jsx(SectionTitle, { children: "DOL Effectiveness Rating" }), _jsx(Description, { children: "Rate how effectively price interacted with the liquidity level and how well this interaction aligned with your trade thesis." }), _jsxs(RatingSliderContainer, { children: [_jsxs(SliderContainer, { children: [_jsx(Slider, { type: "range", min: "1", max: "10", value: value || '5', onChange: handleRatingChange }), _jsx(RatingValue, { color: ratingColor, children: ratingValue })] }), _jsx(RatingDescription, { children: ratingDescription })] }), _jsxs(NotesContainer, { children: [_jsx(NotesLabel, { htmlFor: "dolNotes", children: "Additional Notes" }), _jsx(NotesTextarea, { id: "dolNotes", name: "dolNotes", value: notes, onChange: handleNotesChange, placeholder: "Add any additional notes about the DOL analysis..." })] })] }));
};
export default DOLEffectivenessRating;
//# sourceMappingURL=DOLEffectivenessRating.js.map