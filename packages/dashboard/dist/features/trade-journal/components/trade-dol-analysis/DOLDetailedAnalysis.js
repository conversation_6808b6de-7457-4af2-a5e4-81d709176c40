import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { DOL_PRICE_ACTION_DESCRIPTIONS, DOL_VOLUME_PROFILE_DESCRIPTIONS, DOL_TIME_OF_DAY_DESCRIPTION, DOL_MARKET_STRUCTURE_DESCRIPTION } from '../../constants/dolAnalysis';
const AnalysisContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const Description = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const TextArea = styled.textarea `
  width: 100%;
  min-height: 80px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.background};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
const DOLDetailedAnalysis = ({ formValues, onChange }) => {
    // Get the appropriate price action description based on DOL type
    const getPriceActionDescription = () => {
        if (formValues.dolType && DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType]) {
            return DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType];
        }
        return 'Describe the price action during the liquidity interaction.';
    };
    // Get the appropriate volume profile description based on DOL strength
    const getVolumeProfileDescription = () => {
        if (formValues.dolStrength && DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength]) {
            return DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength];
        }
        return 'Describe the volume profile during the liquidity interaction.';
    };
    // Handle text area change
    const handleTextAreaChange = (e) => {
        onChange(e.target.name, e.target.value);
    };
    return (_jsxs(AnalysisContainer, { children: [_jsx(SectionTitle, { children: "Detailed Analysis" }), _jsx(Description, { children: "Provide detailed information about the liquidity interaction." }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "dolPriceAction", children: "Price Action" }), _jsx(Description, { children: getPriceActionDescription() }), _jsx(TextArea, { id: "dolPriceAction", name: "dolPriceAction", value: formValues.dolPriceAction || '', onChange: handleTextAreaChange, placeholder: "Describe the price action..." })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "dolVolumeProfile", children: "Volume Profile" }), _jsx(Description, { children: getVolumeProfileDescription() }), _jsx(TextArea, { id: "dolVolumeProfile", name: "dolVolumeProfile", value: formValues.dolVolumeProfile || '', onChange: handleTextAreaChange, placeholder: "Describe the volume profile..." })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "dolTimeOfDay", children: "Time of Day Significance" }), _jsx(Description, { children: DOL_TIME_OF_DAY_DESCRIPTION }), _jsx(TextArea, { id: "dolTimeOfDay", name: "dolTimeOfDay", value: formValues.dolTimeOfDay || '', onChange: handleTextAreaChange, placeholder: "Describe the time of day significance..." })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: "dolMarketStructure", children: "Market Structure" }), _jsx(Description, { children: DOL_MARKET_STRUCTURE_DESCRIPTION }), _jsx(TextArea, { id: "dolMarketStructure", name: "dolMarketStructure", value: formValues.dolMarketStructure || '', onChange: handleTextAreaChange, placeholder: "Describe the market structure..." })] })] }));
};
export default DOLDetailedAnalysis;
//# sourceMappingURL=DOLDetailedAnalysis.js.map