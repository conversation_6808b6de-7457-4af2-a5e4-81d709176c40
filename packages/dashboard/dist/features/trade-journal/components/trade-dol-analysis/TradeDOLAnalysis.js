import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import DOLTypeSelector from './DOLTypeSelector';
import DOLStrengthSelector from './DOLStrengthSelector';
import DOLReactionSelector from './DOLReactionSelector';
import DOLContextSelector from './DOLContextSelector';
import DOLDetailedAnalysis from './DOLDetailedAnalysis';
import DOLEffectivenessRating from './DOLEffectivenessRating';
const AnalysisContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
const Introduction = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const IntroTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;
const IntroText = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;
const Divider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
const ValidationError = styled.div `
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const DOLAnalysis = ({ formValues, onChange, validationErrors, }) => {
    return (_jsxs(AnalysisContainer, { children: [_jsxs(Introduction, { children: [_jsx(IntroTitle, { children: "Draw on Liquidity (DOL) Analysis" }), _jsx(IntroText, { children: "Analyze how price interacted with liquidity levels in this trade. This analysis will help you understand market behavior around key levels and improve your ability to anticipate price movements." })] }), validationErrors.dolAnalysis && (_jsx(ValidationError, { children: validationErrors.dolAnalysis })), _jsx(Divider, {}), _jsx(DOLTypeSelector, { value: formValues.dolType || '', onChange: onChange }), _jsx(Divider, {}), _jsx(DOLStrengthSelector, { value: formValues.dolStrength || '', onChange: onChange }), _jsx(Divider, {}), _jsx(DOLReactionSelector, { value: formValues.dolReaction || '', onChange: onChange }), _jsx(Divider, {}), _jsx(DOLContextSelector, { value: formValues.dolContext || [], onChange: onChange }), _jsx(Divider, {}), _jsx(DOLDetailedAnalysis, { formValues: formValues, onChange: onChange }), _jsx(Divider, {}), _jsx(DOLEffectivenessRating, { value: formValues.dolEffectiveness || '5', notes: formValues.dolNotes || '', onChange: onChange })] }));
};
export default DOLAnalysis;
//# sourceMappingURL=TradeDOLAnalysis.js.map