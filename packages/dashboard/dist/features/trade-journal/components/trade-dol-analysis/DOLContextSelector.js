import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { DOL_CONTEXT_OPTIONS } from '../../constants/dolAnalysis';
const SelectorContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const Description = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;
const CheckboxGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;
const CheckboxOption = styled.div `
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const CheckboxInput = styled.input `
  margin-right: ${({ theme }) => theme.spacing.sm};
`;
const CheckboxLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const DOLContextSelector = ({ value, onChange }) => {
    // Handle checkbox change
    const handleCheckboxChange = (e) => {
        const contextValue = e.target.value;
        const isChecked = e.target.checked;
        let newValue;
        if (isChecked) {
            // Add to array if checked
            newValue = [...value, contextValue];
        }
        else {
            // Remove from array if unchecked
            newValue = value.filter(item => item !== contextValue);
        }
        onChange('dolContext', newValue);
    };
    return (_jsxs(SelectorContainer, { children: [_jsx(SectionTitle, { children: "DOL Context" }), _jsx(Description, { children: "Select all contextual factors that apply to this liquidity interaction." }), _jsx(CheckboxGroup, { children: DOL_CONTEXT_OPTIONS.map((option) => (_jsxs(CheckboxOption, { children: [_jsx(CheckboxInput, { type: "checkbox", id: `dolContext_${option.value}`, name: "dolContext", value: option.value, checked: value.includes(option.value), onChange: handleCheckboxChange }), _jsx(CheckboxLabel, { htmlFor: `dolContext_${option.value}`, children: option.label })] }, option.value))) })] }));
};
export default DOLContextSelector;
//# sourceMappingURL=DOLContextSelector.js.map