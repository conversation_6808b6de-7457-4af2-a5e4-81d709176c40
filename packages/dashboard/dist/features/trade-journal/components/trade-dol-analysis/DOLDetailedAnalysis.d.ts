/**
 * DOL Detailed Analysis Component
 *
 * Component for entering detailed DOL analysis information
 */
import React from 'react';
interface DOLDetailedAnalysisProps {
    formValues: {
        dolType?: string;
        dolStrength?: string;
        dolPriceAction?: string;
        dolVolumeProfile?: string;
        dolTimeOfDay?: string;
        dolMarketStructure?: string;
    };
    onChange: (field: string, value: string) => void;
}
declare const DOLDetailedAnalysis: React.FC<DOLDetailedAnalysisProps>;
export default DOLDetailedAnalysis;
//# sourceMappingURL=DOLDetailedAnalysis.d.ts.map