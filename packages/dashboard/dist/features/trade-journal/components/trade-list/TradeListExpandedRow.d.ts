/**
 * Trade List Expanded Row Component
 *
 * Displays expanded details for a trade row
 */
import React from 'react';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
interface TradeListExpandedRowProps {
    trade: CompleteTradeData;
}
/**
 * Trade List Expanded Row Component
 */
declare const TradeListExpandedRow: React.FC<TradeListExpandedRowProps>;
export default TradeListExpandedRow;
//# sourceMappingURL=TradeListExpandedRow.d.ts.map