import { jsx as _jsx } from "react/jsx-runtime";
import styled, { keyframes } from 'styled-components';
const shimmer = keyframes `
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
`;
const LoadingContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md} 0;
`;
const LoadingRow = styled.div `
  height: 60px;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.colors.background} 8%,
    ${({ theme }) => theme.colors.cardBackground} 18%,
    ${({ theme }) => theme.colors.background} 33%
  );
  background-size: 2000px 100%;
  animation: ${shimmer} 1.5s infinite linear;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;
/**
 * Trade List Loading Component
 */
const TradeListLoading = ({ rowCount = 5 }) => {
    return (_jsx(LoadingContainer, { children: Array.from({ length: rowCount }).map((_, index) => (_jsx(LoadingRow, {}, index))) }));
};
export default TradeListLoading;
//# sourceMappingURL=TradeListLoading.js.map