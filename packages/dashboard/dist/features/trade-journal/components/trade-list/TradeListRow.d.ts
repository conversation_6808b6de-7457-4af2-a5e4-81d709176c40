/**
 * Trade List Row Component
 *
 * Displays a row in the trade list
 */
import React from 'react';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { TradeColumn } from '../TradeList';
interface TradeListRowProps {
    trade: CompleteTradeData;
    visibleColumns: TradeColumn[];
    expanded?: boolean;
    toggleRowExpansion: (tradeId: number) => void;
}
/**
 * Trade List Row Component
 */
declare const TradeListRow: React.FC<TradeListRowProps>;
export default TradeListRow;
//# sourceMappingURL=TradeListRow.d.ts.map