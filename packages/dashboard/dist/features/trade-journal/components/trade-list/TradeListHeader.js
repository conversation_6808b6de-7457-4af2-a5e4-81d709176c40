import { jsx as _jsx } from "react/jsx-runtime";
import styled from 'styled-components';
const TradeHeader = styled.div `
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: transparent;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  position: sticky;
  top: 0;
  z-index: 1;
  backdrop-filter: blur(8px);
`;
const TradeDetail = styled.div `
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
`;
/**
 * Trade List Header Component
 */
const TradeListHeader = ({ visibleColumns }) => {
    return (_jsx(TradeHeader, { children: visibleColumns.map((column) => (_jsx(TradeDetail, { children: column.label }, column.id))) }));
};
export default TradeListHeader;
//# sourceMappingURL=TradeListHeader.js.map