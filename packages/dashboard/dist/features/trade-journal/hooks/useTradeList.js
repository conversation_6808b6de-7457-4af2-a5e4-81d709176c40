/**
 * useTradeList Hook
 *
 * Custom hook for managing trade list state and behavior
 */
import { useState, useMemo } from 'react';
/**
 * Hook for managing trade list state and behavior
 * @param trades The trades to display
 * @param expandable Whether the rows can be expanded
 */
export function useTradeList(trades, expandable = false) {
    // Track expanded rows
    const [expandedRows, setExpandedRows] = useState({});
    // Toggle row expansion
    const toggleRowExpansion = (tradeId) => {
        if (!expandable)
            return;
        setExpandedRows((prev) => ({
            ...prev,
            [tradeId]: !prev[tradeId],
        }));
    };
    // Check if a row is expanded
    const isRowExpanded = (tradeId) => {
        return expandable && expandedRows[tradeId];
    };
    // Sort trades by date (newest first)
    const sortedTrades = useMemo(() => {
        if (!trades)
            return [];
        return [...trades].sort((a, b) => {
            const dateA = new Date(a.trade.date).getTime();
            const dateB = new Date(b.trade.date).getTime();
            return dateB - dateA; // Newest first
        });
    }, [trades]);
    return {
        sortedTrades,
        expandedRows,
        toggleRowExpansion,
        isRowExpanded,
    };
}
//# sourceMappingURL=useTradeList.js.map