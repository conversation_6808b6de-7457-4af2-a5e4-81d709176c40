{"version": 3, "file": "useTradeCalculations.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-journal/hooks/useTradeCalculations.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAIlC;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAClC,UAA2B,EAC3B,aAAoE;IAEpE,0DAA0D;IAC1D,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;YAC9C,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAE7C,IAAI,UAAU,GAAG,CAAC,EAAE;gBAClB,MAAM,SAAS,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnD,aAAa,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACvB,GAAG,IAAI;oBACP,SAAS;iBACV,CAAC,CAAC,CAAC;aACL;SACF;IACH,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IAE9D,8BAA8B;IAC9B,SAAS,CAAC,GAAG,EAAE;QACb,mDAAmD;QACnD,wEAAwE;QACxE,iEAAiE;IACnE,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnE;;;OAGG;IACH,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,QAAQ,EAAE;YACxE,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACnD,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEjD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBAC/D,IAAI,UAAkB,CAAC;gBAEvB,IAAI,UAAU,CAAC,SAAS,KAAK,MAAM,EAAE;oBACnC,UAAU,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC;iBAClD;qBAAM;oBACL,UAAU,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC;iBAClD;gBAED,aAAa,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACvB,GAAG,IAAI;oBACP,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC7B,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW;iBACvE,CAAC,CAAC,CAAC;aACL;SACF;IACH,CAAC,CAAC;IAEF,OAAO;QACL,mBAAmB;KACpB,CAAC;AACJ,CAAC"}