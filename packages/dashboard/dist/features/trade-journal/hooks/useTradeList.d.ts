/**
 * useTradeList Hook
 *
 * Custom hook for managing trade list state and behavior
 */
import { CompleteTradeData } from '../types';
/**
 * Hook for managing trade list state and behavior
 * @param trades The trades to display
 * @param expandable Whether the rows can be expanded
 */
export declare function useTradeList(trades: CompleteTradeData[], expandable?: boolean): {
    sortedTrades: CompleteTradeData[];
    expandedRows: Record<number, boolean>;
    toggleRowExpansion: (tradeId: number) => void;
    isRowExpanded: (tradeId: number) => boolean;
};
export type TradeListHook = ReturnType<typeof useTradeList>;
//# sourceMappingURL=useTradeList.d.ts.map