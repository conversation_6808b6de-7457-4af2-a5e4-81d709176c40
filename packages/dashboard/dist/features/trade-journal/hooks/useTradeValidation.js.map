{"version": 3, "file": "useTradeValidation.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-journal/hooks/useTradeValidation.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAQ9C;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IAE/E;;;;;OAKG;IACH,MAAM,kBAAkB,GAAG,WAAW,CACpC,CAAC,UAA2B,EAAE,SAAiB,EAAW,EAAE;QAC1D,MAAM,MAAM,GAAqB,EAAE,CAAC;QAEpC,+BAA+B;QAC/B,QAAQ,SAAS,EAAE;YACjB,KAAK,OAAO;gBACV,mCAAmC;gBACnC,IAAI,CAAC,UAAU,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI,GAAG,kBAAkB,CAAC;gBACvD,IAAI,CAAC,UAAU,CAAC,MAAM;oBAAE,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC;gBAC7D,IAAI,CAAC,UAAU,CAAC,UAAU;oBAAE,MAAM,CAAC,UAAU,GAAG,yBAAyB,CAAC;gBAC1E,IAAI,CAAC,UAAU,CAAC,SAAS;oBAAE,MAAM,CAAC,SAAS,GAAG,wBAAwB,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,QAAQ;oBAAE,MAAM,CAAC,QAAQ,GAAG,sBAAsB,CAAC;gBACnE,MAAM;YAER,KAAK,QAAQ;gBACX,kDAAkD;gBAClD,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,QAAQ,EAAE;oBAC/C,IAAI,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,EAAE;wBAC9C,MAAM,CAAC,QAAQ,GAAG,oCAAoC,CAAC;qBACxD;iBACF;gBAED,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,SAAS,EAAE;oBAC7C,IAAI,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE;wBAC5C,MAAM,CAAC,SAAS,GAAG,6CAA6C,CAAC;qBAClE;iBACF;gBACD,MAAM;YAER,KAAK,UAAU;gBACb,oDAAoD;gBACpD,IAAI,UAAU,CAAC,oBAAoB,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;oBACnE,MAAM,CAAC,gBAAgB,GAAG,oCAAoC,CAAC;iBAChE;gBAED,IAAI,UAAU,CAAC,sBAAsB,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;oBACvE,MAAM,CAAC,kBAAkB,GAAG,sCAAsC,CAAC;iBACpE;gBAED,2DAA2D;gBAC3D,IACE,UAAU,CAAC,gBAAgB;oBAC3B,UAAU,CAAC,kBAAkB;oBAC7B,UAAU,CAAC,gBAAgB,KAAK,UAAU,CAAC,kBAAkB,EAC7D;oBACA,MAAM,CAAC,kBAAkB,GAAG,qDAAqD,CAAC;iBACnF;gBACD,MAAM;YAER,KAAK,cAAc;gBACjB,wDAAwD;gBACxD,IAAI,UAAU,CAAC,OAAO,EAAE;oBACtB,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;wBAC3B,MAAM,CAAC,WAAW,GAAG,8BAA8B,CAAC;qBACrD;oBAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;wBAC3B,MAAM,CAAC,WAAW,GAAG,8BAA8B,CAAC;qBACrD;oBAED,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;wBAChE,MAAM,CAAC,UAAU,GAAG,wCAAwC,CAAC;qBAC9D;iBACF;gBAED,6CAA6C;gBAC7C,IACE,UAAU,CAAC,aAAa;oBACxB,UAAU,CAAC,aAAa,KAAK,WAAW;oBACxC,CAAC,UAAU,CAAC,eAAe,EAC3B;oBACA,MAAM,CAAC,eAAe,GAAG,mCAAmC,CAAC;iBAC9D;gBACD,MAAM;SACT;QAED,gDAAgD;QAChD,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE;YAC3B,2DAA2D;YAC3D,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAE9B,qCAAqC;YACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAClC,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IAC1C,CAAC,EACD,EAAE,CACH,CAAC;IAEF;;;;OAIG;IACH,MAAM,oBAAoB,GAAG,WAAW,CAAC,CAAC,UAA2B,EAAW,EAAE;QAChF,MAAM,MAAM,GAAqB,EAAE,CAAC;QAEpC,6BAA6B;QAC7B,IAAI,CAAC,UAAU,CAAC,IAAI;YAAE,MAAM,CAAC,IAAI,GAAG,kBAAkB,CAAC;QACvD,IAAI,CAAC,UAAU,CAAC,MAAM;YAAE,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,UAAU;YAAE,MAAM,CAAC,UAAU,GAAG,yBAAyB,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,SAAS;YAAE,MAAM,CAAC,SAAS,GAAG,wBAAwB,CAAC;QACvE,IAAI,CAAC,UAAU,CAAC,QAAQ;YAAE,MAAM,CAAC,QAAQ,GAAG,sBAAsB,CAAC;QAEnE,iDAAiD;QACjD,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC7B,GAAG,IAAI;YACP,GAAG,MAAM;SACV,CAAC,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;;OAGG;IACH,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,SAAiB,EAAE,EAAE;QACxD,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE;YAC3B,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAC9B,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACtC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,eAAe;QACf,cAAc;QACd,mBAAmB;KACpB,CAAC;AACJ,CAAC"}