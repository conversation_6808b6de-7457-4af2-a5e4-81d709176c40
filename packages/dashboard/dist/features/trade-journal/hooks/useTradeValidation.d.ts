/**
 * Trade Validation Hook
 *
 * Custom hook for validating trade form data
 */
export interface ValidationErrors {
    [key: string]: string;
}
/**
 * Hook for validating trade form data
 */
export declare function useTradeValidation(): {
    validationErrors: ValidationErrors;
    validateCurrentTab: (formValues: TradeFormValues, activeTab: string) => boolean;
    validateBasicInfoTab: (formValues: TradeFormValues) => boolean;
    clearFieldError: (fieldName: string) => void;
    clearAllErrors: () => void;
    setValidationErrors: import("react").Dispatch<import("react").SetStateAction<ValidationErrors>>;
};
export type TradeValidationHook = ReturnType<typeof useTradeValidation>;
//# sourceMappingURL=useTradeValidation.d.ts.map