/**
 * Trade Validation Hook
 *
 * Custom hook for validating trade form data
 */
import { useState, useCallback } from 'react';
/**
 * Hook for validating trade form data
 */
export function useTradeValidation() {
    const [validationErrors, setValidationErrors] = useState({});
    /**
     * Validate only the current tab
     * @param formValues The form values to validate
     * @param activeTab The active tab to validate
     * @returns Whether the validation passed
     */
    const validateCurrentTab = useCallback((formValues, activeTab) => {
        const errors = {};
        // Validate based on active tab
        switch (activeTab) {
            case 'basic':
                // Basic Info tab - required fields
                if (!formValues.date)
                    errors.date = 'Date is required';
                if (!formValues.symbol)
                    errors.symbol = 'Symbol is required';
                if (!formValues.entryPrice)
                    errors.entryPrice = 'Entry price is required';
                if (!formValues.exitPrice)
                    errors.exitPrice = 'Exit price is required';
                if (!formValues.quantity)
                    errors.quantity = 'Quantity is required';
                break;
            case 'timing':
                // Timing tab - validate only if fields are filled
                if (formValues.entryTime && formValues.exitTime) {
                    if (formValues.exitTime < formValues.entryTime) {
                        errors.exitTime = 'Exit time must be after entry time';
                    }
                }
                if (formValues.rdTime && formValues.entryTime) {
                    if (formValues.entryTime < formValues.rdTime) {
                        errors.entryTime = 'Entry time must be after risk/decision time';
                    }
                }
                break;
            case 'strategy':
                // Strategy tab - validate only if fields are filled
                if (formValues.primarySetupCategory && !formValues.primarySetupType) {
                    errors.primarySetupType = 'Please select a primary setup type';
                }
                if (formValues.secondarySetupCategory && !formValues.secondarySetupType) {
                    errors.secondarySetupType = 'Please select a secondary setup type';
                }
                // Validate primary and secondary setup types are different
                if (formValues.primarySetupType &&
                    formValues.secondarySetupType &&
                    formValues.primarySetupType === formValues.secondarySetupType) {
                    errors.secondarySetupType = 'Primary and secondary setup types must be different';
                }
                break;
            case 'dol-analysis':
                // DOL Analysis tab - validate only if fields are filled
                if (formValues.dolType) {
                    if (!formValues.dolStrength) {
                        errors.dolStrength = 'Please select a DOL strength';
                    }
                    if (!formValues.dolReaction) {
                        errors.dolReaction = 'Please select a DOL reaction';
                    }
                    if (!formValues.dolContext || formValues.dolContext.length === 0) {
                        errors.dolContext = 'Please select at least one DOL context';
                    }
                }
                // Validate DOL target type and specific type
                if (formValues.dolTargetType &&
                    formValues.dolTargetType !== 'RD Target' &&
                    !formValues.specificDOLType) {
                    errors.specificDOLType = 'Please select a specific DOL type';
                }
                break;
        }
        // Update validation errors for current tab only
        setValidationErrors((prev) => {
            // Remove any previous errors for fields in the current tab
            const newErrors = { ...prev };
            // Add new errors for the current tab
            Object.keys(errors).forEach((key) => {
                newErrors[key] = errors[key];
            });
            return newErrors;
        });
        return Object.keys(errors).length === 0;
    }, []);
    /**
     * Validate only the Basic Info tab (required for submission)
     * @param formValues The form values to validate
     * @returns Whether the validation passed
     */
    const validateBasicInfoTab = useCallback((formValues) => {
        const errors = {};
        // Required fields validation
        if (!formValues.date)
            errors.date = 'Date is required';
        if (!formValues.symbol)
            errors.symbol = 'Symbol is required';
        if (!formValues.entryPrice)
            errors.entryPrice = 'Entry price is required';
        if (!formValues.exitPrice)
            errors.exitPrice = 'Exit price is required';
        if (!formValues.quantity)
            errors.quantity = 'Quantity is required';
        // Update validation errors for basic info fields
        setValidationErrors((prev) => ({
            ...prev,
            ...errors,
        }));
        return Object.keys(errors).length === 0;
    }, []);
    /**
     * Clear validation errors for a specific field
     * @param fieldName The field name to clear errors for
     */
    const clearFieldError = useCallback((fieldName) => {
        setValidationErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
        });
    }, []);
    /**
     * Clear all validation errors
     */
    const clearAllErrors = useCallback(() => {
        setValidationErrors({});
    }, []);
    return {
        validationErrors,
        validateCurrentTab,
        validateBasicInfoTab,
        clearFieldError,
        clearAllErrors,
        setValidationErrors,
    };
}
//# sourceMappingURL=useTradeValidation.js.map