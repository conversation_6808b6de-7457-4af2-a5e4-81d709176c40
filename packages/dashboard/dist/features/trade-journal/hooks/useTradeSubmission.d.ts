/**
 * Trade Submission Hook
 *
 * Custom hook for handling trade form submission
 */
import { TradeFormValues } from '../types';
import { Trade } from '@adhd-trading-dashboard/shared';
/**
 * Hook for handling trade form submission
 * @param formValues The form values to submit
 * @param isEditMode Whether the form is in edit mode
 * @param isNewTrade Whether the form is for a new trade
 * @param tradeData The existing trade data (if editing)
 * @param validateBasicInfoTab Function to validate the basic info tab
 * @param validateCurrentTab Function to validate the current tab
 * @param activeTab The active tab
 * @param setActiveTab Function to set the active tab
 * @param setError Function to set the error message
 * @param setSuccess Function to set the success message
 */
export declare function useTradeSubmission(formValues: TradeFormValues, isEditMode: boolean, isNewTrade: boolean, tradeData: Trade | null, validateBasicInfoTab: () => boolean, validateCurrentTab: () => boolean, activeTab: string, setActiveTab: (tab: string) => void, setError: (error: string | null) => void, setSuccess: (success: string | null) => void): {
    handleSubmit: (e: React.FormEvent) => Promise<void>;
    isSubmitting: boolean;
};
export type TradeSubmissionHook = ReturnType<typeof useTradeSubmission>;
//# sourceMappingURL=useTradeSubmission.d.ts.map