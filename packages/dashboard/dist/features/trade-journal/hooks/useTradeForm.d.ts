/**
 * Trade Form Hook
 *
 * Custom hook for managing trade form state and logic
 */
export declare const MODEL_TYPE_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const SESSION_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const SETUP_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const MARKET_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const ENTRY_VERSION_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const PATTERN_QUALITY_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Main hook for managing trade form state and logic
 * @param tradeId The ID of the trade to load (optional)
 */
export declare const useTradeForm: (tradeId?: string) => {
    formValues: TradeFormValues;
    setFormValues: import("react").Dispatch<any>;
    handleChange: (e: import("react").ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    handleSubmit: (e: import("react").FormEvent<Element>) => Promise<void>;
    isSubmitting: boolean;
    isLoading: boolean;
    error: string | null;
    success: string | null;
    validationErrors: import("./useTradeValidation").ValidationErrors;
    isNewTrade: boolean;
    activeTab: string;
    handleTabChange: (newTab: string) => void;
    calculateProfitLoss: () => void;
};
//# sourceMappingURL=useTradeForm.d.ts.map