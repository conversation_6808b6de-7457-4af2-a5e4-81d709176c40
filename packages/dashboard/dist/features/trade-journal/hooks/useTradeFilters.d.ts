/**
 * useTradeFilters Hook
 *
 * Custom hook for managing trade filters
 */
import { FilterState, CompleteTradeData } from '../types';
/**
 * Hook for managing trade filters
 * @param trades The trades to filter
 */
export declare function useTradeFilters(trades: CompleteTradeData[]): {
    filters: FilterState;
    setFilters: import("react").Dispatch<import("react").SetStateAction<FilterState>>;
    handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    resetFilters: () => void;
    filteredTrades: CompleteTradeData[];
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
};
export type TradeFiltersHook = ReturnType<typeof useTradeFilters>;
//# sourceMappingURL=useTradeFilters.d.ts.map