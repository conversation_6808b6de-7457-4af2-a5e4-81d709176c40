/**
 * Trade Form Data Hook
 *
 * Custom hook for managing trade form state
 */
import { useState, useEffect, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';
/**
 * Hook for managing trade form data
 * @param tradeId The ID of the trade to load (optional)
 * @param isEditMode Whether the form is in edit mode
 * @param isNewTrade Whether the form is for a new trade
 */
export function useTradeFormData(tradeId, isEditMode, isNewTrade) {
    const [isLoading, setIsLoading] = useState(isEditMode);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    const [tradeData, setTradeData] = useState(null);
    // Initialize form values with defaults
    const [formValues, setFormValues] = useState({
        // Basic Information
        date: new Date().toISOString().split('T')[0],
        symbol: '',
        direction: 'long',
        quantity: '',
        // Price Information
        entryPrice: '',
        exitPrice: '',
        stopLoss: '',
        takeProfit: '',
        profit: '',
        // Extended Trading Fields
        modelType: '',
        session: '',
        setup: '',
        entryTime: '',
        exitTime: '',
        rdTime: '',
        market: 'Stocks',
        rMultiple: '',
        entryVersion: 'First Entry',
        riskPoints: '',
        patternQuality: '5',
        // Setup Classification Fields
        primarySetupCategory: '',
        primarySetupType: '',
        secondarySetupCategory: '',
        secondarySetupType: '',
        liquidityTaken: '',
        additionalFVGs: [],
        dolTargetType: '',
        specificDOLType: '',
        parentPDArray: '',
        // Pattern Quality Assessment Fields
        patternQualityClarity: '',
        patternQualityConfluence: '',
        patternQualityContext: '',
        patternQualityRisk: '',
        patternQualityReward: '',
        patternQualityTimeframe: '',
        patternQualityVolume: '',
        patternQualityNotes: '',
        // DOL Analysis Fields
        dolType: '',
        dolStrength: '',
        dolReaction: '',
        dolContext: [],
        dolPriceAction: '',
        dolVolumeProfile: '',
        dolTimeOfDay: '',
        dolMarketStructure: '',
        dolEffectiveness: '5',
        dolNotes: '',
        // Additional Information
        notes: '',
        tags: [],
        result: 'win',
    });
    // Load trade data if in edit mode
    useEffect(() => {
        const loadTradeData = async () => {
            console.log(`loadTradeData called - isEditMode: ${isEditMode}, tradeId: "${tradeId}"`);
            // Validate tradeId before proceeding
            if (!tradeId) {
                console.error('loadTradeData: tradeId is undefined or null');
                return;
            }
            if (tradeId === 'new') {
                console.log('Creating new trade, skipping data loading');
                return;
            }
            if (isEditMode) {
                try {
                    console.log(`Attempting to load trade data for ID: ${tradeId}`);
                    setIsLoading(true);
                    // Ensure tradeId is a valid string
                    const sanitizedId = String(tradeId).trim();
                    if (!sanitizedId) {
                        throw new Error('Invalid trade ID: empty or whitespace');
                    }
                    console.log(`Calling tradeStorageService.getTradeById with ID: ${sanitizedId}`);
                    const trade = await tradeStorageService.getTradeById(Number(sanitizedId));
                    console.log(`Trade data retrieved:`, trade);
                    if (trade) {
                        console.log(`Trade found, setting trade data`);
                        setTradeData(trade);
                        console.log(`Converting CompleteTradeData to form values`);
                        // Convert CompleteTradeData to form values
                        const { trade: tradeRecord, fvg_details, setup, analysis } = trade;
                        setFormValues({
                            // Basic Information from TradeRecord
                            date: tradeRecord.date,
                            symbol: tradeRecord.market || 'MNQ',
                            direction: tradeRecord.direction === 'Long' ? 'long' : 'short',
                            quantity: String(tradeRecord.no_of_contracts || 0),
                            // Price Information from TradeRecord
                            entryPrice: String(tradeRecord.entry_price || 0),
                            exitPrice: String(tradeRecord.exit_price || 0),
                            stopLoss: '',
                            takeProfit: '',
                            profit: String(tradeRecord.achieved_pl || 0),
                            // Extended Trading Fields from TradeRecord
                            modelType: tradeRecord.model_type || '',
                            session: tradeRecord.session || '',
                            setup: setup?.primary_setup || '',
                            entryTime: tradeRecord.entry_time || '',
                            exitTime: tradeRecord.exit_time || '',
                            rdTime: tradeRecord.rd_time || '',
                            market: tradeRecord.market || 'MNQ',
                            rMultiple: tradeRecord.r_multiple ? String(tradeRecord.r_multiple) : '',
                            entryVersion: fvg_details?.entry_version || 'First Entry',
                            riskPoints: tradeRecord.risk_points ? String(tradeRecord.risk_points) : '',
                            patternQuality: tradeRecord.pattern_quality_rating
                                ? String(tradeRecord.pattern_quality_rating)
                                : '5',
                            // Setup Classification Fields from TradeSetup
                            primarySetupCategory: '',
                            primarySetupType: setup?.primary_setup || '',
                            secondarySetupCategory: '',
                            secondarySetupType: setup?.secondary_setup || '',
                            liquidityTaken: setup?.liquidity_taken || '',
                            additionalFVGs: setup?.additional_fvgs ? setup.additional_fvgs.split(', ') : [],
                            dolTargetType: analysis?.dol_target_type || '',
                            specificDOLType: fvg_details?.draw_on_liquidity || '',
                            parentPDArray: '',
                            // Pattern Quality Assessment Fields (not in new schema, keep empty)
                            patternQualityClarity: '',
                            patternQualityConfluence: '',
                            patternQualityContext: '',
                            patternQualityRisk: '',
                            patternQualityReward: '',
                            patternQualityTimeframe: '',
                            patternQualityVolume: '',
                            patternQualityNotes: '',
                            // DOL Analysis Fields from TradeAnalysis
                            dolType: analysis?.dol_target_type || '',
                            dolStrength: '',
                            dolReaction: '',
                            dolContext: [],
                            dolPriceAction: analysis?.path_quality || '',
                            dolVolumeProfile: analysis?.clustering || '',
                            dolTimeOfDay: '',
                            dolMarketStructure: '',
                            dolEffectiveness: '5',
                            dolNotes: analysis?.dol_notes || '',
                            // Additional Information
                            notes: tradeRecord.notes || '',
                            tags: [],
                            result: tradeRecord.win_loss === 'Win'
                                ? 'win'
                                : tradeRecord.win_loss === 'Loss'
                                    ? 'loss'
                                    : 'breakeven',
                        });
                    }
                    else {
                        console.error(`Trade with ID ${tradeId} not found in IndexedDB`);
                        setError(`Trade with ID ${tradeId} not found.`);
                    }
                }
                catch (err) {
                    console.error('Error loading trade:', err);
                    setError('Failed to load trade data. Please try again.');
                }
                finally {
                    console.log(`Setting isLoading to false`);
                    setIsLoading(false);
                }
            }
        };
        loadTradeData();
    }, [isEditMode, tradeId]);
    /**
     * Handle form field changes
     * @param e The change event
     */
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormValues((prev) => ({
            ...prev,
            [name]: value,
        }));
    }, []);
    return {
        formValues,
        setFormValues,
        handleChange,
        isLoading,
        setIsLoading,
        error,
        setError,
        success,
        setSuccess,
        tradeData,
    };
}
//# sourceMappingURL=useTradeFormData.js.map