import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Dashboard Page Component
 *
 * This is the main dashboard page that displays trading metrics and charts.
 */
import { useEffect } from "react";
import styled from "styled-components";
import { MetricsPanel } from "./components/MetricsPanel";
import { PerformanceChart } from "./components/PerformanceChart";
import { RecentTradesPanel } from "./components/RecentTradesPanel";
import { useDashboardData } from "./hooks/useDashboardData";
const PageContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
const PageHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const ChartSection = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const ChartTitle = styled.h2 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
`;
const ChartPlaceholder = styled.div `
  height: 300px;
  background-color: ${({ theme }) => theme.colors.chartGrid};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const RecentTradesSection = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const Dashboard = () => {
    const { metrics, chartData, recentTrades, isLoading, fetchDashboardData } = useDashboardData();
    useEffect(() => {
        fetchDashboardData();
    }, [fetchDashboardData]);
    return (_jsxs(PageContainer, { children: [_jsx(PageHeader, { children: _jsx(Title, { children: "Trading Dashboard" }) }), _jsx(MetricsPanel, { metrics: metrics, isLoading: isLoading }), _jsxs(ChartSection, { children: [_jsx(ChartTitle, { children: "Performance" }), isLoading ? (_jsx(ChartPlaceholder, { children: "Loading chart data..." })) : (_jsx(PerformanceChart, { data: chartData }))] }), _jsxs(RecentTradesSection, { children: [_jsx(ChartTitle, { children: "Recent Trades" }), _jsx(RecentTradesPanel, { trades: recentTrades, isLoading: isLoading })] })] }));
};
export default Dashboard;
//# sourceMappingURL=Dashboard.js.map