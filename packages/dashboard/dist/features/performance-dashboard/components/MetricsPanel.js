import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from "styled-components";
const Container = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const MetricCard = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const MetricTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
`;
const MetricValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const LoadingIndicator = styled.div `
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const MetricsPanel = ({ metrics, isLoading = false, }) => {
    if (isLoading) {
        return (_jsx(Container, { children: [1, 2, 3, 4].map((i) => (_jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Loading..." }), _jsx(LoadingIndicator, { children: "Fetching data..." })] }, i))) }));
    }
    return (_jsx(Container, { children: metrics.map((metric, index) => (_jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: metric.title }), _jsx(MetricValue, { children: metric.value })] }, index))) }));
};
//# sourceMappingURL=MetricsPanel.js.map