{"version": 3, "file": "TradeAnalysis.js", "sourceRoot": "", "sources": ["../../../src/features/trade-analysis/TradeAnalysis.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACxC,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAC1D,OAAO,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AACvF,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,wBAAwB,EAAE,MAAM,uCAAuC,CAAC;AACjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAIvD,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;aAG3B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC3C,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;;;;mBAIV,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAA;eACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;;WAEtC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;;CAEjD,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAA;;SAElB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;mBACrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;6BACrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;oBAC3C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAClD,CAAC;AAEF,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAqB;;;aAGrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE;eACtD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CACnC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO;WACxD,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;;6BAEjE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;oBAC/E,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI;;;aAG5C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;CAE/C,CAAC;AAEF,MAAM,oBAAoB,GAAa,GAAG,EAAE;IAC1C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAC/E,gBAAgB,EAAE,CAAC;IACrB,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CACzC,WAAW,CAAC,WAAwB,IAAI,SAAS,CACnD,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,IAAc,EAAE,EAAE;QAC1C,aAAa,CAAC,IAAI,CAAC,CAAC;QACpB,iBAAiB,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,UAAU,EAAE;YAClB,KAAK,SAAS;gBACZ,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,qBAAqB,EAC3B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,EACvB,YAAY,EAAC,yDAAyD,YAEtE,KAAC,kBAAkB,KAAG,GACb,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,IACV,CACJ,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,QAAQ,EACd,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAClD,YAAY,EAAC,+CAA+C,YAE5D,KAAC,WAAW,KAAG,GACN,EAEV,eAAe,IAAI,KAAC,WAAW,KAAG,IAClC,CACJ,CAAC;YAEJ,KAAK,SAAS;gBACZ,OAAO,CACL,KAAC,QAAQ,IACP,KAAK,EAAC,uBAAuB,EAC7B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EACxE,YAAY,EAAC,gEAAgE,YAE7E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,QAAQ,GAAG,GACpD,CACZ,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO,CACL,KAAC,QAAQ,IACP,KAAK,EAAC,yBAAyB,EAC/B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAC5E,YAAY,EAAC,kEAAkE,YAE/E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,UAAU,EAAC,KAAK,EAAC,UAAU,GAAG,GACxD,CACZ,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,0BAA0B,EAChC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,mEAAmE,YAEhF,KAAC,wBAAwB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,WAAW,GAAG,GAC1D,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,wBAAwB,EAC9B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAC1E,YAAY,EAAC,iEAAiE,YAE9E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAG,GACtD,IACV,CACJ,CAAC;YAEJ,KAAK,MAAM;gBACT,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,IACV,CACJ,CAAC;YAEJ;gBACE,OAAO,IAAI,CAAC;SACf;IACH,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,aAAa,eACZ,KAAC,UAAU,cACT,KAAC,KAAK,iCAAuB,GAClB,EAEb,KAAC,WAAW,KAAG,EAEf,MAAC,QAAQ,eACP,KAAC,OAAO,IAAC,MAAM,EAAE,UAAU,KAAK,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,wBAE3E,EACV,KAAC,OAAO,IAAC,MAAM,EAAE,UAAU,KAAK,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,uBAEzE,EACV,KAAC,OAAO,IAAC,MAAM,EAAE,UAAU,KAAK,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,wBAE3E,EACV,KAAC,OAAO,IACN,MAAM,EAAE,UAAU,KAAK,YAAY,EACnC,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,YAAY,CAAC,2BAGrC,EACV,KAAC,OAAO,IACN,MAAM,EAAE,UAAU,KAAK,YAAY,EACnC,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,YAAY,CAAC,2BAGrC,EACV,KAAC,OAAO,IAAC,MAAM,EAAE,UAAU,KAAK,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,8BAErE,IACD,EAEV,aAAa,EAAE,IACF,CACjB,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,aAAa,GAAa,GAAG,EAAE;IACnC,OAAO,CACL,KAAC,qBAAqB,cACpB,KAAC,oBAAoB,KAAG,GACF,CACzB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,aAAa,CAAC"}