{"version": 3, "file": "useTradeAnalysis.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/useTradeAnalysis.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,mBAAmB,EAAS,MAAM,gCAAgC,CAAC;AAC5E,OAAO,EACL,qBAAqB,EACrB,wBAAwB,EACxB,uBAAuB,EACvB,YAAY,EACZ,UAAU,EACV,UAAU,EACV,cAAc,EACd,qBAAqB,EACrB,gBAAgB,EAChB,kBAAkB,EAClB,eAAe,EACf,WAAW,EACX,oBAAoB,GACrB,MAAM,sBAAsB,CAAC;AAG9B;;;;GAIG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,qBAAqB,EAAE,CAAC;IACpD,MAAM,OAAO,GAAG,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;IAE9D,YAAY;IACZ,MAAM,MAAM,GAAG,wBAAwB,CAAC,YAAY,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAClD,MAAM,IAAI,GAAG,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,wBAAwB,CAAC,cAAc,CAAC,CAAC;IAC1D,MAAM,eAAe,GAAG,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;IACxE,MAAM,UAAU,GAAG,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IAC9D,MAAM,YAAY,GAAG,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,wBAAwB,CAAC,eAAe,CAAC,CAAC;IAC5D,MAAM,KAAK,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;IAEpD,uBAAuB;IACvB,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAM,IAAI,CAAC,CAAC;IAC1D,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAU,KAAK,CAAC,CAAC;IACjE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IAE7D,+BAA+B;IAC/B,MAAM,kBAAkB,GAAG;QACzB,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,MAAM,EAAE,IAAI,CAAC,KAAK;QAClB,aAAa,EAAE,IAAI,CAAC,SAAS;KAC9B,CAAC;IAEF,2BAA2B;IAC3B,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAgB,EAAE,CAAC,CAAC;IAC1E,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAoB,EAAE,CAAC,CAAC;IAChF,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAA4B,IAAI,CAAC,CAAC;IAExE,uDAAuD;IACvD,SAAS,CAAC,GAAG,EAAE;QACb,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,sCAAsC;IACtC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,WAAW,EAAE,IAAI,EAAE;YACrB,MAAM,MAAM,GAAY,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACtD,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAwB;gBACrC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,IAAI,EAAE,EAAE,EAAE,mCAAmC;aAC9C,CAAC,CAAC,CAAC;YACJ,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC3B;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAE3B,uBAAuB;IACvB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;IAE5B,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,QAAQ,EAAE;YACZ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SACpC;aAAM;YACL,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACxB;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAExB,kCAAkC;IAClC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,6BAA6B;YAC7B,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CACzC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAClE,CAAC;YAEF,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,kBAAkB;YAC7C,MAAM,UAAU,GAAkB,EAAE,CAAC;YAErC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACpC,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC;gBAC9B,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,aAAa;oBACrB,QAAQ,EAAE,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,iCAAiC;iBACjE,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAE/B,6BAA6B;YAC7B,MAAM,YAAY,GAAG;gBACnB,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE;gBAC5D,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE;gBAC/D,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE;gBAC/D,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE;gBACvD,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;gBACtD,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC1D,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC1D,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;aACzD,CAAC;YAEF,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAC/B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,CAClE,CAAC,MAAM,CAAC;gBAET,OAAO;oBACL,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,KAAK;oBACL,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAElC,mBAAmB;YACnB,IAAI,YAAY,EAAE;gBAChB,UAAU,CAAC;oBACT,OAAO,EAAE,YAAY,CAAC,OAAO;oBAC7B,YAAY,EAAE,YAAY,CAAC,YAAY;oBACvC,UAAU,EAAE,YAAY,CAAC,UAAU;oBACnC,WAAW,EAAE,YAAY,CAAC,WAAW;oBACrC,WAAW,EAAE,YAAY,CAAC,WAAW;oBACrC,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,UAAU,EACR,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC,OAAO;wBAC9C,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;oBACvD,WAAW,EAAE,IAAI;oBACjB,WAAW,EAAE,GAAG;oBAChB,aAAa,EAAE,CAAC,EAAE,wBAAwB;iBAC3C,CAAC,CAAC;aACJ;SACF;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;IAEjC,6CAA6C;IAC7C,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACzC,IAAI;YACF,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,MAAM,QAAQ,GAAG,MAAM,mBAAmB,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAC9E,cAAc,CAAC,QAAQ,CAAC,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,WAAW,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;SAC1F;gBAAS;YACR,eAAe,CAAC,KAAK,CAAC,CAAC;SACxB;IACH,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAEzB,gBAAgB;IAChB,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,MAAM,GAAG,GAAG;YACV,aAAa;YACb,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CACvF,GAAG,CACJ;YACD,WAAW;YACX,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACtB;gBACE,KAAK,CAAC,IAAI;gBACV,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,IAAI;gBACV,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,GAAG;gBACxB,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;aACpC,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ;SACF,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,6BAA6B;QAC7B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QACnD,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;QACb,CAAC,CAAC,QAAQ,GAAG,iBAAiB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3E,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,KAAK,EAAE,CAAC;QACV,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7B,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnB,OAAO;QACL,cAAc;QACd,OAAO;QACP,eAAe;QACf,gBAAgB;QAEhB,YAAY;QACZ,MAAM,EAAE,eAAe;QACvB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,SAAS;QACT,KAAK;QAEL,UAAU;QACV,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,WAAW,EAAE,OAAO,CAAC,WAAW;QAEhC,cAAc;QACd,WAAW;QACX,YAAY;KACb,CAAC;AACJ,CAAC,CAAC"}