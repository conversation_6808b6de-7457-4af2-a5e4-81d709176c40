/**
 * Trade Analysis Context
 *
 * Context for managing trade analysis state
 */
import React, { ReactNode } from 'react';
import { TradeAnalysisState, TradeFilters, UserPreferences } from '../types';
interface TradeAnalysisContextType extends TradeAnalysisState {
    fetchData: () => Promise<void>;
    updateFilters: (filters: Partial<TradeFilters>) => void;
    updatePreferences: (preferences: Partial<UserPreferences>) => void;
    selectTrade: (tradeId: string | null) => void;
    resetFilters: () => void;
}
interface TradeAnalysisProviderProps {
    children: ReactNode;
}
export declare const TradeAnalysisProvider: React.FC<TradeAnalysisProviderProps>;
export declare const useTradeAnalysis: () => TradeAnalysisContextType;
export {};
//# sourceMappingURL=TradeAnalysisContext.d.ts.map