/**
 * Trade Analysis State
 *
 * This module exports the state management for the trade analysis feature.
 */
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
export declare enum TradeAnalysisActionTypes {
    SET_FILTER = "tradeAnalysis/SET_FILTER",
    CLEAR_FILTERS = "tradeAnalysis/CLEAR_FILTERS",
    SET_SORT = "tradeAnalysis/SET_SORT",
    SET_PAGE = "tradeAnalysis/SET_PAGE",
    SET_PAGE_SIZE = "tradeAnalysis/SET_PAGE_SIZE",
    SET_TRADES = "tradeAnalysis/SET_TRADES",
    SET_LOADING = "tradeAnalysis/SET_LOADING",
    SET_ERROR = "tradeAnalysis/SET_ERROR"
}
export interface SetFilterAction {
    type: TradeAnalysisActionTypes.SET_FILTER;
    payload: {
        key: keyof TradeFilter;
        value: any;
    };
}
export interface ClearFiltersAction {
    type: TradeAnalysisActionTypes.CLEAR_FILTERS;
}
export interface SetSortAction {
    type: TradeAnalysisActionTypes.SET_SORT;
    payload: {
        field: string;
        direction: 'asc' | 'desc';
    };
}
export interface SetPageAction {
    type: TradeAnalysisActionTypes.SET_PAGE;
    payload: number;
}
export interface SetPageSizeAction {
    type: TradeAnalysisActionTypes.SET_PAGE_SIZE;
    payload: number;
}
export interface SetTradesAction {
    type: TradeAnalysisActionTypes.SET_TRADES;
    payload: CompleteTradeData[];
}
export interface SetLoadingAction {
    type: TradeAnalysisActionTypes.SET_LOADING;
    payload: boolean;
}
export interface SetErrorAction {
    type: TradeAnalysisActionTypes.SET_ERROR;
    payload: string | null;
}
export type TradeAnalysisAction = SetFilterAction | ClearFiltersAction | SetSortAction | SetPageAction | SetPageSizeAction | SetTradesAction | SetLoadingAction | SetErrorAction;
export interface TradeFilter {
    startDate?: string;
    endDate?: string;
    symbol?: string;
    direction?: 'Long' | 'Short';
    modelType?: string;
    session?: string;
    minProfit?: number;
    maxProfit?: number;
    minRMultiple?: number;
    maxRMultiple?: number;
    winLoss?: 'Win' | 'Loss';
}
export interface TradeSort {
    field: string;
    direction: 'asc' | 'desc';
}
export interface TradeAnalysisState {
    trades: CompleteTradeData[];
    filter: TradeFilter;
    sort: TradeSort;
    page: number;
    pageSize: number;
    isLoading: boolean;
    error: string | null;
}
export declare const initialTradeAnalysisState: TradeAnalysisState;
export declare const tradeAnalysisReducer: (state: TradeAnalysisState, action: TradeAnalysisAction) => TradeAnalysisState;
export declare const TradeAnalysisContext: any, TradeAnalysisProvider: any, useTradeAnalysisStore: any, useTradeAnalysisSelector: any, useTradeAnalysisAction: any, useTradeAnalysisActions: any;
export declare const tradeAnalysisActions: {
    setFilter: (key: keyof TradeFilter, value: any) => SetFilterAction;
    clearFilters: () => ClearFiltersAction;
    setSort: (field: string, direction: 'asc' | 'desc') => SetSortAction;
    setPage: (page: number) => SetPageAction;
    setPageSize: (pageSize: number) => SetPageSizeAction;
    setTrades: (trades: CompleteTradeData[]) => SetTradesAction;
    setLoading: (isLoading: boolean) => SetLoadingAction;
    setError: (error: string | null) => SetErrorAction;
};
export declare const selectTrades: (state: TradeAnalysisState) => CompleteTradeData[];
export declare const selectFilter: (state: TradeAnalysisState) => TradeFilter;
export declare const selectSort: (state: TradeAnalysisState) => TradeSort;
export declare const selectPage: (state: TradeAnalysisState) => number;
export declare const selectPageSize: (state: TradeAnalysisState) => number;
export declare const selectIsLoading: (state: TradeAnalysisState) => boolean;
export declare const selectError: (state: TradeAnalysisState) => string | null;
export declare const selectFilteredTrades: any;
export declare const selectSortedTrades: any;
export declare const selectPaginatedTrades: any;
export declare const selectTotalPages: any;
export declare const selectTradeSummary: any;
//# sourceMappingURL=tradeAnalysisState.d.ts.map