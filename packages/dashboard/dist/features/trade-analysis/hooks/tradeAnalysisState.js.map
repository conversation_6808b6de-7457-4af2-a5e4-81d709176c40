{"version": 3, "file": "tradeAnalysisState.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/tradeAnalysisState.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EACL,kBAAkB,EAClB,cAAc,EACd,YAAY,GAIb,MAAM,gCAAgC,CAAC;AAExC,eAAe;AACf,MAAM,CAAN,IAAY,wBASX;AATD,WAAY,wBAAwB;IAClC,mEAAuC,CAAA;IACvC,yEAA6C,CAAA;IAC7C,+DAAmC,CAAA;IACnC,+DAAmC,CAAA;IACnC,yEAA6C,CAAA;IAC7C,mEAAuC,CAAA;IACvC,qEAAyC,CAAA;IACzC,iEAAqC,CAAA;AACvC,CAAC,EATW,wBAAwB,KAAxB,wBAAwB,QASnC;AA2FD,gBAAgB;AAChB,MAAM,CAAC,MAAM,yBAAyB,GAAuB;IAC3D,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,IAAI,EAAE;QACJ,KAAK,EAAE,MAAM;QACb,SAAS,EAAE,MAAM;KAClB;IACD,IAAI,EAAE,CAAC;IACP,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,KAAK;IAChB,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,UAAU;AACV,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAClC,KAAyB,EACzB,MAA2B,EACP,EAAE;IACtB,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,EAAE;oBACN,GAAG,KAAK,CAAC,MAAM;oBACf,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;iBAC3C;gBACD,0CAA0C;gBAC1C,IAAI,EAAE,CAAC;aACR,CAAC;QACJ,KAAK,wBAAwB,CAAC,aAAa;YACzC,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,CAAC;aACR,CAAC;QACJ,KAAK,wBAAwB,CAAC,QAAQ;YACpC,OAAO;gBACL,GAAG,KAAK;gBACR,IAAI,EAAE,MAAM,CAAC,OAAO;aACrB,CAAC;QACJ,KAAK,wBAAwB,CAAC,QAAQ;YACpC,OAAO;gBACL,GAAG,KAAK;gBACR,IAAI,EAAE,MAAM,CAAC,OAAO;aACrB,CAAC;QACJ,KAAK,wBAAwB,CAAC,aAAa;YACzC,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,EAAE,MAAM,CAAC,OAAO;gBACxB,6CAA6C;gBAC7C,IAAI,EAAE,CAAC;aACR,CAAC;QACJ,KAAK,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,EAAE,MAAM,CAAC,OAAO;aACvB,CAAC;QACJ,KAAK,wBAAwB,CAAC,WAAW;YACvC,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS,EAAE,MAAM,CAAC,OAAO;aAC1B,CAAC;QACJ,KAAK,wBAAwB,CAAC,SAAS;YACrC,OAAO;gBACL,GAAG,KAAK;gBACR,KAAK,EAAE,MAAM,CAAC,OAAO;aACtB,CAAC;QACJ;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC,CAAC;AAEF,gBAAgB;AAChB,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,qBAAqB,EAAE,GAAG,YAAY,CACrF,oBAAoB,EACpB;IACE,GAAG,EAAE,eAAe;IACpB,YAAY,EAAE,yBAAyB;IACvC,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAClB,0CAA0C;QAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;KACzB,CAAC;CACH,CACF,CAAC;AAEF,uBAAuB;AACvB,MAAM,CAAC,MAAM,EACX,OAAO,EAAE,oBAAoB,EAC7B,QAAQ,EAAE,qBAAqB,EAC/B,QAAQ,EAAE,qBAAqB,EAC/B,WAAW,EAAE,wBAAwB,EACrC,SAAS,EAAE,sBAAsB,EACjC,UAAU,EAAE,uBAAuB,GACpC,GAAG,kBAAkB,CACpB,gBAAgB,EAChB,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;AAEF,kBAAkB;AAClB,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAClC,SAAS,EAAE,CAAC,GAAsB,EAAE,KAAU,EAAmB,EAAE,CAAC,CAAC;QACnE,IAAI,EAAE,wBAAwB,CAAC,UAAU;QACzC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;KACxB,CAAC;IACF,YAAY,EAAE,GAAuB,EAAE,CAAC,CAAC;QACvC,IAAI,EAAE,wBAAwB,CAAC,aAAa;KAC7C,CAAC;IACF,OAAO,EAAE,CAAC,KAAa,EAAE,SAAyB,EAAiB,EAAE,CAAC,CAAC;QACrE,IAAI,EAAE,wBAAwB,CAAC,QAAQ;QACvC,OAAO,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;KAC9B,CAAC;IACF,OAAO,EAAE,CAAC,IAAY,EAAiB,EAAE,CAAC,CAAC;QACzC,IAAI,EAAE,wBAAwB,CAAC,QAAQ;QACvC,OAAO,EAAE,IAAI;KACd,CAAC;IACF,WAAW,EAAE,CAAC,QAAgB,EAAqB,EAAE,CAAC,CAAC;QACrD,IAAI,EAAE,wBAAwB,CAAC,aAAa;QAC5C,OAAO,EAAE,QAAQ;KAClB,CAAC;IACF,SAAS,EAAE,CAAC,MAA2B,EAAmB,EAAE,CAAC,CAAC;QAC5D,IAAI,EAAE,wBAAwB,CAAC,UAAU;QACzC,OAAO,EAAE,MAAM;KAChB,CAAC;IACF,UAAU,EAAE,CAAC,SAAkB,EAAoB,EAAE,CAAC,CAAC;QACrD,IAAI,EAAE,wBAAwB,CAAC,WAAW;QAC1C,OAAO,EAAE,SAAS;KACnB,CAAC;IACF,QAAQ,EAAE,CAAC,KAAoB,EAAkB,EAAE,CAAC,CAAC;QACnD,IAAI,EAAE,wBAAwB,CAAC,SAAS;QACxC,OAAO,EAAE,KAAK;KACf,CAAC;CACH,CAAC;AAEF,YAAY;AACZ,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;AACxE,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;AACxE,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;AACpE,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;AACpE,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC5E,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC;AAC9E,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;AAEtE,qBAAqB;AACrB,MAAM,CAAC,MAAM,oBAAoB,GAAG,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;IAChG,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;QACjC,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC;QAE5B,oBAAoB;QACpB,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACzE,OAAO,KAAK,CAAC;SACd;QACD,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACrE,OAAO,KAAK,CAAC;SACd;QAED,+CAA+C;QAC/C,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC1E,OAAO,KAAK,CAAC;SACd;QAED,yBAAyB;QACzB,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE;YAC5D,OAAO,KAAK,CAAC;SACd;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE;YAC7D,OAAO,KAAK,CAAC;SACd;QAED,uBAAuB;QACvB,IAAI,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,EAAE;YACtD,OAAO,KAAK,CAAC;SACd;QAED,sBAAsB;QACtB,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE;YACjF,OAAO,KAAK,CAAC;SACd;QACD,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE;YACjF,OAAO,KAAK,CAAC;SACd;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,YAAY,EAAE;YACtF,OAAO,KAAK,CAAC;SACd;QACD,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,YAAY,EAAE;YACtF,OAAO,KAAK,CAAC;SACd;QAED,wBAAwB;QACxB,IAAI,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,OAAO,EAAE;YACvD,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,kBAAkB,GAAG,cAAc,CAC9C,oBAAoB,EACpB,UAAU,EACV,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;IACf,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC/B,+CAA+C;QAC/C,IAAI,MAAW,CAAC;QAChB,IAAI,MAAW,CAAC;QAEhB,QAAQ,IAAI,CAAC,KAAK,EAAE;YAClB,KAAK,MAAM;gBACT,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtB,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;gBAClC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;gBAClC,MAAM;YACR,KAAK,WAAW;gBACd,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC3B,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC3B,MAAM;YACR;gBACE,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAA6B,CAAC,CAAC;gBACrD,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAA6B,CAAC,CAAC;SACxD;QAED,IAAI,MAAM,GAAG,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QACD,IAAI,MAAM,GAAG,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QACD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;AACL,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,cAAc,CACjD,kBAAkB,EAClB,UAAU,EACV,cAAc,EACd,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;IACzB,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IACzC,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC,CAAC;AACzD,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,cAAc,CAC5C,oBAAoB,EACpB,cAAc,EACd,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;IACnB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;AAC7C,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,EAAE;IAChF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAClC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IAClG,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACjG,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CACnC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,KAAK,CAAC,CACxD,CAAC,MAAM,CAAC;IAET,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAC5D,CAAC,CACF,CAAC;IACF,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,iCAAiC;IACtD,MAAM,SAAS,GAAG,WAAW,GAAG,SAAS,CAAC;IAE1C,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAElE,MAAM,UAAU,GACd,aAAa,GAAG,CAAC;QACf,CAAC,CAAC,MAAM;aACH,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7D,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa;QAC5F,CAAC,CAAC,CAAC,CAAC;IAER,MAAM,WAAW,GACf,YAAY,GAAG,CAAC;QACd,CAAC,CAAC,MAAM;aACH,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7D,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY;QAC3F,CAAC,CAAC,CAAC,CAAC;IAER,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAExF,qCAAqC;IACrC,MAAM,gBAAgB,GACpB,WAAW,GAAG,CAAC;QACb,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW;QAC7F,CAAC,CAAC,CAAC,CAAC;IAER,OAAO;QACL,WAAW;QACX,aAAa;QACb,YAAY;QACZ,eAAe;QACf,WAAW;QACX,SAAS;QACT,SAAS;QACT,OAAO;QACP,UAAU;QACV,WAAW;QACX,YAAY;QACZ,gBAAgB;KACjB,CAAC;AACJ,CAAC,CAAC,CAAC"}