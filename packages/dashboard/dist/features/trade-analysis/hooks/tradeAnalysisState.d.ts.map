{"version": 3, "file": "tradeAnalysisState.d.ts", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/tradeAnalysisState.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAML,iBAAiB,EAClB,MAAM,gCAAgC,CAAC;AAGxC,oBAAY,wBAAwB;IAClC,UAAU,6BAA6B;IACvC,aAAa,gCAAgC;IAC7C,QAAQ,2BAA2B;IACnC,QAAQ,2BAA2B;IACnC,aAAa,gCAAgC;IAC7C,UAAU,6BAA6B;IACvC,WAAW,8BAA8B;IACzC,SAAS,4BAA4B;CACtC;AAGD,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,wBAAwB,CAAC,UAAU,CAAC;IAC1C,OAAO,EAAE;QACP,GAAG,EAAE,MAAM,WAAW,CAAC;QACvB,KAAK,EAAE,GAAG,CAAC;KACZ,CAAC;CACH;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,wBAAwB,CAAC,aAAa,CAAC;CAC9C;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC;IACxC,OAAO,EAAE;QACP,KAAK,EAAE,MAAM,CAAC;QACd,SAAS,EAAE,KAAK,GAAG,MAAM,CAAC;KAC3B,CAAC;CACH;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC;IACxC,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,wBAAwB,CAAC,aAAa,CAAC;IAC7C,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,wBAAwB,CAAC,UAAU,CAAC;IAC1C,OAAO,EAAE,iBAAiB,EAAE,CAAC;CAC9B;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,wBAAwB,CAAC,WAAW,CAAC;IAC3C,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,wBAAwB,CAAC,SAAS,CAAC;IACzC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;CACxB;AAGD,MAAM,MAAM,mBAAmB,GAC3B,eAAe,GACf,kBAAkB,GAClB,aAAa,GACb,aAAa,GACb,iBAAiB,GACjB,eAAe,GACf,gBAAgB,GAChB,cAAc,CAAC;AAGnB,MAAM,WAAW,WAAW;IAC1B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;IAC7B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,OAAO,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;CAC1B;AAGD,MAAM,WAAW,SAAS;IACxB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,KAAK,GAAG,MAAM,CAAC;CAC3B;AAGD,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,iBAAiB,EAAE,CAAC;IAC5B,MAAM,EAAE,WAAW,CAAC;IACpB,IAAI,EAAE,SAAS,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,OAAO,CAAC;IACnB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAGD,eAAO,MAAM,yBAAyB,EAAE,kBAWvC,CAAC;AAGF,eAAO,MAAM,oBAAoB,UACxB,kBAAkB,UACjB,mBAAmB,KAC1B,kBAqDF,CAAC;AAmBF,eAAO,MACI,oBAAoB,OACnB,qBAAqB,OACrB,qBAAqB,OAClB,wBAAwB,OAC1B,sBAAsB,OACrB,uBAAuB,KAKpC,CAAC;AAGF,eAAO,MAAM,oBAAoB;qBACd,MAAM,WAAW,SAAS,GAAG,KAAG,eAAe;wBAI9C,kBAAkB;qBAGnB,MAAM,aAAa,KAAK,GAAG,MAAM,KAAG,aAAa;oBAIlD,MAAM,KAAG,aAAa;4BAId,MAAM,KAAG,iBAAiB;wBAI9B,iBAAiB,EAAE,KAAG,eAAe;4BAIjC,OAAO,KAAG,gBAAgB;sBAIhC,MAAM,GAAG,IAAI,KAAG,cAAc;CAIjD,CAAC;AAGF,eAAO,MAAM,YAAY,UAAW,kBAAkB,wBAAiB,CAAC;AACxE,eAAO,MAAM,YAAY,UAAW,kBAAkB,gBAAiB,CAAC;AACxE,eAAO,MAAM,UAAU,UAAW,kBAAkB,cAAe,CAAC;AACpE,eAAO,MAAM,UAAU,UAAW,kBAAkB,WAAe,CAAC;AACpE,eAAO,MAAM,cAAc,UAAW,kBAAkB,WAAmB,CAAC;AAC5E,eAAO,MAAM,eAAe,UAAW,kBAAkB,YAAoB,CAAC;AAC9E,eAAO,MAAM,WAAW,UAAW,kBAAkB,kBAAgB,CAAC;AAGtE,eAAO,MAAM,oBAAoB,KAuD/B,CAAC;AAEH,eAAO,MAAM,kBAAkB,KAwC9B,CAAC;AAEF,eAAO,MAAM,qBAAqB,KAQjC,CAAC;AAEF,eAAO,MAAM,gBAAgB,KAM5B,CAAC;AAEF,eAAO,MAAM,kBAAkB,KAqD7B,CAAC"}