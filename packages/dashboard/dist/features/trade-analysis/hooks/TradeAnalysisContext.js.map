{"version": 3, "file": "TradeAnalysisContext.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/TradeAnalysisContext.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EACZ,aAAa,EACb,UAAU,EACV,UAAU,EAEV,WAAW,EACX,SAAS,GACV,MAAM,OAAO,CAAC;AACf,OAAO,EAAE,eAAe,EAAE,MAAM,gCAAgC,CAAC;AAQjE,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE,yBAAyB;AACzB,MAAM,mBAAmB,GAAG,GAAG,EAAE;IAC/B,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IACzB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,wBAAwB;IAElE,OAAO;QACL,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChD,OAAO,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC;AACJ,CAAC,CAAC;AAEF,gBAAgB;AAChB,MAAM,YAAY,GAAuB;IACvC,IAAI,EAAE,IAAI;IACV,OAAO,EAAE;QACP,SAAS,EAAE,mBAAmB,EAAE;KACjC;IACD,WAAW,EAAE;QACX,gBAAgB,EAAE,OAAO;QACzB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE;YACV,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,KAAK;YACnB,YAAY,EAAE,KAAK;SACpB;QACD,YAAY,EAAE;YACZ,QAAQ;YACR,WAAW;YACX,WAAW;YACX,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,UAAU;SACX;QACD,kBAAkB,EAAE,EAAE;QACtB,YAAY,EAAE,EAAE;KACjB;IACD,SAAS,EAAE,KAAK;IAChB,KAAK,EAAE,IAAI;IACX,eAAe,EAAE,IAAI;CACtB,CAAC;AAEF,mBAAmB;AACnB,MAAM,oBAAoB,GAAG,CAC3B,KAAyB,EACzB,MAA2B,EACP,EAAE;IACtB,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,kBAAkB;YACrB,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,KAAK,oBAAoB;YACvB,OAAO;gBACL,GAAG,KAAK;gBACR,IAAI,EAAE,MAAM,CAAC,OAAO;gBACpB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,KAAK,kBAAkB;YACrB,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,MAAM,CAAC,OAAO;aACtB,CAAC;QACJ,KAAK,gBAAgB;YACnB,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,EAAE;oBACP,GAAG,KAAK,CAAC,OAAO;oBAChB,GAAG,MAAM,CAAC,OAAO;iBAClB;aACF,CAAC;QACJ,KAAK,oBAAoB;YACvB,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,EAAE;oBACX,GAAG,KAAK,CAAC,WAAW;oBACpB,GAAG,MAAM,CAAC,OAAO;iBAClB;aACF,CAAC;QACJ,KAAK,cAAc;YACjB,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,EAAE,MAAM,CAAC,OAAO;aAChC,CAAC;QACJ,KAAK,eAAe;YAClB,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,EAAE;oBACP,SAAS,EAAE,mBAAmB,EAAE;iBACjC;aACF,CAAC;QACJ;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC,CAAC;AAWF,MAAM,oBAAoB,GAAG,aAAa,CAAuC,SAAS,CAAC,CAAC;AAO5F,MAAM,CAAC,MAAM,qBAAqB,GAAyC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC1F,2CAA2C;IAC3C,MAAM,CAAC,gBAAgB,CAAC,GAAG,eAAe,CACxC,4BAA4B,EAC5B,EAAE,CACH,CAAC;IAEF,6CAA6C;IAC7C,MAAM,kBAAkB,GAAG;QACzB,GAAG,YAAY;QACf,WAAW,EAAE;YACX,GAAG,YAAY,CAAC,WAAW;YAC3B,GAAG,gBAAgB;SACpB;KACF,CAAC;IAEF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;IAE/E,oDAAoD;IACpD,SAAS,CAAC,GAAG,EAAE;QACb,YAAY,CAAC,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IACxF,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAExB,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACvC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;QACvC,IAAI;YACF,MAAM,IAAI,GAAG,MAAM,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SACzD;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC;gBACP,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B;aAC9E,CAAC,CAAC;SACJ;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IAEpB,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,OAA8B,EAAE,EAAE;QACnE,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACzD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAG,WAAW,CAAC,CAAC,WAAqC,EAAE,EAAE;QAC9E,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACjE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,OAAsB,EAAE,EAAE;QACzD,QAAQ,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACvD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,QAAQ,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IACtC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,iCAAiC;IACjC,SAAS,CAAC,GAAG,EAAE;QACb,SAAS,EAAE,CAAC;IACd,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IAE/B,MAAM,KAAK,GAAG;QACZ,GAAG,KAAK;QACR,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,WAAW;QACX,YAAY;KACb,CAAC;IAEF,OAAO,KAAC,oBAAoB,CAAC,QAAQ,IAAC,KAAK,EAAE,KAAK,YAAG,QAAQ,GAAiC,CAAC;AACjG,CAAC,CAAC;AAEF,oCAAoC;AACpC,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAA6B,EAAE;IAC7D,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,CAAC,CAAC;IACjD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;KACjF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC"}