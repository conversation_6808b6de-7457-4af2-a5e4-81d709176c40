/**
 * Trade Analysis Types
 *
 * Re-exports types from shared package and defines feature-specific types
 */
import { Trade, TradeFormData, PerformanceMetrics } from '@adhd-trading-dashboard/shared';
export { Trade, TradeFormData, PerformanceMetrics };
export interface EquityPoint {
    date: string;
    equity: number;
    baseline?: number;
}
export interface DistributionBar {
    range: string;
    count: number;
    isWin: boolean;
}
//# sourceMappingURL=index.d.ts.map