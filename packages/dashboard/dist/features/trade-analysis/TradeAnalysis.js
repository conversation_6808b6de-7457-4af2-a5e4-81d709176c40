import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trade Analysis Page
 *
 * This page displays trade analysis and performance metrics.
 */
import { useState } from 'react';
import styled from 'styled-components';
import { DataCard } from '@adhd-trading-dashboard/shared';
import { TradeAnalysisProvider, useTradeAnalysis } from './hooks/TradeAnalysisContext';
import { FilterPanel } from './components/FilterPanel';
import { PerformanceSummary } from './components/PerformanceSummary';
import { TradesTable } from './components/TradesTable';
import { CategoryPerformanceChart } from './components/CategoryPerformanceChart';
import { TimePerformanceChart } from './components/TimePerformanceChart';
import { TradeDetail } from './components/TradeDetail';
const PageContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;
const PageHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const ViewTabs = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: ${({ theme }) => theme.spacing.xs};
`;
const ViewTab = styled.button `
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: ${({ theme, active }) => active ? theme.fontWeights.semibold : theme.fontWeights.regular};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textSecondary)};
  cursor: pointer;
  border-bottom: 2px solid ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;
const TradeAnalysisContent = () => {
    const { data, isLoading, error, selectedTradeId, preferences, updatePreferences } = useTradeAnalysis();
    const [activeView, setActiveView] = useState(preferences.defaultView || 'summary');
    const handleViewChange = (view) => {
        setActiveView(view);
        updatePreferences({ defaultView: view });
    };
    const renderContent = () => {
        switch (activeView) {
            case 'summary':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance Summary", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.metrics, emptyMessage: "No performance data available for the selected filters.", children: _jsx(PerformanceSummary, {}) }), _jsx(DataCard, { title: "Performance by Time of Day", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0, emptyMessage: "No time of day performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "timeOfDay", title: "Time of Day" }) }), _jsx(DataCard, { title: "Performance by Day of Week", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0, emptyMessage: "No day of week performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "dayOfWeek", title: "Day of Week" }) })] }));
            case 'trades':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Trades", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.trades || data.trades.length === 0, emptyMessage: "No trades available for the selected filters.", children: _jsx(TradesTable, {}) }), selectedTradeId && _jsx(TradeDetail, {})] }));
            case 'symbols':
                return (_jsx(DataCard, { title: "Performance by Symbol", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.symbolPerformance || data.symbolPerformance.length === 0, emptyMessage: "No symbol performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "symbol", title: "Symbol" }) }));
            case 'strategies':
                return (_jsx(DataCard, { title: "Performance by Strategy", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.strategyPerformance || data.strategyPerformance.length === 0, emptyMessage: "No strategy performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "strategy", title: "Strategy" }) }));
            case 'timeframes':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance by Timeframe", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeframePerformance || data.timeframePerformance.length === 0, emptyMessage: "No timeframe performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "timeframe", title: "Timeframe" }) }), _jsx(DataCard, { title: "Performance by Session", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.sessionPerformance || data.sessionPerformance.length === 0, emptyMessage: "No session performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "session", title: "Session" }) })] }));
            case 'time':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance by Time of Day", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0, emptyMessage: "No time of day performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "timeOfDay", title: "Time of Day" }) }), _jsx(DataCard, { title: "Performance by Day of Week", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0, emptyMessage: "No day of week performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "dayOfWeek", title: "Day of Week" }) })] }));
            default:
                return null;
        }
    };
    return (_jsxs(PageContainer, { children: [_jsx(PageHeader, { children: _jsx(Title, { children: "Trade Analysis" }) }), _jsx(FilterPanel, {}), _jsxs(ViewTabs, { children: [_jsx(ViewTab, { active: activeView === 'summary', onClick: () => handleViewChange('summary'), children: "Summary" }), _jsx(ViewTab, { active: activeView === 'trades', onClick: () => handleViewChange('trades'), children: "Trades" }), _jsx(ViewTab, { active: activeView === 'symbols', onClick: () => handleViewChange('symbols'), children: "Symbols" }), _jsx(ViewTab, { active: activeView === 'strategies', onClick: () => handleViewChange('strategies'), children: "Strategies" }), _jsx(ViewTab, { active: activeView === 'timeframes', onClick: () => handleViewChange('timeframes'), children: "Timeframes" }), _jsx(ViewTab, { active: activeView === 'time', onClick: () => handleViewChange('time'), children: "Time Analysis" })] }), renderContent()] }));
};
const TradeAnalysis = () => {
    return (_jsx(TradeAnalysisProvider, { children: _jsx(TradeAnalysisContent, {}) }));
};
export default TradeAnalysis;
//# sourceMappingURL=TradeAnalysis.js.map