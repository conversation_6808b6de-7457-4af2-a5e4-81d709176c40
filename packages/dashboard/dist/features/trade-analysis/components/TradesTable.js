import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trades Table Component
 *
 * Displays a table of trades with sorting and filtering
 */
import { useState, useMemo } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { Badge, Tag } from '@adhd-trading-dashboard/shared';
const Container = styled.div `
  overflow-x: auto;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const TableHead = styled.thead `
  background-color: ${({ theme }) => theme.colors.background};
  position: sticky;
  top: 0;
  z-index: 1;
`;
const TableBody = styled.tbody ``;
const TableRow = styled.tr `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme, isSelected }) => isSelected ? `${theme.colors.primary}10` : 'transparent'};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const TableHeaderCell = styled.th `
  padding: ${({ theme }) => theme.spacing.sm};
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};

  &:hover {
    ${({ sortable, theme }) => sortable &&
    `
      color: ${theme.colors.primary};
    `}
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
  vertical-align: middle;
`;
const SortIcon = styled.span `
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : '↓')}';
  }
`;
const DirectionBadge = styled(Badge) `
  text-transform: capitalize;
`;
const StatusBadge = styled(Badge) `
  text-transform: capitalize;
`;
const TagsContainer = styled.div `
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const ProfitLoss = styled.span `
  color: ${({ theme, value }) => value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};
  font-weight: ${({ theme, value }) => value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const TradesTable = ({ className }) => {
    const { data, selectedTradeId, selectTrade } = useTradeAnalysis();
    const [sortField, setSortField] = useState('entryTime');
    const [sortDirection, setSortDirection] = useState('desc');
    const handleSort = (field) => {
        if (sortField === field) {
            // Toggle direction if same field
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        }
        else {
            // Set new field and default direction
            setSortField(field);
            setSortDirection('desc');
        }
    };
    const sortedTrades = useMemo(() => {
        if (!data?.trades)
            return [];
        return [...data.trades].sort((a, b) => {
            let comparison = 0;
            switch (sortField) {
                case 'entryTime':
                    comparison = new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime();
                    break;
                case 'symbol':
                    comparison = a.symbol.localeCompare(b.symbol);
                    break;
                case 'direction':
                    comparison = a.direction.localeCompare(b.direction);
                    break;
                case 'profitLoss':
                    comparison = a.profitLoss - b.profitLoss;
                    break;
                case 'profitLossPercent':
                    comparison = a.profitLossPercent - b.profitLossPercent;
                    break;
                case 'status':
                    comparison = a.status.localeCompare(b.status);
                    break;
                default:
                    comparison = 0;
            }
            return sortDirection === 'asc' ? comparison : -comparison;
        });
    }, [data?.trades, sortField, sortDirection]);
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return (date.toLocaleDateString() +
            ' ' +
            date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    };
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
    };
    const getDirectionVariant = (direction) => {
        return direction === 'long' ? 'success' : 'error';
    };
    const getStatusVariant = (status) => {
        switch (status) {
            case 'win':
                return 'success';
            case 'loss':
                return 'error';
            case 'breakeven':
                return 'info';
            default:
                return 'default';
        }
    };
    const handleRowClick = (tradeId) => {
        selectTrade(tradeId === selectedTradeId ? null : tradeId);
    };
    if (!data || !data.trades || data.trades.length === 0) {
        return _jsx(EmptyState, { children: "No trades found for the selected filters." });
    }
    return (_jsx(Container, { className: className, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsxs(TableHeaderCell, { sortable: true, active: sortField === 'entryTime', onClick: () => handleSort('entryTime'), children: ["Date/Time", sortField === 'entryTime' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'symbol', onClick: () => handleSort('symbol'), children: ["Symbol", sortField === 'symbol' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'direction', onClick: () => handleSort('direction'), children: ["Direction", sortField === 'direction' && _jsx(SortIcon, { direction: sortDirection })] }), _jsx(TableHeaderCell, { children: "Entry/Exit" }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'profitLoss', onClick: () => handleSort('profitLoss'), children: ["P&L", sortField === 'profitLoss' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'profitLossPercent', onClick: () => handleSort('profitLossPercent'), children: ["P&L %", sortField === 'profitLossPercent' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'status', onClick: () => handleSort('status'), children: ["Status", sortField === 'status' && _jsx(SortIcon, { direction: sortDirection })] }), _jsx(TableHeaderCell, { children: "Strategy" }), _jsx(TableHeaderCell, { children: "Tags" })] }) }), _jsx(TableBody, { children: sortedTrades.map((trade) => (_jsxs(TableRow, { isSelected: trade.id === selectedTradeId, onClick: () => handleRowClick(trade.id), children: [_jsx(TableCell, { children: formatDate(trade.entryTime) }), _jsx(TableCell, { children: trade.symbol }), _jsx(TableCell, { children: _jsx(DirectionBadge, { direction: trade.direction, variant: getDirectionVariant(trade.direction), size: "small", children: trade.direction }) }), _jsxs(TableCell, { children: [trade.entryPrice.toFixed(2), " \u2192 ", trade.exitPrice.toFixed(2)] }), _jsx(TableCell, { children: _jsx(ProfitLoss, { value: trade.profitLoss, children: formatCurrency(trade.profitLoss) }) }), _jsx(TableCell, { children: _jsx(ProfitLoss, { value: trade.profitLossPercent, children: formatPercent(trade.profitLossPercent) }) }), _jsx(TableCell, { children: _jsx(StatusBadge, { status: trade.status, variant: getStatusVariant(trade.status), size: "small", children: trade.status }) }), _jsx(TableCell, { children: trade.strategy }), _jsx(TableCell, { children: _jsx(TagsContainer, { children: trade.tags?.map((tag, index) => (_jsx(Tag, { size: "small", variant: "default", children: tag }, index))) }) })] }, trade.id))) })] }) }));
};
//# sourceMappingURL=TradesTable.js.map