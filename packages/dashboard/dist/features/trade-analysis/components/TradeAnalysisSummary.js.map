{"version": 3, "file": "TradeAnalysisSummary.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeAnalysisSummary.tsx"], "names": [], "mappings": ";AAMA,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAC;AAUtD;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAwC,CAAC,EACxE,OAAO,EACP,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,kBAAkB;IAClB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACvC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,oBAAoB;IACpB,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAAE,EAAE;QACzC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,SAAS;YAChB,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,gBAAgB;IAChB,MAAM,YAAY,GAAG,CAAC,KAAa,EAAE,EAAE;QACrC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,oBAAoB;IACpB,MAAM,eAAe,GAAwB;QAC3C,OAAO,EAAE,MAAM;QACf,YAAY,EAAE,KAAK;QACnB,eAAe,EAAE,qBAAqB;QACtC,OAAO,EAAE,MAAM;QACf,aAAa,EAAE,QAAQ;QACvB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;KACzB,CAAC;IAEF,qBAAqB;IACrB,MAAM,gBAAgB,GAAwB;QAC5C,QAAQ,EAAE,MAAM;QAChB,UAAU,EAAE,MAAM;QAClB,YAAY,EAAE,KAAK;KACpB,CAAC;IAEF,qBAAqB;IACrB,MAAM,gBAAgB,GAAwB;QAC5C,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE,MAAM;KACd,CAAC;IAEF,gBAAgB;IAChB,IAAI,SAAS,EAAE;QACb,OAAO,CACL,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,wCAE9C,GACD,CACR,CAAC;KACH;IAED,OAAO,CACL,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,eAAK,KAAK,EAAE;gBACV,OAAO,EAAE,MAAM;gBACf,mBAAmB,EAAE,uCAAuC;gBAC5D,GAAG,EAAE,MAAM;gBACX,OAAO,EAAE,MAAM;aAChB,aACC,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,OAAO,CAAC,WAAW,GAAO,EACzD,cAAK,KAAK,EAAE,gBAAgB,6BAAoB,IAC5C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAO,EACvE,cAAK,KAAK,EAAE,gBAAgB,yBAAgB,IACxC,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,GAAO,EACxE,cAAK,KAAK,EAAE,gBAAgB,8BAAqB,IAC7C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,GAAO,EACvE,cAAK,KAAK,EAAE,gBAAgB,2BAAkB,IAC1C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAO,EACxE,cAAK,KAAK,EAAE,gBAAgB,4BAAmB,IAC3C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,GAAO,EACzE,cAAK,KAAK,EAAE,gBAAgB,6BAAoB,IAC5C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,OAAO,CAAC,aAAa,GAAO,EAC3D,cAAK,KAAK,EAAE,gBAAgB,+BAAsB,IAC9C,EAEN,eAAK,KAAK,EAAE,eAAe,aACzB,cAAK,KAAK,EAAE,gBAAgB,YAAG,OAAO,CAAC,YAAY,GAAO,EAC1D,cAAK,KAAK,EAAE,gBAAgB,8BAAqB,IAC7C,IACF,GACD,CACR,CAAC;AACJ,CAAC,CAAC"}