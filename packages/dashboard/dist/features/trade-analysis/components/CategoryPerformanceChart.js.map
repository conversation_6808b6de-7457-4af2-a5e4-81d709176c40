{"version": 3, "file": "CategoryPerformanceChart.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/CategoryPerformanceChart.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACxC,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAGvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAWjE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;CAE3B,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;;;eAGX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;CAC/C,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA;sBACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;CAC3D,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA,EAAE,CAAC;AAEjC,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;6BACG,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;;wBAGvC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;;CAE7D,CAAC;AAEF,MAAM,eAAe,GAAG,MAAM,CAAC,EAAE,CAA0C;aAC9D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;iBAE3B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;WAC/C,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;YAChF,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;;;MAG1D,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,CACxB,QAAQ;IACR;eACS,KAAK,CAAC,MAAM,CAAC,OAAO;KAC9B;;CAEJ,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAA;aACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC3C,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAA8B;;iBAEzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;gBAGhC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;;CAEnE,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAA;;sBAET,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;mBACzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI;;gBAEzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAsC;;WAEjD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;sBACf,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,CAC1C,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;CACrD,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAmB;WACtC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAC5B,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;iBAC/E,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAClC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO;CACrE,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;aAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;WAEjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAA4C,CAAC,EAChF,SAAS,EACT,QAAQ,EACR,KAAK,GACN,EAAE,EAAE;IACH,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAgB,EAAE,CAAC;IACpC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAY,YAAY,CAAC,CAAC;IACpE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAgB,MAAM,CAAC,CAAC;IAE1E,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI,CAAC;KACb;IAED,IAAI,eAAe,GAA0B,EAAE,CAAC;IAEhD,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ;YACX,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACzC,MAAM;QACR,KAAK,UAAU;YACb,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC3C,MAAM;QACR,KAAK,WAAW;YACd,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5C,MAAM;QACR,KAAK,SAAS;YACZ,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAC1C,MAAM;KACT;IAED,IAAI,CAAC,eAAe,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;QACpD,OAAO,MAAC,UAAU,sBAAK,QAAQ,oCAA0C,CAAC;KAC3E;IAED,MAAM,UAAU,GAAG,CAAC,KAAgB,EAAE,EAAE;QACtC,IAAI,SAAS,KAAK,KAAK,EAAE;YACvB,iCAAiC;YACjC,gBAAgB,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC5D;aAAM;YACL,sCAAsC;YACtC,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;IAEF,YAAY;IACZ,MAAM,UAAU,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACpD,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,QAAQ,SAAS,EAAE;YACjB,KAAK,OAAO;gBACV,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,QAAQ;gBACX,UAAU,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;gBACjC,MAAM;YACR,KAAK,SAAS;gBACZ,UAAU,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;gBACnC,MAAM;YACR,KAAK,YAAY;gBACf,UAAU,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;gBACzC,MAAM;YACR,KAAK,mBAAmB;gBACtB,UAAU,GAAG,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,CAAC;gBACvD,MAAM;YACR;gBACE,UAAU,GAAG,CAAC,CAAC;SAClB;QAED,OAAO,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,uCAAuC;IACvC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAE5F,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;QAC9C,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,SAAS,IAAC,SAAS,EAAE,SAAS,YAC7B,MAAC,KAAK,eACJ,KAAC,SAAS,cACR,MAAC,QAAQ,eACP,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,OAAO,EAC7B,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,aAEjC,KAAK,EACL,SAAS,KAAK,OAAO,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IAChD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,QAAQ,EAC9B,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,uBAGlC,SAAS,KAAK,QAAQ,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACjD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,SAAS,EAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,yBAGnC,SAAS,KAAK,SAAS,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IAClD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,YAAY,EAClC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,oBAGtC,SAAS,KAAK,YAAY,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACrD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,mBAAmB,EACzC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,wBAG7C,SAAS,KAAK,mBAAmB,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IAC5D,IACT,GACD,EAEZ,KAAC,SAAS,cACP,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC/B,MAAC,QAAQ,eACP,KAAC,SAAS,cAAE,IAAI,CAAC,KAAK,GAAa,EACnC,KAAC,SAAS,cAAE,IAAI,CAAC,MAAM,GAAa,EACpC,KAAC,SAAS,cAAE,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,GAAa,EACpD,MAAC,SAAS,eACR,KAAC,UAAU,IAAC,KAAK,EAAE,IAAI,CAAC,UAAU,YAAG,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAc,EAClF,KAAC,YAAY,cACX,KAAC,GAAG,IACF,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,EACvE,QAAQ,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,GAC9B,GACW,IACL,EACZ,KAAC,SAAS,cACR,KAAC,UAAU,IAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,YACtC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAC5B,GACH,KAjBC,KAAK,CAkBT,CACZ,CAAC,GACQ,IACN,GACE,CACb,CAAC;AACJ,CAAC,CAAC"}