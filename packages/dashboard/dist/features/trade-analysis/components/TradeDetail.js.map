{"version": 3, "file": "TradeDetail.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeDetail.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAEvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,gCAAgC,CAAC;AAMlE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;gBACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;mBACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;CAClD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;WAC7C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;CACjD,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAmB;eACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;WAC/C,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAC5B,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBAC7E,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBACxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAA;gBACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;iBAC9B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;0BACtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;CAE3D,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;aAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;WAEjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;IACvE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAErD,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;QAC7B,OAAO,IAAI,CAAC;KACb;IAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,eAAe,CAAC,CAAC;IAEhF,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO,KAAC,UAAU,mCAA8B,CAAC;KAClD;IAED,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAU,EAAE;QAChD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,OAAO,CACL,IAAI,CAAC,kBAAkB,EAAE;YACzB,GAAG;YACH,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CACpE,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;QAC9C,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvD,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAU,EAAE;QACxD,OAAO,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAU,EAAE;QAClD,QAAQ,MAAM,EAAE;YACd,KAAK,KAAK;gBACR,OAAO,SAAS,CAAC;YACnB,KAAK,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,SAAS,CAAC;SACpB;IACH,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAU,EAAE;QACxE,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,GAAG,KAAK,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACvC,MAAM,gBAAgB,GAAG,OAAO,GAAG,EAAE,CAAC;QAEtC,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,OAAO,GAAG,KAAK,KAAK,gBAAgB,GAAG,CAAC;SACzC;QACD,OAAO,GAAG,OAAO,GAAG,CAAC;IACvB,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,SAAS,IACR,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,GAAG,aAAa,CAAC,MAAM,gBAAgB,EAC9C,OAAO,EAAC,SAAS,EACjB,OAAO,EAAC,QAAQ,aAEhB,MAAC,UAAU,IAAC,KAAK,EAAE,aAAa,CAAC,UAAU,aACxC,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,QAAI,aAAa,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAEhF,EAEb,MAAC,UAAU,eACT,MAAC,aAAa,eACZ,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,cACV,KAAC,KAAK,IAAC,OAAO,EAAE,mBAAmB,CAAC,aAAa,CAAC,SAAS,CAAQ,EAAE,IAAI,EAAC,OAAO,YAC9E,aAAa,CAAC,SAAS,GAClB,GACI,IACA,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,yBAAqB,EACjC,KAAC,WAAW,cACV,KAAC,KAAK,IAAC,OAAO,EAAE,gBAAgB,CAAC,aAAa,CAAC,MAAM,CAAQ,EAAE,IAAI,EAAC,OAAO,YACxE,aAAa,CAAC,MAAM,GACf,GACI,IACA,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,cAAE,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAe,IAClD,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,cAAE,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAe,IACjD,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,cACT,iBAAiB,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,QAAQ,CAAC,GACvD,IACA,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,8BAA0B,EACtC,KAAC,WAAW,cAAE,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAe,IAClD,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,cAAE,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAe,IACjD,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,cAAE,aAAa,CAAC,QAAQ,GAAe,IACrC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,cAAE,aAAa,CAAC,SAAS,GAAe,IACtC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,0BAAsB,EAClC,KAAC,WAAW,cAAE,aAAa,CAAC,OAAO,GAAe,IACpC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,cAAE,aAAa,CAAC,QAAQ,GAAe,IACrC,IACL,EAEZ,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CACtD,MAAC,aAAa,eACZ,KAAC,WAAW,uBAAmB,EAC/B,KAAC,aAAa,cACX,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CACtC,KAAC,GAAG,IAAa,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,YACzC,GAAG,IADI,KAAK,CAET,CACP,CAAC,GACY,IACF,CACjB,EAEA,aAAa,CAAC,KAAK,IAAI,CACtB,MAAC,KAAK,eACJ,KAAC,WAAW,wBAAoB,EAChC,KAAC,WAAW,cAAE,aAAa,CAAC,KAAK,GAAe,IAC1C,CACT,IACS,CACb,CAAC;AACJ,CAAC,CAAC"}