import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trade Analysis Filter Component
 *
 * A component for filtering trade analysis data.
 */
import { useState } from 'react';
import { Card, Input, Select, Button, FormField } from '@adhd-trading-dashboard/shared';
/**
 * Trade Analysis Filter Component
 *
 * A component for filtering trade analysis data.
 */
export const TradeAnalysisFilter = ({ filter, onSetFilter, onClearFilters, isLoading = false, }) => {
    // Local state for form values
    const [localFilter, setLocalFilter] = useState(filter);
    // Handle input change
    const handleInputChange = (key, value) => {
        setLocalFilter((prev) => ({
            ...prev,
            [key]: value,
        }));
    };
    // Handle form submit
    const handleSubmit = (e) => {
        e.preventDefault();
        // Apply all filters at once
        Object.entries(localFilter).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                onSetFilter(key, value);
            }
        });
    };
    // Handle clear filters
    const handleClear = () => {
        setLocalFilter({});
        onClearFilters();
    };
    return (_jsx(Card, { title: "Filter Trades", children: _jsxs("form", { onSubmit: handleSubmit, children: [_jsxs("div", { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }, children: [_jsx(FormField, { label: "Start Date", children: _jsx(Input, { type: "date", value: localFilter.startDate || '', onChange: (value) => handleInputChange('startDate', value), disabled: isLoading, fullWidth: true }) }), _jsx(FormField, { label: "End Date", children: _jsx(Input, { type: "date", value: localFilter.endDate || '', onChange: (value) => handleInputChange('endDate', value), disabled: isLoading, fullWidth: true }) }), _jsx(FormField, { label: "Symbol", children: _jsx(Input, { type: "text", value: localFilter.symbol || '', onChange: (value) => handleInputChange('symbol', value), placeholder: "Enter symbol", disabled: isLoading, fullWidth: true }) }), _jsx(FormField, { label: "Action", children: _jsx(Select, { options: [
                                    { value: '', label: 'All Actions' },
                                    { value: 'BUY', label: 'Buy' },
                                    { value: 'SELL', label: 'Sell' },
                                ], value: localFilter.action || '', onChange: (value) => handleInputChange('action', value), disabled: isLoading, fullWidth: true }) }), _jsx(FormField, { label: "Min Profit", children: _jsx(Input, { type: "number", value: localFilter.minProfit?.toString() || '', onChange: (value) => handleInputChange('minProfit', value ? Number(value) : undefined), placeholder: "Min profit", disabled: isLoading, fullWidth: true }) }), _jsx(FormField, { label: "Max Profit", children: _jsx(Input, { type: "number", value: localFilter.maxProfit?.toString() || '', onChange: (value) => handleInputChange('maxProfit', value ? Number(value) : undefined), placeholder: "Max profit", disabled: isLoading, fullWidth: true }) })] }), _jsxs("div", { style: {
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '16px',
                        marginTop: '24px'
                    }, children: [_jsx(Button, { variant: "outline", onClick: handleClear, disabled: isLoading, children: "Clear Filters" }), _jsx(Button, { type: "submit", disabled: isLoading, loading: isLoading, children: "Apply Filters" })] })] }) }));
};
//# sourceMappingURL=TradeAnalysisFilter.js.map