import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * Category Performance Chart Component
 *
 * Displays performance metrics by category (symbol, strategy, timeframe, session)
 */
import { useState } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
const Container = styled.div `
  overflow-x: auto;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const TableHead = styled.thead `
  background-color: ${({ theme }) => theme.colors.background};
`;
const TableBody = styled.tbody ``;
const TableRow = styled.tr `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;
const TableHeaderCell = styled.th `
  padding: ${({ theme }) => theme.spacing.sm};
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};

  &:hover {
    ${({ sortable, theme }) => sortable &&
    `
      color: ${theme.colors.primary};
    `}
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
`;
const SortIcon = styled.span `
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : '↓')}';
  }
`;
const BarContainer = styled.div `
  height: 8px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  overflow: hidden;
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
const Bar = styled.div `
  height: 100%;
  width: ${({ width }) => `${width}%`};
  background-color: ${({ theme, positive }) => positive ? theme.colors.profit : theme.colors.loss};
`;
const ProfitLoss = styled.span `
  color: ${({ theme, value }) => value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};
  font-weight: ${({ theme, value }) => value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const CategoryPerformanceChart = ({ className, category, title, }) => {
    const { data } = useTradeAnalysis();
    const [sortField, setSortField] = useState('profitLoss');
    const [sortDirection, setSortDirection] = useState('desc');
    if (!data) {
        return null;
    }
    let performanceData = [];
    switch (category) {
        case 'symbol':
            performanceData = data.symbolPerformance;
            break;
        case 'strategy':
            performanceData = data.strategyPerformance;
            break;
        case 'timeframe':
            performanceData = data.timeframePerformance;
            break;
        case 'session':
            performanceData = data.sessionPerformance;
            break;
    }
    if (!performanceData || performanceData.length === 0) {
        return _jsxs(EmptyState, { children: ["No ", category, " performance data available."] });
    }
    const handleSort = (field) => {
        if (sortField === field) {
            // Toggle direction if same field
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        }
        else {
            // Set new field and default direction
            setSortField(field);
            setSortDirection('desc');
        }
    };
    // Sort data
    const sortedData = [...performanceData].sort((a, b) => {
        let comparison = 0;
        switch (sortField) {
            case 'value':
                comparison = a.value.localeCompare(b.value);
                break;
            case 'trades':
                comparison = a.trades - b.trades;
                break;
            case 'winRate':
                comparison = a.winRate - b.winRate;
                break;
            case 'profitLoss':
                comparison = a.profitLoss - b.profitLoss;
                break;
            case 'averageProfitLoss':
                comparison = a.averageProfitLoss - b.averageProfitLoss;
                break;
            default:
                comparison = 0;
        }
        return sortDirection === 'asc' ? comparison : -comparison;
    });
    // Find max profit/loss for bar scaling
    const maxProfitLoss = Math.max(...performanceData.map((item) => Math.abs(item.profitLoss)));
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value.toFixed(2)}%`;
    };
    return (_jsx(Container, { className: className, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsxs(TableHeaderCell, { sortable: true, active: sortField === 'value', onClick: () => handleSort('value'), children: [title, sortField === 'value' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'trades', onClick: () => handleSort('trades'), children: ["Trades", sortField === 'trades' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'winRate', onClick: () => handleSort('winRate'), children: ["Win Rate", sortField === 'winRate' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'profitLoss', onClick: () => handleSort('profitLoss'), children: ["P&L", sortField === 'profitLoss' && _jsx(SortIcon, { direction: sortDirection })] }), _jsxs(TableHeaderCell, { sortable: true, active: sortField === 'averageProfitLoss', onClick: () => handleSort('averageProfitLoss'), children: ["Avg P&L", sortField === 'averageProfitLoss' && _jsx(SortIcon, { direction: sortDirection })] })] }) }), _jsx(TableBody, { children: sortedData.map((item, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: item.value }), _jsx(TableCell, { children: item.trades }), _jsx(TableCell, { children: formatPercent(item.winRate) }), _jsxs(TableCell, { children: [_jsx(ProfitLoss, { value: item.profitLoss, children: formatCurrency(item.profitLoss) }), _jsx(BarContainer, { children: _jsx(Bar, { width: Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100), positive: item.profitLoss >= 0 }) })] }), _jsx(TableCell, { children: _jsx(ProfitLoss, { value: item.averageProfitLoss, children: formatCurrency(item.averageProfitLoss) }) })] }, index))) })] }) }));
};
//# sourceMappingURL=CategoryPerformanceChart.js.map