/**
 * EquityCurve Component
 *
 * Displays a chart showing equity growth over time.
 */
import React from 'react';
export interface EquityPoint {
    /** The date of the equity point */
    date: string;
    /** The equity value */
    equity: number;
    /** The baseline value for comparison */
    baseline: number;
}
interface EquityCurveProps {
    data: EquityPoint[];
    isLoading: boolean;
}
/**
 * EquityCurve Component
 *
 * Visualizes account equity growth over time
 */
declare const EquityCurve: React.FC<EquityCurveProps>;
export default EquityCurve;
//# sourceMappingURL=EquityCurve.d.ts.map