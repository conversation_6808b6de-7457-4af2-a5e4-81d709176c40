import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { Badge, Card, Tag } from '@adhd-trading-dashboard/shared';
const Container = styled(Card) `
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const DetailGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const DetailSection = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const DetailLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
`;
const DetailValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const ProfitLoss = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, value }) => value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const TagsContainer = styled.div `
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
const Notes = styled.div `
  margin-top: ${({ theme }) => theme.spacing.md};
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  white-space: pre-wrap;
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const TradeDetail = ({ className }) => {
    const { data, selectedTradeId } = useTradeAnalysis();
    if (!data || !selectedTradeId) {
        return null;
    }
    const selectedTrade = data.trades.find((trade) => trade.id === selectedTradeId);
    if (!selectedTrade) {
        return _jsx(EmptyState, { children: "Trade not found." });
    }
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return (date.toLocaleDateString() +
            ' ' +
            date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    };
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
    };
    const getDirectionVariant = (direction) => {
        return direction === 'long' ? 'success' : 'error';
    };
    const getStatusVariant = (status) => {
        switch (status) {
            case 'win':
                return 'success';
            case 'loss':
                return 'error';
            case 'breakeven':
                return 'info';
            default:
                return 'default';
        }
    };
    const calculateDuration = (entryTime, exitTime) => {
        const entry = new Date(entryTime).getTime();
        const exit = new Date(exitTime).getTime();
        const durationMs = exit - entry;
        const minutes = Math.floor(durationMs / (1000 * 60));
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${remainingMinutes}m`;
        }
        return `${minutes}m`;
    };
    return (_jsxs(Container, { className: className, title: `${selectedTrade.symbol} Trade Details`, variant: "default", padding: "medium", children: [_jsxs(ProfitLoss, { value: selectedTrade.profitLoss, children: [formatCurrency(selectedTrade.profitLoss), " (", formatPercent(selectedTrade.profitLossPercent), ")"] }), _jsxs(DetailGrid, { children: [_jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Direction" }), _jsx(DetailValue, { children: _jsx(Badge, { variant: getDirectionVariant(selectedTrade.direction), size: "small", children: selectedTrade.direction }) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Status" }), _jsx(DetailValue, { children: _jsx(Badge, { variant: getStatusVariant(selectedTrade.status), size: "small", children: selectedTrade.status }) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Entry Time" }), _jsx(DetailValue, { children: formatDate(selectedTrade.entryTime) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Exit Time" }), _jsx(DetailValue, { children: formatDate(selectedTrade.exitTime) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Duration" }), _jsx(DetailValue, { children: calculateDuration(selectedTrade.entryTime, selectedTrade.exitTime) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Entry Price" }), _jsx(DetailValue, { children: selectedTrade.entryPrice.toFixed(2) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Exit Price" }), _jsx(DetailValue, { children: selectedTrade.exitPrice.toFixed(2) })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Quantity" }), _jsx(DetailValue, { children: selectedTrade.quantity })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Timeframe" }), _jsx(DetailValue, { children: selectedTrade.timeframe })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Session" }), _jsx(DetailValue, { children: selectedTrade.session })] }), _jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Strategy" }), _jsx(DetailValue, { children: selectedTrade.strategy })] })] }), selectedTrade.tags && selectedTrade.tags.length > 0 && (_jsxs(DetailSection, { children: [_jsx(DetailLabel, { children: "Tags" }), _jsx(TagsContainer, { children: selectedTrade.tags.map((tag, index) => (_jsx(Tag, { variant: "info", size: "small", children: tag }, index))) })] })), selectedTrade.notes && (_jsxs(Notes, { children: [_jsx(DetailLabel, { children: "Notes" }), _jsx(DetailValue, { children: selectedTrade.notes })] }))] }));
};
//# sourceMappingURL=TradeDetail.js.map