{"version": 3, "file": "EquityCurve.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/EquityCurve.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAkBvC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;mBAKd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;CACtD,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAA;;;;CAI1B,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAA;;YAElB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;CAE9C,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAA;;YAEpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;;;CAIpD,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAA;YACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;CAE7C,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAA;YACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;CAE7C,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAA;;UAEpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CAClD,CAAC;AAEF,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAK1B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;sBAC9B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS;CAC1D,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,GAA+B,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;IACtE,IAAI,SAAS,EAAE;QACb,OAAO,KAAC,kBAAkB,+CAAkD,CAAC;KAC9E;IAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAO,KAAC,kBAAkB,2CAA8C,CAAC;KAC1E;IAED,+BAA+B;IAC/B,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAC5D,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;IAC/C,MAAM,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAEhD,qCAAqC;IACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;IAE/D,+BAA+B;IAC/B,MAAM,UAAU,GAAG,CAAC,UAAyB,EAAE,QAA+B,EAAE,EAAE;QAChF,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9C,OAAO,UAAU;aACd,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,4CAA4C;YAC5C,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAE,CAAC,CAAC,QAAQ,CAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;YAC3F,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,sBAAsB;IACtB,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAE1E,6CAA6C;IAC7C,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAC1D,OAAO,IAAI;aACR,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;aACzD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE;YACtB,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,CACL,YAAqB,SAAS,EAAE,aAAa,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG,YAC/D,KAAC,UAAU,IAAC,UAAU,EAAC,QAAQ,YAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAc,IAD3D,QAAQ,CAAC,EAAE,CAEf,CACL,CAAC;QACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,0BAA0B;IAC1B,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,EAAE;QACrC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;IACpD,CAAC,CAAC;IAEF,kBAAkB;IAClB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACvC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,kDAAkD;IAClD,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,MAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,KAAK,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;YACzE,OAAO,CACL,aAAsB,SAAS,EAAE,gBAAgB,CAAC,GAAG,aACnD,KAAC,KAAK,IAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAI,EACtC,KAAC,UAAU,IAAC,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,GAAG,EAAC,UAAU,EAAC,KAAK,YACvC,cAAc,CAAC,KAAK,CAAC,GACX,KAJP,SAAS,CAAC,EAAE,CAKhB,CACL,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,cAAc,cACb,KAAC,QAAQ,IACP,OAAO,EAAE,OAAO,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,YAE3F,aAAG,SAAS,EAAE,aAAa,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,GAAG,aAEtD,KAAC,KAAK,IAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,GAAI,EACzC,WAAW,EAAE,EAGd,KAAC,KAAK,IAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,GAAI,EAClD,UAAU,EAAE,EAGZ,YAAY,IAAI,KAAC,YAAY,IAAC,CAAC,EAAE,YAAY,GAAI,EAGlD,KAAC,UAAU,IAAC,CAAC,EAAE,UAAU,GAAI,IAC3B,GACK,GACI,CAClB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,WAAW,CAAC"}