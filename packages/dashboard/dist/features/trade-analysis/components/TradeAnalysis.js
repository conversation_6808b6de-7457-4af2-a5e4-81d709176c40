import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trade Analysis Component
 *
 * The main component for the trade analysis feature.
 */
import { useEffect } from 'react';
import { Button } from '@adhd-trading-dashboard/shared';
import { TradeAnalysisProvider } from '../hooks/tradeAnalysisState';
import { useTradeAnalysis } from '../hooks/useTradeAnalysis';
import { TradeAnalysisFilter } from './TradeAnalysisFilter';
import { TradeAnalysisTable } from './TradeAnalysisTable';
import { TradeAnalysisSummary } from './TradeAnalysisSummary';
import { TradeAnalysisCharts } from './TradeAnalysisCharts';
/**
 * Trade Analysis Component (Connected)
 *
 * The connected component that uses the trade analysis hook.
 */
const TradeAnalysisContent = () => {
    const { trades, filter, sort, page, pageSize, totalPages, tradeSummary, equityCurveData, distributionData, isLoading, error, setFilter, clearFilters, setSort, setPage, setPageSize, fetchTrades, exportTrades, } = useTradeAnalysis();
    // Fetch trades on mount
    useEffect(() => {
        fetchTrades();
    }, [fetchTrades]);
    return (_jsxs("div", { style: { display: 'flex', flexDirection: 'column', gap: '24px' }, children: [_jsxs("div", { style: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }, children: [_jsx("h1", { style: { margin: 0 }, children: "Trade Analysis" }), _jsxs("div", { style: { display: 'flex', gap: '16px' }, children: [_jsx(Button, { variant: "outline", onClick: fetchTrades, disabled: isLoading, startIcon: _jsx("span", { children: "\uD83D\uDD04" }), children: "Refresh" }), _jsx(Button, { onClick: exportTrades, disabled: isLoading || trades.length === 0, startIcon: _jsx("span", { children: "\uD83D\uDCCA" }), children: "Export" })] })] }), error && (_jsxs("div", { style: {
                    padding: '16px',
                    backgroundColor: '#ffebee',
                    color: '#c62828',
                    borderRadius: '4px',
                }, children: [_jsx("strong", { children: "Error:" }), " ", error] })), _jsx(TradeAnalysisFilter, { filter: filter, onSetFilter: setFilter, onClearFilters: clearFilters, isLoading: isLoading }), tradeSummary && _jsx(TradeAnalysisSummary, { summary: tradeSummary, isLoading: isLoading }), _jsx(TradeAnalysisCharts, { equityCurveData: equityCurveData, distributionData: distributionData, isLoading: isLoading }), _jsx(TradeAnalysisTable, { trades: trades, sort: sort, onSort: setSort, page: page, onPageChange: setPage, pageSize: pageSize, onPageSizeChange: setPageSize, totalPages: totalPages, isLoading: isLoading })] }));
};
/**
 * Trade Analysis Component
 *
 * The main component for the trade analysis feature.
 */
export const TradeAnalysis = ({ title }) => {
    return (_jsx(TradeAnalysisProvider, { children: _jsx(TradeAnalysisContent, {}) }));
};
//# sourceMappingURL=TradeAnalysis.js.map