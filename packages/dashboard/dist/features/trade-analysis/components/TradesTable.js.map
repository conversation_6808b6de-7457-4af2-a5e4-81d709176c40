{"version": 3, "file": "TradesTable.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradesTable.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACjD,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAEvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,gCAAgC,CAAC;AAe5D,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;CAE3B,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;;;eAGX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;CAC/C,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA;sBACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;;;;CAI3D,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA,EAAE,CAAC;AAEjC,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAA0B;6BACvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;sBACzC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,CAC5C,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,aAAa;;;wBAGpC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;;CAE7D,CAAC;AAEF,MAAM,eAAe,GAAG,MAAM,CAAC,EAAE,CAA0C;aAC9D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;iBAE3B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;WAC/C,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;YAChF,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;;;MAG1D,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,CACxB,QAAQ;IACR;eACS,KAAK,CAAC,MAAM,CAAC,OAAO;KAC9B;;CAEJ,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAA;aACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;CAE3C,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAA8B;;iBAEzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;gBAGhC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;;CAEnE,CAAC;AAEF,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC,CAA+B;;CAElE,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAyB;;CAEzD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAmB;WACtC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAC5B,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;iBAC/E,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAClC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO;CACrE,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;aAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;WAEjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;IACvE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAClE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAY,WAAW,CAAC,CAAC;IACnE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAgB,MAAM,CAAC,CAAC;IAE1E,MAAM,UAAU,GAAG,CAAC,KAAgB,EAAE,EAAE;QACtC,IAAI,SAAS,KAAK,KAAK,EAAE;YACvB,iCAAiC;YACjC,gBAAgB,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC5D;aAAM;YACL,sCAAsC;YACtC,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;QAChC,IAAI,CAAC,IAAI,EAAE,MAAM;YAAE,OAAO,EAAE,CAAC;QAE7B,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,QAAQ,SAAS,EAAE;gBACjB,KAAK,WAAW;oBACd,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;oBAC/E,MAAM;gBACR,KAAK,QAAQ;oBACX,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,WAAW;oBACd,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBACpD,MAAM;gBACR,KAAK,YAAY;oBACf,UAAU,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;oBACzC,MAAM;gBACR,KAAK,mBAAmB;oBACtB,UAAU,GAAG,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,CAAC;oBACvD,MAAM;gBACR,KAAK,QAAQ;oBACX,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBACR;oBACE,UAAU,GAAG,CAAC,CAAC;aAClB;YAED,OAAO,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;IAE7C,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAU,EAAE;QAChD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,OAAO,CACL,IAAI,CAAC,kBAAkB,EAAE;YACzB,GAAG;YACH,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CACpE,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;QAC9C,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvD,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAAC,SAAyB,EAAU,EAAE;QAChE,OAAO,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,MAAmB,EAAU,EAAE;QACvD,QAAQ,MAAM,EAAE;YACd,KAAK,KAAK;gBACR,OAAO,SAAS,CAAC;YACnB,KAAK,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,SAAS,CAAC;SACpB;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,OAAe,EAAE,EAAE;QACzC,WAAW,CAAC,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACrD,OAAO,KAAC,UAAU,4DAAuD,CAAC;KAC3E;IAED,OAAO,CACL,KAAC,SAAS,IAAC,SAAS,EAAE,SAAS,YAC7B,MAAC,KAAK,eACJ,KAAC,SAAS,cACR,MAAC,QAAQ,eACP,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,WAAW,EACjC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,0BAGrC,SAAS,KAAK,WAAW,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACpD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,QAAQ,EAC9B,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,uBAGlC,SAAS,KAAK,QAAQ,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACjD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,WAAW,EACjC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,0BAGrC,SAAS,KAAK,WAAW,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACpD,EAElB,KAAC,eAAe,6BAA6B,EAE7C,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,YAAY,EAClC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,oBAGtC,SAAS,KAAK,YAAY,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACrD,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,mBAAmB,EACzC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,sBAExC,SAAS,KAAK,mBAAmB,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACjE,EAElB,MAAC,eAAe,IACd,QAAQ,QACR,MAAM,EAAE,SAAS,KAAK,QAAQ,EAC9B,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,uBAGlC,SAAS,KAAK,QAAQ,IAAI,KAAC,QAAQ,IAAC,SAAS,EAAE,aAAa,GAAI,IACjD,EAElB,KAAC,eAAe,2BAA2B,EAE3C,KAAC,eAAe,uBAAuB,IAC9B,GACD,EAEZ,KAAC,SAAS,cACP,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAC3B,MAAC,QAAQ,IAEP,UAAU,EAAE,KAAK,CAAC,EAAE,KAAK,eAAe,EACxC,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,aAEvC,KAAC,SAAS,cAAE,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,GAAa,EACpD,KAAC,SAAS,cAAE,KAAK,CAAC,MAAM,GAAa,EACrC,KAAC,SAAS,cACR,KAAC,cAAc,IACb,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,OAAO,EAAE,mBAAmB,CAAC,KAAK,CAAC,SAAS,CAAQ,EACpD,IAAI,EAAC,OAAO,YAEX,KAAK,CAAC,SAAS,GACD,GACP,EACZ,MAAC,SAAS,eACP,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAK,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IACjD,EACZ,KAAC,SAAS,cACR,KAAC,UAAU,IAAC,KAAK,EAAE,KAAK,CAAC,UAAU,YAAG,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,GAAc,GAC1E,EACZ,KAAC,SAAS,cACR,KAAC,UAAU,IAAC,KAAK,EAAE,KAAK,CAAC,iBAAiB,YACvC,aAAa,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAC5B,GACH,EACZ,KAAC,SAAS,cACR,KAAC,WAAW,IACV,MAAM,EAAE,KAAK,CAAC,MAAM,EACpB,OAAO,EAAE,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAQ,EAC9C,IAAI,EAAC,OAAO,YAEX,KAAK,CAAC,MAAM,GACD,GACJ,EACZ,KAAC,SAAS,cAAE,KAAK,CAAC,QAAQ,GAAa,EACvC,KAAC,SAAS,cACR,KAAC,aAAa,cACX,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAC/B,KAAC,GAAG,IAAa,IAAI,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,YAC5C,GAAG,IADI,KAAK,CAET,CACP,CAAC,GACY,GACN,KA5CP,KAAK,CAAC,EAAE,CA6CJ,CACZ,CAAC,GACQ,IACN,GACE,CACb,CAAC;AACJ,CAAC,CAAC"}