{"version": 3, "file": "TradeAnalysisCharts.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeAnalysisCharts.tsx"], "names": [], "mappings": ";AAMA,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAC;AAYtD;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAuC,CAAC,EACtE,eAAe,EACf,gBAAgB,EAChB,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,kBAAkB;IAClB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACvC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,gBAAgB;IAChB,IAAI,SAAS,EAAE;QACb,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,6CAE9C,GACD,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,6CAE9C,GACD,IACH,CACP,CAAC;KACH;IAED,cAAc;IACd,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;QACjE,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,4EAE9C,GACD,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,4EAE9C,GACD,IACH,CACP,CAAC;KACH;IAED,0CAA0C;IAC1C,MAAM,gBAAgB,GAAG,GAAG,CAAC;IAC7B,MAAM,iBAAiB,GAAG,GAAG,CAAC;IAC9B,MAAM,kBAAkB,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACxE,MAAM,qBAAqB,GAAG,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC;IACpG,MAAM,sBAAsB,GAAG,iBAAiB,GAAG,kBAAkB,CAAC,GAAG,GAAG,kBAAkB,CAAC,MAAM,CAAC;IAEtG,sCAAsC;IACtC,MAAM,WAAW,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACzE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAEzE,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACxD,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,cAAc,CAAC,CAAC;IAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,cAAc,CAAC,CAAC;IAE/D,qCAAqC;IACrC,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,EAAE;QAC5B,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,SAAS,CAAC;QACpE,OAAO,kBAAkB,CAAC,IAAI,GAAG,UAAU,GAAG,qBAAqB,CAAC;IACtE,CAAC,CAAC;IAEF,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;QAC/B,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;QACzC,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC;QACpD,OAAO,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,UAAU,GAAG,sBAAsB,CAAC;IAC/F,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC9C,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAClD,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;IAEhB,MAAM,YAAY,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAChD,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CACpD,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;IAEhB,0CAA0C;IAC1C,MAAM,cAAc,GAAG,GAAG,CAAC;IAC3B,MAAM,eAAe,GAAG,GAAG,CAAC;IAC5B,MAAM,gBAAgB,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACtE,MAAM,mBAAmB,GAAG,cAAc,GAAG,gBAAgB,CAAC,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC;IAC5F,MAAM,oBAAoB,GAAG,eAAe,GAAG,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAE9F,sCAAsC;IACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,MAAM,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAE/D,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,YAC7B,eAAK,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,aAErD,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,EAAE,EAAE,kBAAkB,CAAC,IAAI,GAAG,qBAAqB,EACnD,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,MAAM,EAAC,MAAM,GACb,EAGF,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,EAC1B,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,MAAM,EAAC,MAAM,GACb,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gCACxC,MAAM,KAAK,GAAG,SAAS,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;gCAC/D,OAAO,CACL,wBACE,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAC/B,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EACjB,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EACjB,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,kBAAkB,CAAC,IAAI,GAAG,EAAE,EAC/B,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAChB,UAAU,EAAC,KAAK,EAChB,gBAAgB,EAAC,QAAQ,EACzB,QAAQ,EAAC,IAAI,YAEZ,cAAc,CAAC,KAAK,CAAC,GACjB,KAhBD,UAAU,CAiBd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gCACxC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gCAChG,OAAO,CACL,wBACE,eACE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAChB,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAChB,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,CAAC,EACvD,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EACf,CAAC,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,EAAE,EACvD,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,YAEZ,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,GAClE,KAfD,UAAU,CAgBd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGF,eAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,WAAW,EAAC,GAAG,EAAC,eAAe,EAAC,KAAK,GAAG,EAGzF,eAAM,CAAC,EAAE,UAAU,EAAE,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,GAAG,GAAG,EAGpE,aAAG,SAAS,EAAE,aAAa,kBAAkB,CAAC,IAAI,GAAG,EAAE,KAAK,kBAAkB,CAAC,GAAG,GAAG,EAAE,GAAG,aACxF,eAAM,EAAE,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,GAAG,EAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,GAAG,GAAG,EACtE,eAAM,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,GAAG,EAAC,gBAAgB,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,uBAAc,EAExE,eAAM,EAAE,EAAC,GAAG,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,WAAW,EAAC,GAAG,EAAC,eAAe,EAAC,KAAK,GAAG,EAC3F,eAAM,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,gBAAgB,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,yBAAgB,IACzE,IACA,GACF,GACD,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,YAC7B,eAAK,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,aAEjD,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,EAAE,EAAE,gBAAgB,CAAC,IAAI,GAAG,mBAAmB,EAC/C,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,MAAM,EAAC,MAAM,GACb,EAGF,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,EACxB,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,MAAM,EAAC,MAAM,GACb,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gCACxC,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,CAAC;gCACpC,OAAO,CACL,wBACE,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,EAC7B,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,UAAU,GAAG,oBAAoB,EACnF,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,UAAU,GAAG,oBAAoB,EACnF,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,EAAE,EAC7B,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,UAAU,GAAG,oBAAoB,EAClF,UAAU,EAAC,KAAK,EAChB,gBAAgB,EAAC,QAAQ,EACzB,QAAQ,EAAC,IAAI,YAEZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GACb,KAhBD,UAAU,CAiBd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGD,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gCAC7B,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,oBAAoB,CAAC;gCAC9D,OAAO,CACL,wBACE,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,EACvC,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,SAAS,EAC1D,KAAK,EAAE,QAAQ,GAAG,CAAC,EACnB,MAAM,EAAE,SAAS,EACjB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EACrC,OAAO,EAAE,GAAG,GACZ,EAEF,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,EACtD,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,EAAE,EACnD,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,EACb,SAAS,EAAE,cAAc,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,KAAK,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,EAAE,GAAG,YAEnI,CAAC,CAAC,KAAK,GACH,EAEP,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,EACtD,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,EAC9D,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,EACb,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,YAEpC,CAAC,CAAC,KAAK,GACH,KA5BD,CAAC,CA6BL,CACL,CAAC;4BACJ,CAAC,CAAC,IACE,GACF,GACD,IACH,CACP,CAAC;AACJ,CAAC,CAAC"}