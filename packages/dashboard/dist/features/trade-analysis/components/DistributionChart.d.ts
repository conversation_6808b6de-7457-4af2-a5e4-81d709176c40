/**
 * DistributionChart Component
 *
 * Displays a bar chart showing the distribution of trade outcomes.
 */
import React from 'react';
export interface DistributionBar {
    /** The range label */
    range: string;
    /** The count of trades in the range */
    count: number;
    /** Whether the range represents winning trades */
    isWin: boolean;
}
interface DistributionChartProps {
    data: DistributionBar[];
    isLoading: boolean;
}
/**
 * DistributionChart Component
 *
 * Visualizes the distribution of winning and losing trades by range
 */
declare const DistributionChart: React.FC<DistributionChartProps>;
export default DistributionChart;
//# sourceMappingURL=DistributionChart.d.ts.map