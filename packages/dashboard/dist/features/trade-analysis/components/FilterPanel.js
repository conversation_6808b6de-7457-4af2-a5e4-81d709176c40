import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Filter Panel Component
 *
 * Provides filtering options for trade analysis
 */
import { useState } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { <PERSON><PERSON>, Card, Tag } from '@adhd-trading-dashboard/shared';
const Container = styled(Card) `
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const FilterGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FilterSection = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const FilterLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const DateRangeContainer = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const DateInput = styled.input `
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  flex: 1;
`;
const TagsContainer = styled.div `
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
const FilterTag = styled(Tag) `
  cursor: pointer;
  opacity: ${({ selected }) => (selected ? 1 : 0.6)};

  &:hover {
    opacity: 0.8;
  }
`;
const ButtonContainer = styled.div `
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
export const FilterPanel = ({ className }) => {
    const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();
    // Local state for filter values
    const [localFilters, setLocalFilters] = useState(filters);
    // Available options from data
    const availableSymbols = data?.trades
        ? [...new Set(data.trades.map((trade) => trade.symbol))]
        : [];
    const availableStrategies = data?.trades
        ? [...new Set(data.trades.map((trade) => trade.strategy))]
        : [];
    const availableTags = data?.trades
        ? [...new Set(data.trades.flatMap((trade) => trade.tags || []))]
        : [];
    // Direction options
    const directionOptions = ['long', 'short'];
    // Status options
    const statusOptions = ['win', 'loss', 'breakeven'];
    // Timeframe options
    const timeframeOptions = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];
    // Session options
    const sessionOptions = ['pre-market', 'regular', 'after-hours'];
    // Handle date range change
    const handleDateChange = (field, value) => {
        setLocalFilters((prev) => ({
            ...prev,
            dateRange: {
                ...prev.dateRange,
                [field]: value,
            },
        }));
    };
    // Handle array filter toggle
    const handleToggleFilter = (field, value) => {
        setLocalFilters((prev) => {
            const currentValues = prev[field] || [];
            const newValues = currentValues.includes(value)
                ? currentValues.filter((v) => v !== value)
                : [...currentValues, value];
            return {
                ...prev,
                [field]: newValues.length > 0 ? newValues : undefined,
            };
        });
    };
    // Apply filters
    const applyFilters = () => {
        updateFilters(localFilters);
    };
    // Reset filters
    const handleResetFilters = () => {
        resetFilters();
        setLocalFilters(filters);
    };
    // Check if a filter value is selected
    const isSelected = (field, value) => {
        const values = localFilters[field];
        return values ? values.includes(value) : false;
    };
    return (_jsxs(Container, { className: className, title: "Filters", variant: "default", padding: "medium", children: [_jsxs(FilterGrid, { children: [_jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Date Range" }), _jsxs(DateRangeContainer, { children: [_jsx(DateInput, { type: "date", value: localFilters.dateRange.startDate, onChange: (e) => handleDateChange('startDate', e.target.value) }), _jsx(DateInput, { type: "date", value: localFilters.dateRange.endDate, onChange: (e) => handleDateChange('endDate', e.target.value) })] })] }), _jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Direction" }), _jsx(TagsContainer, { children: directionOptions.map((direction) => (_jsx(FilterTag, { variant: direction === 'long' ? 'success' : 'error', selected: isSelected('directions', direction), onClick: () => handleToggleFilter('directions', direction), children: direction }, direction))) })] }), _jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Status" }), _jsx(TagsContainer, { children: statusOptions.map((status) => (_jsx(FilterTag, { variant: status === 'win' ? 'success' : status === 'loss' ? 'error' : 'info', selected: isSelected('statuses', status), onClick: () => handleToggleFilter('statuses', status), children: status }, status))) })] }), availableSymbols.length > 0 && (_jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Symbols" }), _jsx(TagsContainer, { children: availableSymbols.map((symbol) => (_jsx(FilterTag, { variant: "primary", selected: isSelected('symbols', symbol), onClick: () => handleToggleFilter('symbols', symbol), children: symbol }, symbol))) })] })), availableStrategies.length > 0 && (_jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Strategies" }), _jsx(TagsContainer, { children: availableStrategies.map((strategy) => (_jsx(FilterTag, { variant: "secondary", selected: isSelected('strategies', strategy), onClick: () => handleToggleFilter('strategies', strategy), children: strategy }, strategy))) })] })), _jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Timeframe" }), _jsx(TagsContainer, { children: timeframeOptions.map((timeframe) => (_jsx(FilterTag, { variant: "default", selected: isSelected('timeframes', timeframe), onClick: () => handleToggleFilter('timeframes', timeframe), children: timeframe }, timeframe))) })] }), _jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Session" }), _jsx(TagsContainer, { children: sessionOptions.map((session) => (_jsx(FilterTag, { variant: "default", selected: isSelected('sessions', session), onClick: () => handleToggleFilter('sessions', session), children: session }, session))) })] }), availableTags.length > 0 && (_jsxs(FilterSection, { children: [_jsx(FilterLabel, { children: "Tags" }), _jsx(TagsContainer, { children: availableTags.map((tag) => (_jsx(FilterTag, { variant: "info", selected: isSelected('tags', tag), onClick: () => handleToggleFilter('tags', tag), children: tag }, tag))) })] }))] }), _jsxs(ButtonContainer, { children: [_jsx(Button, { variant: "outline", onClick: handleResetFilters, children: "Reset" }), _jsx(Button, { onClick: applyFilters, children: "Apply Filters" })] })] }));
};
//# sourceMappingURL=FilterPanel.js.map