{"version": 3, "file": "TradeAnalysis.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeAnalysis.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AACH,OAAc,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,MAAM,gCAAgC,CAAC;AACxD,OAAO,EAAE,qBAAqB,EAAE,MAAM,6BAA6B,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAO5D;;;;GAIG;AACH,MAAM,oBAAoB,GAAa,GAAG,EAAE;IAC1C,MAAM,EACJ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,YAAY,EACZ,OAAO,EACP,OAAO,EACP,WAAW,EACX,WAAW,EACX,YAAY,GACb,GAAG,gBAAgB,EAAE,CAAC;IAEvB,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,aAEnE,eACE,KAAK,EAAE;oBACL,OAAO,EAAE,MAAM;oBACf,cAAc,EAAE,eAAe;oBAC/B,UAAU,EAAE,QAAQ;iBACrB,aAED,aAAI,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,+BAAqB,EAE7C,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1C,KAAC,MAAM,IACL,OAAO,EAAC,SAAS,EACjB,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,SAAS,EACnB,SAAS,EAAE,0CAAe,wBAGnB,EAET,KAAC,MAAM,IACL,OAAO,EAAE,YAAY,EACrB,QAAQ,EAAE,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAC1C,SAAS,EAAE,0CAAe,uBAGnB,IACL,IACF,EAGL,KAAK,IAAI,CACR,eACE,KAAK,EAAE;oBACL,OAAO,EAAE,MAAM;oBACf,eAAe,EAAE,SAAS;oBAC1B,KAAK,EAAE,SAAS;oBAChB,YAAY,EAAE,KAAK;iBACpB,aAED,sCAAuB,OAAE,KAAK,IAC1B,CACP,EAGD,KAAC,mBAAmB,IAClB,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,SAAS,EACtB,cAAc,EAAE,YAAY,EAC5B,SAAS,EAAE,SAAS,GACpB,EAGD,YAAY,IAAI,KAAC,oBAAoB,IAAC,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,GAAI,EAGtF,KAAC,mBAAmB,IAClB,eAAe,EAAE,eAAe,EAChC,gBAAgB,EAAE,gBAAgB,EAClC,SAAS,EAAE,SAAS,GACpB,EAGF,KAAC,kBAAkB,IACjB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,OAAO,EACf,IAAI,EAAE,IAAI,EACV,YAAY,EAAE,OAAO,EACrB,QAAQ,EAAE,QAAQ,EAClB,gBAAgB,EAAE,WAAW,EAC7B,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,SAAS,GACpB,IACE,CACP,CAAC;AACJ,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;IACvE,OAAO,CACL,KAAC,qBAAqB,cACpB,KAAC,oBAAoB,KAAG,GACF,CACzB,CAAC;AACJ,CAAC,CAAC"}