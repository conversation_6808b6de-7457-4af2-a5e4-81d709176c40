import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const ChartContainer = styled.div `
  width: 100%;
  height: 300px;
  position: relative;
  margin-top: 20px;
  margin-bottom: 40px;
`;
const ChartSvg = styled.svg `
  width: 100%;
  height: 100%;
  overflow: visible;
`;
const Bar = styled.rect `
  fill: ${({ theme, isWin }) => (isWin ? theme.colors.success : theme.colors.error)};
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
`;
const XAxis = styled.line `
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;
const YAxis = styled.line `
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;
const ChartLabel = styled.text `
  font-size: 12px;
  fill: ${({ theme }) => theme.colors.textSecondary};
`;
const LoadingPlaceholder = styled.div `
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: ${({ theme }) => theme.colors.chartGrid};
`;
/**
 * DistributionChart Component
 *
 * Visualizes the distribution of winning and losing trades by range
 */
const DistributionChart = ({ data, isLoading }) => {
    if (isLoading) {
        return _jsx(LoadingPlaceholder, { children: "Loading distribution data..." });
    }
    if (!data || data.length === 0) {
        return _jsx(LoadingPlaceholder, { children: "No distribution data available" });
    }
    // Chart dimensions and margins
    const margin = { top: 20, right: 30, bottom: 60, left: 50 };
    const width = 800 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;
    // Find max count for y-scale
    const maxCount = Math.max(...data.map((d) => d.count));
    // Calculate bar width based on number of bars
    const barWidth = (width / data.length) * 0.8;
    const barSpacing = (width / data.length) * 0.2;
    return (_jsx(ChartContainer, { children: _jsx(ChartSvg, { viewBox: `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`, children: _jsxs("g", { transform: `translate(${margin.left}, ${margin.top})`, children: [_jsx(YAxis, { x1: 0, y1: 0, x2: 0, y2: height }), Array.from({ length: 6 }, (_, i) => {
                        const value = Math.ceil(maxCount / 5) * i;
                        const y = height - (value / maxCount) * height;
                        return (_jsxs("g", { transform: `translate(-10, ${y})`, children: [_jsx(ChartLabel, { textAnchor: "end", dominantBaseline: "middle", children: value }), _jsx("line", { x1: 0, y1: 0, x2: width, y2: 0, stroke: "#eee", strokeDasharray: "3,3" })] }, `y-label-${i}`));
                    }), _jsx(XAxis, { x1: 0, y1: height, x2: width, y2: height }), data.map((d, i) => {
                        const barHeight = (d.count / maxCount) * height;
                        const x = i * (barWidth + barSpacing) + barSpacing / 2;
                        const y = height - barHeight;
                        return (_jsxs("g", { children: [_jsx(Bar, { x: x, y: y, width: barWidth, height: barHeight, isWin: d.isWin, rx: 2 }), _jsx("g", { transform: `translate(${x + barWidth / 2}, ${height + 20})`, children: _jsx(ChartLabel, { textAnchor: "middle", transform: "rotate(45)", x: 0, y: 0, children: d.range }) })] }, `bar-${i}`));
                    }), _jsx(ChartLabel, { x: width / 2, y: -5, textAnchor: "middle", style: { fontSize: '14px' }, children: "Distribution of P&L by Range" })] }) }) }));
};
export default DistributionChart;
//# sourceMappingURL=DistributionChart.js.map