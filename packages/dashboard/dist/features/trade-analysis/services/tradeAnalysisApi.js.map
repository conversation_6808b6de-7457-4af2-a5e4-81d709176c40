{"version": 3, "file": "tradeAnalysisApi.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/services/tradeAnalysisApi.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAeH;;;;;GAKG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,KAAK,EAAE,OAAqB,EAA8B,EAAE;IAChG,0BAA0B;IAC1B,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAEzD,2EAA2E;IAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,qBAAqB;IAC/D,IAAI,WAAW,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;KACxD;IAED,sCAAsC;IACtC,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAG,CAAC,OAAqB,EAAqB,EAAE;IACpE,kBAAkB;IAClB,MAAM,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAE3C,oBAAoB;IACpB,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAEzC,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,4BAA4B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACzE,MAAM,mBAAmB,GAAG,4BAA4B,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7E,MAAM,oBAAoB,GAAG,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC/E,MAAM,kBAAkB,GAAG,4BAA4B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAE3E,+BAA+B;IAC/B,MAAM,oBAAoB,GAAG,wBAAwB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC3E,MAAM,oBAAoB,GAAG,wBAAwB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAE3E,OAAO;QACL,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;KACrB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,kBAAkB,GAAG,CAAC,OAAqB,EAAW,EAAE;IAC5D,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAE5C,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAE/F,sCAAsC;IACtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAE9E,MAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClF,MAAM,UAAU,GAAG;QACjB,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,YAAY;QACZ,aAAa;QACb,oBAAoB;KACrB,CAAC;IACF,MAAM,UAAU,GAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACrF,MAAM,QAAQ,GAAqB,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAC5E,MAAM,IAAI,GAAG;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,MAAM;QACN,WAAW;QACX,UAAU;QACV,UAAU;QACV,YAAY;KACb,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,8CAA8C;QAC9C,MAAM,SAAS,GAAG,IAAI,IAAI,CACxB,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAChF,CAAC;QAEF,qCAAqC;QACrC,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjD,wCAAwC;QACxC,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,mBAAmB;QAChF,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE7E,gCAAgC;QAChC,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,MAAM,SAAS,GAAmB,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACzE,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,gBAAgB;QAE9D,0CAA0C;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAClC,MAAM,WAAW,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,8BAA8B;QAEjF,yCAAyC;QACzC,IAAI,SAAS,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAmB,CAAC;QAElE,IAAI,WAAW,EAAE;YACf,SAAS,GAAG,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,iCAAiC;YACvF,MAAM,GAAG,WAAW,CAAC;SACtB;aAAM,IAAI,KAAK,EAAE;YAChB,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,iBAAiB;YAC/D,SAAS;gBACP,SAAS,KAAK,MAAM;oBAClB,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC;oBACrC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,KAAK,CAAC;SAChB;aAAM;YACL,MAAM,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,kBAAkB;YACjE,SAAS;gBACP,SAAS,KAAK,MAAM;oBAClB,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,CAAC;oBACtC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC;YAC3C,MAAM,GAAG,MAAM,CAAC;SACjB;QAED,gBAAgB;QAChB,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,mBAAmB;QAEzE,IAAI,SAAS,KAAK,MAAM,EAAE;YACxB,UAAU,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC;YACjD,iBAAiB,GAAG,CAAC,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SACxD;aAAM;YACL,UAAU,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC;YACjD,iBAAiB,GAAG,CAAC,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SACxD;QAED,4BAA4B;QAC5B,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAChD,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAE9D,iCAAiC;QACjC,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACtE,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3E,kCAAkC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;SACF;QAED,sBAAsB;QACtB,MAAM,KAAK,GAAU;YACnB,EAAE,EAAE,SAAS,CAAC,EAAE;YAChB,MAAM;YACN,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE;YAClC,QAAQ,EAAE,QAAQ,CAAC,WAAW,EAAE;YAChC,MAAM;YACN,UAAU;YACV,iBAAiB;YACjB,SAAS;YACT,OAAO;YACP,QAAQ;YACR,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,mBAAmB,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,CAAC,SAAS;SACxF,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACpB;IAED,qCAAqC;IACrC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AAClG,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAG,CAAC,MAAe,EAAsB,EAAE;IAC/D,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;IACvE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IACvE,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;IAEzE,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACjF,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACvF,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IAEjG,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAExF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAExF,MAAM,UAAU,GACd,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7F,MAAM,WAAW,GACf,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3F,wCAAwC;IACxC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;QACpD,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACpE,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GACnB,SAAS,CAAC,MAAM,GAAG,CAAC;QAClB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;QAC3E,CAAC,CAAC,CAAC,CAAC;IAER,yCAAyC;IACzC,MAAM,YAAY,GAChB,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7F,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7E,MAAM,UAAU,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,WAAW,CAAC;IAEtE,OAAO;QACL,WAAW,EAAE,MAAM,CAAC,MAAM;QAC1B,aAAa,EAAE,aAAa,CAAC,MAAM;QACnC,YAAY,EAAE,YAAY,CAAC,MAAM;QACjC,SAAS,EAAE,SAAS,CAAC,MAAM;QAC3B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG;QAC1C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;QAChD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;QAClD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;QACxD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;QAChD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;QACxD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;KAC/C,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,4BAA4B,GAAG,CACnC,MAAe,EACf,QAAyD,EAClC,EAAE;IACzB,2BAA2B;IAC3B,MAAM,UAAU,GAAG,IAAI,GAAG,EAAmB,CAAC;IAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAW,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YAClC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;SACnC;QACD,UAAU,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,WAAW,GAA0B,EAAE,CAAC;IAE9C,UAAU,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,aAAa,EAAE,EAAE;QACnD,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC/E,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,iBAAiB,GACrB,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1E,WAAW,CAAC,IAAI,CAAC;YACf,QAAQ;YACR,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG;YAC1C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;YACnD,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,GAAG;SAC7D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;AACjE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,wBAAwB,GAAG,CAC/B,MAAe,EACf,QAAmC,EAChB,EAAE;IACrB,oBAAoB;IACpB,IAAI,SAAmB,CAAC;IAExB,IAAI,QAAQ,KAAK,WAAW,EAAE;QAC5B,SAAS,GAAG;YACV,YAAY;YACZ,aAAa;YACb,aAAa;YACb,aAAa;YACb,aAAa;YACb,aAAa;YACb,aAAa;SACd,CAAC;KACH;SAAM;QACL,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KACtE;IAED,4BAA4B;IAC5B,MAAM,eAAe,GAAsB,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACtE,QAAQ;QACR,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,UAAU,EAAE,CAAC;KACd,CAAC,CAAC,CAAC;IAEJ,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,SAAiB,CAAC;QAEtB,IAAI,QAAQ,KAAK,WAAW,EAAE;YAC5B,8BAA8B;YAC9B,MAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC;YAErC,IAAI,SAAS,GAAG,GAAG,IAAI,SAAS,IAAI,EAAE,EAAE;gBACtC,OAAO,CAAC,+BAA+B;aACxC;YAED,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;iBAC/B,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;iBACpC,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;iBACpC,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;iBACpC,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;iBACpC,IAAI,SAAS,GAAG,IAAI;gBAAE,SAAS,GAAG,CAAC,CAAC;;gBACpC,SAAS,GAAG,CAAC,CAAC;SACpB;aAAM;YACL,iDAAiD;YACjD,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE;gBACtC,OAAO,CAAC,UAAU;aACnB;YACD,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;SAC3B;QAED,wBAAwB;QACxB,MAAM,IAAI,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;QAEpC,uBAAuB;QACvB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;YACrC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,QAAQ,KAAK,WAAW,EAAE;gBAC5B,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACpC,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC;gBAExC,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC;gBACnE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;gBACpE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;gBACpE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;gBACpE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;gBACpE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;gBACpE,IAAI,SAAS,KAAK,CAAC;oBAAE,OAAO,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,EAAE,CAAC;gBAClE,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,OAAO,UAAU,CAAC,MAAM,EAAE,KAAK,SAAS,GAAG,CAAC,CAAC;aAC9C;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC,CAAC,CAAC;IAEH,2CAA2C;IAC3C,OAAO,eAAe;SACnB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACjC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACd,GAAG,IAAI;QACP,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;QAC7C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;KACpD,CAAC,CAAC,CAAC;AACR,CAAC,CAAC"}