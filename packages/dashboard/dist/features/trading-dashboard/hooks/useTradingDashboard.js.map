{"version": 3, "file": "useTradingDashboard.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/hooks/useTradingDashboard.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAUzD,qEAAqE;AACrE,MAAM,UAAU,GAAY;IAC1B;QACE,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,GAAG;QACd,cAAc,EAAE,GAAG;QACnB,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,EAAE;QACR,GAAG,EAAE,IAAI;QACT,SAAS,EAAE,wBAAwB;QACnC,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,cAAc;QAC5B,eAAe,EAAE,4BAA4B;KAC9C;IACD;QACE,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,OAAO;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,IAAI;QACf,cAAc,EAAE,GAAG;QACnB,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,IAAI,EAAE,EAAE;QACR,GAAG,EAAE,IAAI;QACT,SAAS,EAAE,WAAW;QACtB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,eAAe;QAC7B,eAAe,EAAE,kBAAkB;KACpC;IACD;QACE,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,IAAI;QACf,cAAc,EAAE,GAAG;QACnB,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,OAAO;QACnB,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,EAAE;QACR,GAAG,EAAE,IAAI;QACT,SAAS,EAAE,wBAAwB;QACnC,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,cAAc;QAC5B,eAAe,EAAE,kBAAkB;KACpC;IACD;QACE,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,GAAG;QACd,cAAc,EAAE,GAAG;QACnB,GAAG,EAAE,KAAK;QACV,UAAU,EAAE,OAAO;QACnB,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,EAAE;QACR,GAAG,EAAE,CAAC,IAAI;QACV,SAAS,EAAE,wBAAwB;QACnC,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,eAAe;QAC7B,eAAe,EAAE,kBAAkB;KACpC;IACD;QACE,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,sBAAsB;QAC7B,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,OAAO;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,IAAI;QACf,cAAc,EAAE,GAAG;QACnB,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,IAAI,EAAE,EAAE;QACR,GAAG,EAAE,IAAI;QACT,SAAS,EAAE,WAAW;QACtB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,eAAe;QAC7B,eAAe,EAAE,yBAAyB;KAC3C;CACF,CAAC;AAEF,yBAAyB;AACzB,MAAM,aAAa,GAAqB;IACtC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;IAC9C,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;IAC7C,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;IAC7C,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE;IAC9C,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;CAC/C,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAEjC,EAAE;IACF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAiB;QACjD,MAAM,EAAE,EAAE;QACV,kBAAkB,EAAE,EAAE;QACtB,SAAS,EAAE,EAAE;QACb,gBAAgB,EAAE,EAAE;QACpB,kBAAkB,EAAE,EAAE;QACtB,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,IAAI;KACZ,CAAC,CAAC;IAEH,gDAAgD;IAChD,MAAM,gBAAgB,GAAG,CAAC,MAAe,EAAuB,EAAE;QAChE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACjE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACnE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,OAAO;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;YACtD,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACxD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAC3D,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE;SAC9C,CAAC;IACJ,CAAC,CAAC;IAEF,8BAA8B;IAC9B,MAAM,yBAAyB,GAAG,CAAC,MAAe,EAAsB,EAAE;QACxE,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAmB,CAAC;QAE5C,wBAAwB;QACxB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aACzB;YACD,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAClC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE;YAC3B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;YACvC,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACtE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACnE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;gBACb,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW;gBAC5E,CAAC,CAAC,CAAC,CAAC;YAER,OAAO;gBACL,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,WAAW;gBACX,GAAG;aACJ,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAC7D,CAAC,CAAC;IAEF,gCAAgC;IAChC,MAAM,2BAA2B,GAAG,CAAC,MAAe,EAAwB,EAAE;QAC5E,MAAM,UAAU,GAAG,IAAI,GAAG,EAAmB,CAAC;QAE9C,0BAA0B;QAC1B,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC5B,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC7B;YACD,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC;YACzC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACxE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;gBACb,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW;gBAC9E,CAAC,CAAC,CAAC,CAAC;YAER,OAAO;gBACL,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,WAAW;gBACX,GAAG;aACJ,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAC7D,CAAC,CAAC;IAEF,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAChD,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEhE,IAAI;YACF,4EAA4E;YAC5E,mEAAmE;YACnE,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEzD,mBAAmB;YACnB,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,kBAAkB,GAAG,2BAA2B,CAAC,UAAU,CAAC,CAAC;YAEnE,QAAQ,CAAC;gBACP,MAAM,EAAE,UAAU;gBAClB,kBAAkB;gBAClB,SAAS,EAAE,aAAa;gBACxB,gBAAgB;gBAChB,kBAAkB;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAClB,GAAG,IAAI;gBACP,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,+BAA+B;aACvC,CAAC,CAAC,CAAC;SACL;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,6BAA6B;IAC7B,SAAS,CAAC,GAAG,EAAE;QACb,kBAAkB,EAAE,CAAC;IACvB,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAEzB,OAAO;QACL,GAAG,KAAK;QACR,kBAAkB;KACnB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,mBAAmB,CAAC"}