/**
 * useTradingDashboard Hook
 *
 * Custom hook for fetching and managing trading dashboard data
 */
import { useState, useEffect, useCallback } from 'react';
// Mock data for development - will be replaced with actual API calls
const mockTrades = [
    {
        id: 1,
        date: '03/31/2025',
        model: 'RD-Cont.',
        session: 'NY Open',
        setup: 'Simple FVG-RD',
        entry: '09:57:00',
        exit: '10:01:00',
        direction: 'Long',
        market: 'MNQ',
        rMultiple: 0.8,
        patternQuality: 4.5,
        win: true,
        entryPrice: 19103.75,
        exitPrice: 19130.75,
        risk: 34,
        pnl: 54.0,
        dolTarget: 'FVG Target (Macro-FVG)',
        rdType: 'True-RD',
        entryVersion: 'Simple-Entry',
        drawOnLiquidity: 'REQH/L, MNOR-FVG, AM-FPFVG',
    },
    {
        id: 2,
        date: '03/31/2025',
        model: 'RD-Cont.',
        session: 'NY Open',
        setup: 'Complex FVG-RD',
        entry: '10:15:00',
        exit: '10:22:00',
        direction: 'Short',
        market: 'MNQ',
        rMultiple: 1.25,
        patternQuality: 3.5,
        win: true,
        entryPrice: 19145.25,
        exitPrice: 19124.0,
        risk: 17,
        pnl: 42.5,
        dolTarget: 'RD Target',
        rdType: 'True-RD',
        entryVersion: 'Complex-Entry',
        drawOnLiquidity: 'REQH/L, MNOR-FVG',
    },
    {
        id: 3,
        date: '04/01/2025',
        model: 'RD-Cont.',
        session: 'London Close',
        setup: 'Simple FVG-RD',
        entry: '11:32:00',
        exit: '11:45:00',
        direction: 'Long',
        market: 'MNQ',
        rMultiple: 1.45,
        patternQuality: 4.0,
        win: true,
        entryPrice: 19210.5,
        exitPrice: 19235.25,
        risk: 17,
        pnl: 49.5,
        dolTarget: 'FVG Target (Macro-FVG)',
        rdType: 'True-RD',
        entryVersion: 'Simple-Entry',
        drawOnLiquidity: 'REQH/L, MNOR-FVG',
    },
    {
        id: 4,
        date: '04/01/2025',
        model: 'RD-Cont.',
        session: 'NY Open',
        setup: 'Complex FVG-RD',
        entry: '09:45:00',
        exit: '09:52:00',
        direction: 'Long',
        market: 'MNQ',
        rMultiple: 0.0,
        patternQuality: 3.0,
        win: false,
        entryPrice: 19267.5,
        exitPrice: 19250.75,
        risk: 17,
        pnl: -33.5,
        dolTarget: 'FVG Target (Macro-FVG)',
        rdType: 'True-RD',
        entryVersion: 'Complex-Entry',
        drawOnLiquidity: 'REQH/L, MNOR-FVG',
    },
    {
        id: 5,
        date: '04/02/2025',
        model: 'RD-Cont.',
        session: 'Pre-Market',
        setup: 'True-RD Continuation',
        entry: '07:52:00',
        exit: '08:02:00',
        direction: 'Short',
        market: 'MNQ',
        rMultiple: 2.13,
        patternQuality: 3.5,
        win: true,
        entryPrice: 19448.75,
        exitPrice: 19413.0,
        risk: 17,
        pnl: 71.5,
        dolTarget: 'RD Target',
        rdType: 'True-RD',
        entryVersion: 'Complex-Entry',
        drawOnLiquidity: 'NDOG, Prev-3Days-KeyFVG',
    },
];
// Performance chart data
const mockChartData = [
    { date: '03/29', pnl: 72.5, cumulative: 72.5 },
    { date: '03/30', pnl: 92, cumulative: 164.5 },
    { date: '03/31', pnl: 92, cumulative: 256.5 },
    { date: '04/01', pnl: 113, cumulative: 369.5 },
    { date: '04/02', pnl: 102.5, cumulative: 472 },
];
/**
 * useTradingDashboard Hook
 *
 * Fetches and manages trading dashboard data
 */
export const useTradingDashboard = () => {
    const [state, setState] = useState({
        trades: [],
        performanceMetrics: [],
        chartData: [],
        setupPerformance: [],
        sessionPerformance: [],
        isLoading: true,
        error: null,
    });
    // Calculate performance metrics based on trades
    const calculateMetrics = (trades) => {
        const totalTrades = trades.length;
        const winningTrades = trades.filter((trade) => trade.win).length;
        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
        const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
        const avgRMultiple = totalTrades > 0 ? trades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades : 0;
        return [
            { title: 'Win Rate', value: `${winRate.toFixed(1)}%` },
            { title: 'Total P&L', value: `$${totalPnl.toFixed(2)}` },
            { title: 'Avg R-Multiple', value: avgRMultiple.toFixed(2) },
            { title: 'Total Trades', value: totalTrades },
        ];
    };
    // Calculate setup performance
    const calculateSetupPerformance = (trades) => {
        const setupMap = new Map();
        // Group trades by setup
        trades.forEach((trade) => {
            const setup = trade.setup;
            if (!setupMap.has(setup)) {
                setupMap.set(setup, []);
            }
            setupMap.get(setup)?.push(trade);
        });
        // Calculate metrics for each setup
        return Array.from(setupMap.entries())
            .map(([name, setupTrades]) => {
            const totalTrades = setupTrades.length;
            const winningTrades = setupTrades.filter((trade) => trade.win).length;
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
            const pnl = setupTrades.reduce((sum, trade) => sum + trade.pnl, 0);
            const avgRMultiple = totalTrades > 0
                ? setupTrades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades
                : 0;
            return {
                name,
                winRate,
                avgRMultiple,
                totalTrades,
                pnl,
            };
        })
            .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending
    };
    // Calculate session performance
    const calculateSessionPerformance = (trades) => {
        const sessionMap = new Map();
        // Group trades by session
        trades.forEach((trade) => {
            const session = trade.session;
            if (!sessionMap.has(session)) {
                sessionMap.set(session, []);
            }
            sessionMap.get(session)?.push(trade);
        });
        // Calculate metrics for each session
        return Array.from(sessionMap.entries())
            .map(([name, sessionTrades]) => {
            const totalTrades = sessionTrades.length;
            const winningTrades = sessionTrades.filter((trade) => trade.win).length;
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
            const pnl = sessionTrades.reduce((sum, trade) => sum + trade.pnl, 0);
            const avgRMultiple = totalTrades > 0
                ? sessionTrades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades
                : 0;
            return {
                name,
                winRate,
                avgRMultiple,
                totalTrades,
                pnl,
            };
        })
            .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending
    };
    // Fetch dashboard data
    const fetchDashboardData = useCallback(async () => {
        setState((prev) => ({ ...prev, isLoading: true, error: null }));
        try {
            // In a real app, this would be an API call to fetch data from Google Sheets
            // For now, we'll use mock data with a timeout to simulate API call
            await new Promise((resolve) => setTimeout(resolve, 800));
            // Process the data
            const performanceMetrics = calculateMetrics(mockTrades);
            const setupPerformance = calculateSetupPerformance(mockTrades);
            const sessionPerformance = calculateSessionPerformance(mockTrades);
            setState({
                trades: mockTrades,
                performanceMetrics,
                chartData: mockChartData,
                setupPerformance,
                sessionPerformance,
                isLoading: false,
                error: null,
            });
        }
        catch (error) {
            console.error('Error fetching dashboard data:', error);
            setState((prev) => ({
                ...prev,
                isLoading: false,
                error: 'Failed to load dashboard data',
            }));
        }
    }, []);
    // Fetch data on initial load
    useEffect(() => {
        fetchDashboardData();
    }, [fetchDashboardData]);
    return {
        ...state,
        fetchDashboardData,
    };
};
export default useTradingDashboard;
//# sourceMappingURL=useTradingDashboard.js.map