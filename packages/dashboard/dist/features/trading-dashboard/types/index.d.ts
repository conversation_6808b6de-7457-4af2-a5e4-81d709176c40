/**
 * Trading Dashboard Types
 *
 * Type definitions for the Trading Dashboard feature
 */
import { Trade, PerformanceMetrics } from '@adhd-trading-dashboard/shared';
export { Trade, PerformanceMetrics };
/**
 * PerformanceMetric
 *
 * Represents a single performance metric
 */
export interface PerformanceMetric {
    title: string;
    value: string | number;
    change?: number;
    isPositive?: boolean;
}
/**
 * ChartDataPoint
 *
 * Represents a single data point for performance charts
 */
export interface ChartDataPoint {
    date: string;
    pnl: number;
    cumulative: number;
}
/**
 * SetupPerformance
 *
 * Represents performance metrics for a specific setup
 */
export interface SetupPerformance {
    name: string;
    winRate: number;
    avgRMultiple: number;
    totalTrades: number;
    pnl: number;
}
/**
 * SessionPerformance
 *
 * Represents performance metrics for a specific trading session
 */
export interface SessionPerformance {
    name: string;
    winRate: number;
    avgRMultiple: number;
    totalTrades: number;
    pnl: number;
}
/**
 * DashboardState
 *
 * Represents the state of the trading dashboard
 */
export interface DashboardState {
    trades: Trade[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    isLoading: boolean;
    error: string | null;
}
//# sourceMappingURL=index.d.ts.map