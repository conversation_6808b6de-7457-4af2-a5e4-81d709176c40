{"version": 3, "file": "RecentTradesTable.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/RecentTradesTable.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAQvC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAA;sBACX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;mBACtC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBAC5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;mBAE5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,CAAA;eACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;gBAClC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;;;;CAIzB,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA;6BACD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;CAC9D,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAA;;aAEhB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;WACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;eACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;;CAG/C,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;6BACG,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;;;;CAK9D,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAA;aACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;WACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;eACnC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;CAC/C,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAiC;WAC7D,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,CAChC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;CACnE,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAkB;WAC3C,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;CAC/E,CAAC;AAEF,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAmB;WACzC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;CACxF,CAAC;AAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAKxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAKvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAqC,CAAC,EAClE,MAAM,EACN,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,IAAI,SAAS,EAAE;QACb,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,gCAA2B,EACtC,KAAC,gBAAgB,yCAA0C,IAC5C,CAClB,CAAC;KACH;IAED,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,gCAA2B,EACtC,KAAC,eAAe,2CAA2C,IAC5C,CAClB,CAAC;KACH;IAED,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,gCAA2B,EACtC,MAAC,KAAK,eACJ,KAAC,SAAS,cACR,yBACE,KAAC,WAAW,uBAAmB,EAC/B,KAAC,WAAW,wBAAoB,EAChC,KAAC,WAAW,0BAAsB,EAClC,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,yBAAqB,EACjC,KAAC,WAAW,wBAAoB,EAChC,KAAC,WAAW,uBAAmB,EAC/B,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,sBAAkB,IAC3B,GACK,EACZ,0BACG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CACrB,MAAC,QAAQ,eACP,KAAC,SAAS,cAAE,KAAK,CAAC,IAAI,GAAa,EACnC,KAAC,SAAS,cAAE,KAAK,CAAC,KAAK,GAAa,EACpC,KAAC,SAAS,cAAE,KAAK,CAAC,OAAO,GAAa,EACtC,KAAC,aAAa,IAAC,SAAS,EAAE,KAAK,CAAC,SAAS,YAAG,KAAK,CAAC,SAAS,GAAiB,EAC5E,KAAC,SAAS,cAAE,KAAK,CAAC,MAAM,GAAa,EACrC,KAAC,SAAS,cAAE,KAAK,CAAC,KAAK,GAAa,EACpC,KAAC,SAAS,cAAE,KAAK,CAAC,IAAI,GAAa,EACnC,KAAC,UAAU,IAAC,GAAG,EAAE,KAAK,CAAC,GAAG,YAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAc,EACrE,MAAC,OAAO,IAAC,KAAK,EAAE,KAAK,CAAC,GAAG,kBAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAW,KAT/C,KAAK,CAAC,EAAE,CAUZ,CACZ,CAAC,GACI,IACF,IACO,CAClB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,iBAAiB,CAAC"}