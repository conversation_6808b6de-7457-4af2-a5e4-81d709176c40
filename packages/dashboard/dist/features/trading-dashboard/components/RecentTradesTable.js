import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const TableContainer = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  overflow-x: auto;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const TableTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;
const TableHead = styled.thead `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const TableHeader = styled.th `
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
`;
const TableRow = styled.tr `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const DirectionCell = styled(TableCell) `
  color: ${({ theme, direction }) => direction === 'Long' ? theme.colors.success : theme.colors.error};
`;
const ResultCell = styled(TableCell) `
  color: ${({ theme, win }) => (win ? theme.colors.success : theme.colors.error)};
`;
const PnlCell = styled(TableCell) `
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};
`;
const LoadingContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const NoDataContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * RecentTradesTable Component
 *
 * Displays a table of recent trades
 */
export const RecentTradesTable = ({ trades, isLoading = false, }) => {
    if (isLoading) {
        return (_jsxs(TableContainer, { children: [_jsx(TableTitle, { children: "Recent Trades" }), _jsx(LoadingContainer, { children: "Loading trades data..." })] }));
    }
    if (!trades || trades.length === 0) {
        return (_jsxs(TableContainer, { children: [_jsx(TableTitle, { children: "Recent Trades" }), _jsx(NoDataContainer, { children: "No trades data available" })] }));
    }
    return (_jsxs(TableContainer, { children: [_jsx(TableTitle, { children: "Recent Trades" }), _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs("tr", { children: [_jsx(TableHeader, { children: "Date" }), _jsx(TableHeader, { children: "Setup" }), _jsx(TableHeader, { children: "Session" }), _jsx(TableHeader, { children: "Direction" }), _jsx(TableHeader, { children: "Market" }), _jsx(TableHeader, { children: "Entry" }), _jsx(TableHeader, { children: "Exit" }), _jsx(TableHeader, { children: "R-Multiple" }), _jsx(TableHeader, { children: "P&L" })] }) }), _jsx("tbody", { children: trades.map((trade) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: trade.date }), _jsx(TableCell, { children: trade.setup }), _jsx(TableCell, { children: trade.session }), _jsx(DirectionCell, { direction: trade.direction, children: trade.direction }), _jsx(TableCell, { children: trade.market }), _jsx(TableCell, { children: trade.entry }), _jsx(TableCell, { children: trade.exit }), _jsx(ResultCell, { win: trade.win, children: trade.rMultiple.toFixed(2) }), _jsxs(PnlCell, { value: trade.pnl, children: ["$", trade.pnl.toFixed(2)] })] }, trade.id))) })] })] }));
};
export default RecentTradesTable;
//# sourceMappingURL=RecentTradesTable.js.map