import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const Container = styled.div `
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;
const AnalysisCard = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const CardTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
`;
const TableHead = styled.thead `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const TableHeader = styled.th `
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
`;
const TableRow = styled.tr `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const WinRateCell = styled(TableCell) `
  color: ${({ theme, value }) => {
    if (value >= 70)
        return theme.colors.success;
    if (value >= 50)
        return theme.colors.warning;
    return theme.colors.error;
}};
`;
const PnlCell = styled(TableCell) `
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};
`;
const LoadingContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const NoDataContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * SetupAnalysis Component
 *
 * Displays performance metrics for different trading setups
 */
export const SetupAnalysis = ({ setupPerformance, sessionPerformance, isLoading = false, }) => {
    if (isLoading) {
        return (_jsxs(Container, { children: [_jsxs(AnalysisCard, { children: [_jsx(CardTitle, { children: "Setup Performance" }), _jsx(LoadingContainer, { children: "Loading setup data..." })] }), _jsxs(AnalysisCard, { children: [_jsx(CardTitle, { children: "Session Performance" }), _jsx(LoadingContainer, { children: "Loading session data..." })] })] }));
    }
    return (_jsxs(Container, { children: [_jsxs(AnalysisCard, { children: [_jsx(CardTitle, { children: "Setup Performance" }), setupPerformance.length === 0 ? (_jsx(NoDataContainer, { children: "No setup data available" })) : (_jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs("tr", { children: [_jsx(TableHeader, { children: "Setup" }), _jsx(TableHeader, { children: "Win Rate" }), _jsx(TableHeader, { children: "Avg R" }), _jsx(TableHeader, { children: "Trades" }), _jsx(TableHeader, { children: "P&L" })] }) }), _jsx("tbody", { children: setupPerformance.map((setup, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: setup.name }), _jsxs(WinRateCell, { value: setup.winRate, children: [setup.winRate.toFixed(1), "%"] }), _jsx(TableCell, { children: setup.avgRMultiple.toFixed(2) }), _jsx(TableCell, { children: setup.totalTrades }), _jsxs(PnlCell, { value: setup.pnl, children: ["$", setup.pnl.toFixed(2)] })] }, index))) })] }))] }), _jsxs(AnalysisCard, { children: [_jsx(CardTitle, { children: "Session Performance" }), sessionPerformance.length === 0 ? (_jsx(NoDataContainer, { children: "No session data available" })) : (_jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs("tr", { children: [_jsx(TableHeader, { children: "Session" }), _jsx(TableHeader, { children: "Win Rate" }), _jsx(TableHeader, { children: "Avg R" }), _jsx(TableHeader, { children: "Trades" }), _jsx(TableHeader, { children: "P&L" })] }) }), _jsx("tbody", { children: sessionPerformance.map((session, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: session.name }), _jsxs(WinRateCell, { value: session.winRate, children: [session.winRate.toFixed(1), "%"] }), _jsx(TableCell, { children: session.avgRMultiple.toFixed(2) }), _jsx(TableCell, { children: session.totalTrades }), _jsxs(PnlCell, { value: session.pnl, children: ["$", session.pnl.toFixed(2)] })] }, index))) })] }))] })] }));
};
export default SetupAnalysis;
//# sourceMappingURL=SetupAnalysis.js.map