/**
 * Recent Trades Table Component
 *
 * Displays a table of recent trades
 */
import React from 'react';
import { Trade } from '@adhd-trading-dashboard/shared';
interface RecentTradesTableProps {
    trades: Trade[];
    isLoading?: boolean;
}
/**
 * RecentTradesTable Component
 *
 * Displays a table of recent trades
 */
export declare const RecentTradesTable: React.FC<RecentTradesTableProps>;
export default RecentTradesTable;
//# sourceMappingURL=RecentTradesTable.d.ts.map