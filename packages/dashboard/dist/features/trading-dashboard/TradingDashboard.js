import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Trading Dashboard Component
 *
 * Main component for the Trading Dashboard feature
 */
import { useState } from 'react';
import styled from 'styled-components';
import { useTradingDashboard } from './hooks/useTradingDashboard';
import MetricsPanel from './components/MetricsPanel';
import PerformanceChart from './components/PerformanceChart';
import RecentTradesTable from './components/RecentTradesTable';
import SetupAnalysis from './components/SetupAnalysis';
const DashboardContainer = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const Header = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  margin: 0;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const StatusBadge = styled.span `
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 500;
  margin-left: ${({ theme }) => theme.spacing.sm};
`;
const TabsContainer = styled.div `
  display: flex;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const Tab = styled.button `
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textSecondary)};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 500;
  cursor: pointer;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  }

  &:hover {
    color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};
  }
`;
const RefreshButton = styled.button `
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
  }
`;
const LoadingOverlay = styled.div `
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal};
`;
const LoadingSpinner = styled.div `
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
/**
 * TradingDashboard Component
 *
 * Main component for the Trading Dashboard feature
 */
export const TradingDashboard = () => {
    const [activeTab, setActiveTab] = useState('summary');
    const { trades, performanceMetrics, chartData, setupPerformance, sessionPerformance, isLoading, error, fetchDashboardData, } = useTradingDashboard();
    const handleRefresh = () => {
        fetchDashboardData();
    };
    return (_jsxs(DashboardContainer, { children: [_jsxs(Header, { children: [_jsxs("div", { children: [_jsx(Title, { children: "Trading Dashboard" }), _jsx(StatusBadge, { children: "SESSION 1" })] }), _jsx(RefreshButton, { onClick: handleRefresh, disabled: isLoading, children: isLoading ? 'Refreshing...' : 'Refresh Data' })] }), _jsxs(TabsContainer, { children: [_jsx(Tab, { active: activeTab === 'summary', onClick: () => setActiveTab('summary'), children: "Summary" }), _jsx(Tab, { active: activeTab === 'trades', onClick: () => setActiveTab('trades'), children: "Trades" }), _jsx(Tab, { active: activeTab === 'analysis', onClick: () => setActiveTab('analysis'), children: "Analysis" })] }), error && _jsxs("div", { style: { color: 'red', marginBottom: '16px' }, children: ["Error: ", error] }), activeTab === 'summary' && (_jsxs(_Fragment, { children: [_jsx(MetricsPanel, { metrics: performanceMetrics, isLoading: isLoading }), _jsx(PerformanceChart, { data: chartData, isLoading: isLoading }), _jsx(RecentTradesTable, { trades: trades.slice(0, 5), isLoading: isLoading })] })), activeTab === 'trades' && _jsx(RecentTradesTable, { trades: trades, isLoading: isLoading }), activeTab === 'analysis' && (_jsxs(_Fragment, { children: [_jsx(MetricsPanel, { metrics: performanceMetrics, isLoading: isLoading }), _jsx(SetupAnalysis, { setupPerformance: setupPerformance, sessionPerformance: sessionPerformance, isLoading: isLoading })] })), isLoading && (_jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) }))] }));
};
export default TradingDashboard;
//# sourceMappingURL=TradingDashboard.js.map