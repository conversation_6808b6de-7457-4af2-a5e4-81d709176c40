import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';
import { MarketOverview } from './components/MarketOverview';
import { TradingPlan } from './components/TradingPlan';
import { KeyLevels } from './components/KeyLevels';
import { MarketNews } from './components/MarketNews';
import { SectionCard } from './components/ui';
import { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext';
const PageContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;
const PageHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const DateDisplay = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const RefreshButton = styled(Button) `
  margin-left: ${({ theme }) => theme.spacing.md};
`;
const DailyGuideContent = () => {
    const { isLoading, error, currentDate, refreshData } = useDailyGuide();
    const handleRefresh = () => {
        refreshData();
    };
    return (_jsxs(PageContainer, { children: [_jsxs(PageHeader, { children: [_jsx(Title, { children: "Daily Trading Guide" }), _jsxs("div", { style: { display: 'flex', alignItems: 'center' }, children: [_jsx(DateDisplay, { children: currentDate }), _jsx(RefreshButton, { variant: "outline", size: "small", onClick: handleRefresh, disabled: isLoading, children: "Refresh" })] })] }), _jsx(SectionCard, { title: "Market Overview", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(MarketOverview, {}) }), _jsx(SectionCard, { title: "Trading Plan", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(TradingPlan, {}) }), _jsx(SectionCard, { title: "Key Levels", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(KeyLevels, {}) }), _jsx(SectionCard, { title: "Market News", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(MarketNews, {}) })] }));
};
const DailyGuide = () => {
    return (_jsx(DailyGuideProvider, { children: _jsx(DailyGuideContent, {}) }));
};
export default DailyGuide;
//# sourceMappingURL=DailyGuide.js.map