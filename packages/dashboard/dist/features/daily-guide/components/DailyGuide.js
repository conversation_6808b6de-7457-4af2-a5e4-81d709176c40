import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { <PERSON>, Button } from '@adhd-trading-dashboard/shared';
import { DailyGuideProvider } from '../state';
import { useDailyGuide } from '../hooks';
import { MarketOverview } from './MarketOverview';
import { TradingPlan } from './TradingPlan';
import { KeyLevels } from './KeyLevels';
import styled from 'styled-components';
// Styled components
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
const Header = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const DateSelector = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`;
const DateInput = styled.input `
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const Grid = styled.div `
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
`;
const Column = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;
/**
 * Daily Guide Content Component
 *
 * The connected component that uses the daily guide hook.
 */
const DailyGuideContent = ({ className }) => {
    const { selectedDate, marketOverview, tradingPlan, keyPriceLevels, isLoading, error, currentDate, onDateChange, onTradingPlanItemToggle, onAddTradingPlanItem, onRemoveTradingPlanItem, onRefresh, } = useDailyGuide();
    return (_jsxs(Container, { className: className, children: [_jsxs(Header, { children: [_jsxs(Title, { children: ["Daily Trading Guide - ", currentDate] }), _jsxs(DateSelector, { children: [_jsx(DateInput, { type: "date", value: selectedDate, onChange: (e) => onDateChange(e.target.value), max: new Date().toISOString().split('T')[0] }), _jsx(Button, { onClick: onRefresh, startIcon: "\uD83D\uDD04", children: "Refresh" })] })] }), _jsxs(Grid, { children: [_jsxs(Column, { children: [_jsx(MarketOverview, { marketOverview: marketOverview, isLoading: isLoading, error: error, onRefresh: onRefresh }), _jsx(KeyLevels, { keyLevels: keyPriceLevels, isLoading: isLoading, error: error, onRefresh: onRefresh })] }), _jsx(Column, { children: _jsx(TradingPlan, { tradingPlan: tradingPlan, isLoading: isLoading, error: error, onItemToggle: onTradingPlanItemToggle, onItemAdd: onAddTradingPlanItem, onItemRemove: onRemoveTradingPlanItem }) })] })] }));
};
/**
 * Daily Guide Component
 *
 * The main component for the daily guide feature.
 */
export const DailyGuide = ({ title = 'Daily Guide', className, }) => {
    return (_jsx(DailyGuideProvider, { children: _jsx(Card, { title: title, children: _jsx(DailyGuideContent, { className: className }) }) }));
};
//# sourceMappingURL=DailyGuide.js.map