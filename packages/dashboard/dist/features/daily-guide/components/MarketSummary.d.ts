/**
 * Market Summary Component
 *
 * Displays market sentiment, summary text, and last updated information.
 */
import React from 'react';
import { MarketSentiment } from '../types';
export interface MarketSummaryProps {
    /** Market sentiment */
    sentiment: MarketSentiment;
    /** Market summary text */
    summary: string;
    /** Last updated timestamp */
    lastUpdated?: string;
}
/**
 * Market Summary Component
 *
 * Displays market sentiment, summary text, and last updated information.
 */
export declare const MarketSummary: React.FC<MarketSummaryProps>;
//# sourceMappingURL=MarketSummary.d.ts.map