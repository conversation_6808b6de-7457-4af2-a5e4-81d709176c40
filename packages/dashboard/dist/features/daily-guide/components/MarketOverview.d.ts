/**
 * Market Overview Component
 *
 * Main container component for displaying market overview information.
 */
import React from 'react';
import { MarketOverview as MarketOverviewType } from '../types';
export interface MarketOverviewProps {
    /** The market overview data */
    marketOverview: MarketOverviewType | null;
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * Market Overview Component
 *
 * Main container component for displaying market overview information.
 */
export declare const MarketOverview: React.FC<MarketOverviewProps>;
//# sourceMappingURL=MarketOverview.d.ts.map