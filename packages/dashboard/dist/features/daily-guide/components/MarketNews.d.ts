/**
 * Market News Component
 *
 * Displays economic events and news in a structured table format.
 */
import React from 'react';
import { EconomicEvent } from '../types';
export interface MarketNewsProps {
    /** Array of economic events */
    events: EconomicEvent[];
}
/**
 * Market News Component
 *
 * Displays economic events and news in a structured table format.
 */
export declare const MarketNews: React.FC<MarketNewsProps>;
//# sourceMappingURL=MarketNews.d.ts.map