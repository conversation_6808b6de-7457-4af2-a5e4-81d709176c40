import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Trading Plan Component
 *
 * A component for displaying and managing a trading plan.
 */
import { useState } from 'react';
import { Card, Badge, Button, Input, FormField } from '@adhd-trading-dashboard/shared';
import styled from 'styled-components';
// Styled components
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const Section = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const SectionTitle = styled.h4 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const PlanList = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const PlanItem = styled.div `
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  opacity: ${({ completed }) => (completed ? 0.6 : 1)};
  transition: opacity 0.2s ease;
`;
const CheckboxContainer = styled.div `
  margin-right: ${({ theme }) => theme.spacing.sm};
`;
const Checkbox = styled.input `
  cursor: pointer;
  width: 18px;
  height: 18px;
`;
const ItemContent = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  flex: 1;
`;
const Description = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  text-decoration: ${({ completed }) => (completed ? 'line-through' : 'none')};
`;
const ItemActions = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const RiskManagementGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;
const RiskManagementItem = styled.div `
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  flex-direction: column;
`;
const RiskManagementLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const RiskManagementValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const Notes = styled.div `
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.textPrimary};
  white-space: pre-wrap;
`;
const AddItemForm = styled.form `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;
const FormActions = styled.div `
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
/**
 * Get the badge variant for a priority
 */
const getPriorityVariant = (priority) => {
    switch (priority) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
        default:
            return 'info';
    }
};
/**
 * Trading Plan Component
 *
 * A component for displaying and managing a trading plan.
 */
export const TradingPlan = ({ tradingPlan, isLoading = false, error = null, onItemToggle, onItemAdd, onItemRemove, className, }) => {
    const [showAddForm, setShowAddForm] = useState(false);
    const [newItem, setNewItem] = useState({
        description: '',
        priority: 'medium',
        completed: false,
    });
    const handleAddItem = (e) => {
        e.preventDefault();
        if (!newItem.description.trim() || !onItemAdd)
            return;
        onItemAdd({
            ...newItem,
            id: Date.now().toString(), // Generate a unique ID
        });
        setNewItem({
            description: '',
            priority: 'medium',
            completed: false,
        });
        setShowAddForm(false);
    };
    // Loading state
    if (isLoading) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Loading trading plan..." }) }));
    }
    // Error state
    if (error) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", error] }) }));
    }
    // Empty state
    if (!tradingPlan) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsxs(EmptyState, { children: ["No trading plan available.", onItemAdd && (_jsx("div", { style: { marginTop: '16px' }, children: _jsx(Button, { onClick: () => setShowAddForm(true), children: "Create Trading Plan" }) }))] }) }));
    }
    return (_jsx(Card, { title: "Trading Plan", actions: onItemAdd
            ? [{ label: 'Add Item', onClick: () => setShowAddForm(true), icon: '➕' }]
            : undefined, children: _jsxs(Container, { className: className, children: [tradingPlan.strategy && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Strategy" }), _jsx(Notes, { children: tradingPlan.strategy })] })), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "Action Items" }), _jsx(PlanList, { children: tradingPlan.items.map((item) => (_jsxs(PlanItem, { completed: item.completed, children: [_jsx(CheckboxContainer, { children: _jsx(Checkbox, { type: "checkbox", checked: !!item.completed, onChange: (e) => onItemToggle?.(item.id, e.target.checked), disabled: !onItemToggle }) }), _jsxs(ItemContent, { children: [_jsx(Description, { completed: item.completed, children: item.description }), _jsx(Badge, { variant: getPriorityVariant(item.priority), children: item.priority })] }), onItemRemove && (_jsx(ItemActions, { children: _jsx(Button, { variant: "icon", onClick: () => onItemRemove(item.id), "aria-label": "Remove item", children: "\uD83D\uDDD1\uFE0F" }) }))] }, item.id))) })] }), tradingPlan.riskManagement && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Risk Management" }), _jsxs(RiskManagementGrid, { children: [_jsxs(RiskManagementItem, { children: [_jsx(RiskManagementLabel, { children: "Max Risk Per Trade" }), _jsxs(RiskManagementValue, { children: [tradingPlan.riskManagement.maxRiskPerTrade, "%"] })] }), _jsxs(RiskManagementItem, { children: [_jsx(RiskManagementLabel, { children: "Max Daily Loss" }), _jsxs(RiskManagementValue, { children: [tradingPlan.riskManagement.maxDailyLoss, "%"] })] }), _jsxs(RiskManagementItem, { children: [_jsx(RiskManagementLabel, { children: "Max Trades" }), _jsx(RiskManagementValue, { children: tradingPlan.riskManagement.maxTrades })] }), _jsxs(RiskManagementItem, { children: [_jsx(RiskManagementLabel, { children: "Position Sizing" }), _jsx(RiskManagementValue, { children: tradingPlan.riskManagement.positionSizing })] })] })] })), tradingPlan.notes && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Notes" }), _jsx(Notes, { children: tradingPlan.notes })] })), showAddForm && (_jsxs(AddItemForm, { onSubmit: handleAddItem, children: [_jsx(FormField, { label: "Description", children: _jsx(Input, { value: newItem.description, onChange: (value) => setNewItem({ ...newItem, description: value }), placeholder: "Enter task description", required: true, fullWidth: true }) }), _jsx(FormField, { label: "Priority", children: _jsxs("select", { value: newItem.priority, onChange: (e) => setNewItem({ ...newItem, priority: e.target.value }), style: {
                                    padding: '8px',
                                    borderRadius: '4px',
                                    border: '1px solid #ccc',
                                    width: '100%',
                                }, children: [_jsx("option", { value: "high", children: "High" }), _jsx("option", { value: "medium", children: "Medium" }), _jsx("option", { value: "low", children: "Low" })] }) }), _jsxs(FormActions, { children: [_jsx(Button, { variant: "outline", onClick: () => setShowAddForm(false), children: "Cancel" }), _jsx(Button, { type: "submit", children: "Add Item" })] })] }))] }) }));
};
//# sourceMappingURL=TradingPlan.js.map