/**
 * Market Indicators Component
 *
 * Displays market indices with their values, changes, and previous close data.
 */
import React from 'react';
import { MarketIndex } from '../types';
export interface MarketIndicatorsProps {
    /** Array of market indices */
    indices: MarketIndex[];
}
/**
 * Market Indicators Component
 *
 * Displays market indices with their values, changes, and previous close data.
 */
export declare const MarketIndicators: React.FC<MarketIndicatorsProps>;
//# sourceMappingURL=MarketIndicators.d.ts.map