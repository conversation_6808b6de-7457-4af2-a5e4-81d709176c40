import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Minimal App Component
 *
 * A simple component to test React rendering
 */
const MinimalApp = () => {
    return (_jsxs("div", { style: {
            padding: '20px',
            background: 'green',
            color: 'white',
            fontFamily: 'Arial, sans-serif',
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
        }, children: [_jsx("h1", { children: "Minimal App Working!" }), _jsx("p", { children: "This is a minimal test component to verify React rendering" }), _jsx("button", { style: {
                    padding: '10px 20px',
                    background: 'white',
                    color: 'green',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginTop: '20px'
                }, onClick: () => alert('Button clicked!'), children: "Click Me" })] }));
};
export default MinimalApp;
//# sourceMappingURL=MinimalApp.js.map