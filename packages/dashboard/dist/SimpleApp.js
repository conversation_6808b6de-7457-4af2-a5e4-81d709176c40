import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * A super simple component with no dependencies
 */
const SimpleApp = () => {
    return (_jsxs("div", { style: {
            padding: '20px',
            background: 'red',
            color: 'white',
            fontFamily: 'Arial, sans-serif',
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
        }, children: [_jsx("h1", { children: "Simple App Working!" }), _jsx("p", { children: "This is a minimal test component with no dependencies" })] }));
};
export default SimpleApp;
//# sourceMappingURL=SimpleApp.js.map