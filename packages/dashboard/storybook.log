yarn add v1.22.22
warning package-lock.json found. Your project contains lock files generated by tools other than Yarn. It is advised not to mix package managers in order to avoid resolution inconsistencies caused by unsynchronized lock files. To clear this warning, remove package-lock.json.
[1/4] Resolving packages...
warning Lockfile has incorrect entry for "@typescript-eslint/typescript-estree@5.62.0". Ignoring it.
[2/4] Fetching packages...
[3/4] Linking dependencies...
warning " > @adhd-trading-dashboard/dashboard@0.1.0" has unmet peer dependency "react@18.2.0".
warning " > @adhd-trading-dashboard/dashboard@0.1.0" has unmet peer dependency "react-dom@18.2.0".
warning " > @adhd-trading-dashboard/dashboard@0.1.0" has unmet peer dependency "recharts@2.10.3".
warning " > @adhd-trading-dashboard/dashboard@0.1.0" has unmet peer dependency "styled-components@5.3.6".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > recharts@2.10.3" has unmet peer dependency "prop-types@^15.6.0".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > styled-components@5.3.6" has unmet peer dependency "react-is@>= 16.8.0".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > @testing-library/user-event@13.5.0" has unmet peer dependency "@testing-library/dom@>=7.21.4".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > @adhd-trading-dashboard/dashboard > vite-plugin-pwa@1.0.0" has unmet peer dependency "workbox-build@^7.3.0".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > @adhd-trading-dashboard/dashboard > vite-plugin-pwa@1.0.0" has unmet peer dependency "workbox-window@^7.3.0".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > recharts > react-smooth@2.0.5" has unmet peer dependency "prop-types@^15.6.0".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > @adhd-trading-dashboard/dashboard > react-scripts > eslint-config-react-app > eslint-plugin-flowtype@8.0.3" has unmet peer dependency "@babel/plugin-syntax-flow@^7.14.5".
warning "workspace-aggregator-54163b93-71cb-48d2-8bcc-2c372413bac3 > @adhd-trading-dashboard/dashboard > react-scripts > eslint-config-react-app > eslint-plugin-flowtype@8.0.3" has unmet peer dependency "@babel/plugin-transform-react-jsx@^7.14.9".
warning "eslint-plugin-storybook > @typescript-eslint/utils@8.32.1" has unmet peer dependency "eslint@^8.57.0 || ^9.0.0".
warning "eslint-plugin-storybook > @typescript-eslint/utils@8.32.1" has unmet peer dependency "typescript@>=4.8.4 <5.9.0".
warning "eslint-plugin-storybook > @typescript-eslint/utils > @typescript-eslint/typescript-estree@8.32.1" has unmet peer dependency "typescript@>=4.8.4 <5.9.0".
warning "eslint-plugin-storybook > @typescript-eslint/utils > @typescript-eslint/typescript-estree > ts-api-utils@2.1.0" has unmet peer dependency "typescript@>=4.8.4".
warning " > eslint-plugin-storybook@0.12.0" has unmet peer dependency "eslint@>=8".
error Invariant Violation: expected workspace package to exist for "rimraf"
    at invariant (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:2318:15)
    at _loop2 (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:91560:9)
    at PackageHoister.init (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:91619:19)
    at PackageLinker.getFlatHoistedTree (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:48551:20)
    at PackageLinker.<anonymous> (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:48562:27)
    at Generator.next (<anonymous>)
    at step (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:310:30)
    at /Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:328:14
    at new Promise (<anonymous>)
    at new F (/Users/<USER>/.npm-global/lib/node_modules/yarn/lib/cli.js:5539:28)
info Visit https://yarnpkg.com/en/docs/cli/add for documentation about this command.
