/**
 * ProfitLossCell Storybook Stories
 *
 * Comprehensive stories demonstrating all states and use cases for the ProfitLossCell component.
 * Perfect for ADHD trading dashboard - provides visual consistency and quick testing.
 */

import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import styled from 'styled-components';
import { ProfitLossCell } from './ProfitLossCell';

// Styled wrapper for story layouts
const StoryWrapper = styled.div`
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const StorySection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StoryTitle = styled.h3`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: 8px;
`;

const StoryRow = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const StoryLabel = styled.span`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  min-width: 120px;
`;

const TableDemo = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
`;

const TableHeader = styled.th`
  background: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: 12px;
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
`;

const TableCell = styled.td`
  padding: 12px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const meta: Meta<typeof ProfitLossCell> = {
  title: 'Trading/ProfitLossCell',
  component: ProfitLossCell,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# ProfitLossCell Component

A specialized component for displaying profit/loss amounts in trading applications with:

- **Color-coded visual feedback** (Green for profits, Red for losses, Gray for breakeven)
- **ADHD-friendly design** with clear visual states and consistent formatting
- **Accessibility support** with proper ARIA labels and keyboard navigation
- **Multiple size variants** for different contexts (table cells, cards, summaries)
- **Loading states** with shimmer animation
- **Responsive hover effects** for better user interaction

## Use Cases
- Trade tables showing P&L for individual trades
- Dashboard summary cards displaying total profits/losses
- Real-time position monitoring with live P&L updates
- Historical performance analysis views

## Trading Scenarios Covered
- Big wins ($247.50+)
- Small wins ($12.75)
- Breakeven trades ($0.00)
- Small losses (-$25.50)
- Big losses (-$156.25+)
- Loading/pending states
        `,
      },
    },
  },
  argTypes: {
    amount: {
      control: { type: 'number', min: -1000, max: 1000, step: 0.01 },
      description: 'The profit/loss amount to display',
    },
    currency: {
      control: { type: 'text' },
      description: 'Currency symbol (default: $)',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
      description: 'Size variant for different contexts',
    },
    showPositiveSign: {
      control: { type: 'boolean' },
      description: 'Whether to show + sign for positive amounts',
    },
    isLoading: {
      control: { type: 'boolean' },
      description: 'Loading state with shimmer animation',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Stories
export const Default: Story = {
  args: {
    amount: 247.5,
  },
};

export const BigWin: Story = {
  args: {
    amount: 247.5,
    showPositiveSign: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Large profitable trade - the kind that makes your day! Shows clear green styling for quick recognition.',
      },
    },
  },
};

export const SmallWin: Story = {
  args: {
    amount: 12.75,
  },
  parameters: {
    docs: {
      description: {
        story: 'Small but consistent profits add up. Still shows positive green styling.',
      },
    },
  },
};

export const Breakeven: Story = {
  args: {
    amount: 0.0,
  },
  parameters: {
    docs: {
      description: {
        story: 'No profit, no loss. Neutral gray styling indicates breakeven trades.',
      },
    },
  },
};

export const SmallLoss: Story = {
  args: {
    amount: -25.5,
  },
  parameters: {
    docs: {
      description: {
        story: 'Minor loss - part of trading. Red styling provides clear visual feedback.',
      },
    },
  },
};

export const BigLoss: Story = {
  args: {
    amount: -156.25,
  },
  parameters: {
    docs: {
      description: {
        story: 'Significant loss that needs attention. Bold red styling for immediate recognition.',
      },
    },
  },
};

export const Loading: Story = {
  args: {
    amount: null,
    isLoading: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Loading state with shimmer animation while P&L is being calculated.',
      },
    },
  },
};

// Interactive Playground
export const Interactive: Story = {
  args: {
    amount: 125.75,
    currency: '$',
    size: 'medium',
    showPositiveSign: false,
    isLoading: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Interactive playground - adjust the controls to see how the component responds to different values and settings.',
      },
    },
  },
};

// Size Variants
export const SizeVariants: Story = {
  render: () => (
    <StoryWrapper>
      <StorySection>
        <StoryTitle>Size Variants</StoryTitle>
        <StoryRow>
          <StoryLabel>Small:</StoryLabel>
          <ProfitLossCell amount={247.5} size="small" />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Medium:</StoryLabel>
          <ProfitLossCell amount={247.5} size="medium" />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Large:</StoryLabel>
          <ProfitLossCell amount={247.5} size="large" />
        </StoryRow>
      </StorySection>
    </StoryWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'Different size variants for various contexts - small for dense tables, medium for standard use, large for emphasis.',
      },
    },
  },
};

// Trading Scenarios
export const TradingScenarios: Story = {
  render: () => (
    <StoryWrapper>
      <StorySection>
        <StoryTitle>Real Trading Scenarios</StoryTitle>
        <StoryRow>
          <StoryLabel>Scalp Win:</StoryLabel>
          <ProfitLossCell amount={8.25} showPositiveSign />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Day Trade:</StoryLabel>
          <ProfitLossCell amount={156.75} showPositiveSign />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Swing Trade:</StoryLabel>
          <ProfitLossCell amount={487.5} showPositiveSign />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Stop Loss:</StoryLabel>
          <ProfitLossCell amount={-45.25} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Blown Account:</StoryLabel>
          <ProfitLossCell amount={-1247.8} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Commission:</StoryLabel>
          <ProfitLossCell amount={-2.5} />
        </StoryRow>
      </StorySection>
    </StoryWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'Real-world trading scenarios with typical profit/loss amounts traders encounter daily.',
      },
    },
  },
};

// Table Context Demo
export const TableContext: Story = {
  render: () => (
    <StoryWrapper>
      <StorySection>
        <StoryTitle>Trading Table Context</StoryTitle>
        <TableDemo>
          <thead>
            <tr>
              <TableHeader>Date</TableHeader>
              <TableHeader>Symbol</TableHeader>
              <TableHeader>Direction</TableHeader>
              <TableHeader>Quantity</TableHeader>
              <TableHeader>P&L</TableHeader>
            </tr>
          </thead>
          <tbody>
            <tr>
              <TableCell>2024-01-15</TableCell>
              <TableCell>MNQ</TableCell>
              <TableCell>Long</TableCell>
              <TableCell>2</TableCell>
              <TableCell>
                <ProfitLossCell amount={247.5} size="small" />
              </TableCell>
            </tr>
            <tr>
              <TableCell>2024-01-15</TableCell>
              <TableCell>ES</TableCell>
              <TableCell>Short</TableCell>
              <TableCell>1</TableCell>
              <TableCell>
                <ProfitLossCell amount={-156.25} size="small" />
              </TableCell>
            </tr>
            <tr>
              <TableCell>2024-01-15</TableCell>
              <TableCell>NQ</TableCell>
              <TableCell>Long</TableCell>
              <TableCell>0.5</TableCell>
              <TableCell>
                <ProfitLossCell amount={0.0} size="small" />
              </TableCell>
            </tr>
            <tr>
              <TableCell>2024-01-16</TableCell>
              <TableCell>MNQ</TableCell>
              <TableCell>Long</TableCell>
              <TableCell>1</TableCell>
              <TableCell>
                <ProfitLossCell amount={null} isLoading size="small" />
              </TableCell>
            </tr>
          </tbody>
        </TableDemo>
      </StorySection>
    </StoryWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'How the component looks in a real trading table context with mixed results and loading states.',
      },
    },
  },
};

// Edge Cases
export const EdgeCases: Story = {
  render: () => (
    <StoryWrapper>
      <StorySection>
        <StoryTitle>Edge Cases & Error Handling</StoryTitle>
        <StoryRow>
          <StoryLabel>Very Large Win:</StoryLabel>
          <ProfitLossCell amount={12547.89} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Very Large Loss:</StoryLabel>
          <ProfitLossCell amount={-9876.54} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Tiny Amount:</StoryLabel>
          <ProfitLossCell amount={0.01} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Null Amount:</StoryLabel>
          <ProfitLossCell amount={null} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Undefined:</StoryLabel>
          <ProfitLossCell amount={undefined} />
        </StoryRow>
        <StoryRow>
          <StoryLabel>Different Currency:</StoryLabel>
          <ProfitLossCell amount={247.5} currency="€" />
        </StoryRow>
        <StoryRow>
          <StoryLabel>No Currency:</StoryLabel>
          <ProfitLossCell amount={247.5} currency="" />
        </StoryRow>
      </StorySection>
    </StoryWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'Edge cases including very large numbers, null/undefined values, and different currencies.',
      },
    },
  },
};
