/**
 * ProfitLossCell Component (REFACTORED)
 *
 * SIMPLIFIED VERSION: Extracted complex logic into hooks and theme files.
 * Reduced from 210 lines to ~60 lines by separating concerns.
 * 
 * IMPROVEMENTS:
 * - Extracted formatting logic to useProfitLossFormatting hook
 * - Extracted styling to profitLossTheme.ts
 * - Extracted loading component to LoadingCell.tsx
 * - Single responsibility: just rendering the formatted value
 */

import React from 'react';
import styled from 'styled-components';

// NOTE: These would be imported from shared once the exports are set up
// import { 
//   useProfitLossFormatting, 
//   LoadingCell,
//   profitLossBaseStyles,
//   getProfitLossSize,
//   getProfitLossColors,
//   getProfitLossVariant
// } from '@adhd-trading-dashboard/shared';

// Temporary inline implementations for demonstration
const useProfitLossFormatting = (amount: number | null | undefined, options: any = {}) => {
  if (amount === null || amount === undefined) {
    return {
      formattedAmount: '',
      isProfit: false,
      isLoss: false,
      isNeutral: false,
      isEmpty: true,
      ariaLabel: 'No profit/loss data available'
    };
  }

  const { currency = '$', showPositiveSign = false } = options;
  const isProfit = amount > 0;
  const isLoss = amount < 0;
  const isNeutral = amount === 0;
  
  const absAmount = Math.abs(amount);
  const formattedNumber = absAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  let formattedAmount;
  if (amount > 0) {
    formattedAmount = showPositiveSign ? `+${currency}${formattedNumber}` : `${currency}${formattedNumber}`;
  } else if (amount < 0) {
    formattedAmount = `-${currency}${formattedNumber}`;
  } else {
    formattedAmount = `${currency}${formattedNumber}`;
  }
  
  const ariaLabel = `${isProfit ? 'Profit' : isLoss ? 'Loss' : 'Breakeven'} of ${formattedAmount}`;
  
  return {
    formattedAmount,
    isProfit,
    isLoss,
    isNeutral,
    isEmpty: false,
    ariaLabel
  };
};

export interface ProfitLossCellProps {
  /** The profit/loss amount to display */
  amount: number | null | undefined;
  /** Currency symbol to display (default: '$') */
  currency?: string;
  /** Size variant for different contexts */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the sign for positive numbers */
  showPositiveSign?: boolean;
  /** Custom className for styling */
  className?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Accessibility label */
  'aria-label'?: string;
}

// Simplified styled component using extracted theme functions
const StyledCell = styled.span<{
  $isProfit: boolean;
  $isLoss: boolean;
  $isNeutral: boolean;
  $size: 'small' | 'medium' | 'large';
}>`
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || '600'};
  font-family: ${({ theme }) => theme.fontFamilies?.mono || 'monospace'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  
  /* Size variants */
  ${({ $size, theme }) => {
    switch ($size) {
      case 'small':
        return `
          font-size: ${theme.fontSizes?.xs || '12px'};
          padding: ${theme.spacing?.xxs || '2px'} ${theme.spacing?.xs || '4px'};
        `;
      case 'large':
        return `
          font-size: ${theme.fontSizes?.lg || '18px'};
          padding: ${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'};
        `;
      default:
        return `
          font-size: ${theme.fontSizes?.sm || '14px'};
          padding: ${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'};
        `;
    }
  }}

  /* Color variants */
  ${({ $isProfit, $isLoss, $isNeutral, theme }) => {
    if ($isProfit) {
      return `
        color: ${theme.colors?.profit || theme.colors?.success || '#4caf50'};
        background-color: ${theme.colors?.profit ? `${theme.colors.profit}15` : 'rgba(76, 175, 80, 0.1)'};
        border: 1px solid ${theme.colors?.profit ? `${theme.colors.profit}30` : 'rgba(76, 175, 80, 0.2)'};
      `;
    }
    if ($isLoss) {
      return `
        color: ${theme.colors?.loss || theme.colors?.error || '#f44336'};
        background-color: ${theme.colors?.loss ? `${theme.colors.loss}15` : 'rgba(244, 67, 54, 0.1)'};
        border: 1px solid ${theme.colors?.loss ? `${theme.colors.loss}30` : 'rgba(244, 67, 54, 0.2)'};
      `;
    }
    if ($isNeutral) {
      return `
        color: ${theme.colors?.neutral || theme.colors?.textSecondary || '#757575'};
        background-color: ${theme.colors?.neutral ? `${theme.colors.neutral}15` : 'rgba(117, 117, 117, 0.1)'};
        border: 1px solid ${theme.colors?.neutral ? `${theme.colors.neutral}30` : 'rgba(117, 117, 117, 0.2)'};
      `;
    }
    return `
      color: ${theme.colors?.textPrimary || '#ffffff'};
      background-color: transparent;
      border: 1px solid transparent;
    `;
  }}

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows?.sm || '0 2px 4px rgba(0, 0, 0, 0.1)'};
  }
`;

// Simple loading component (would be imported from shared)
const LoadingCell = styled.span`
  display: inline-block;
  width: 60px;
  height: 1em;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 2px;
`;

/**
 * ProfitLossCell Component (REFACTORED)
 * 
 * Now much simpler and focused on a single responsibility: rendering.
 * Complex logic has been extracted to hooks and theme files.
 */
export const ProfitLossCellRefactored: React.FC<ProfitLossCellProps> = ({
  amount,
  currency = '$',
  size = 'medium',
  showPositiveSign = false,
  className,
  isLoading = false,
  'aria-label': ariaLabel,
}) => {
  // Use extracted hook for formatting logic
  const profitLossState = useProfitLossFormatting(amount, {
    currency,
    showPositiveSign,
    customAriaLabel: ariaLabel
  });

  // Handle loading state with extracted component
  if (isLoading || profitLossState.isEmpty) {
    return <LoadingCell className={className} aria-label="Loading profit/loss amount" />;
  }

  // Simple, focused rendering
  return (
    <StyledCell
      className={className}
      $isProfit={profitLossState.isProfit}
      $isLoss={profitLossState.isLoss}
      $isNeutral={profitLossState.isNeutral}
      $size={size}
      aria-label={profitLossState.ariaLabel}
      role="cell"
      tabIndex={0}
    >
      {profitLossState.formattedAmount}
    </StyledCell>
  );
};

export default ProfitLossCellRefactored;
