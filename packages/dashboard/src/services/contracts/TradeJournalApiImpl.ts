/**
 * Trade Journal API Implementation
 *
 * Concrete implementation of the TradeJournalApi contract.
 * This provides the actual functionality for trade journal operations.
 */

import {
  TradeJournalApi,
  TradeJournalEvents,
  TradeJournalState,
  Trade,
  TradeFormData,
  SetupComponents,
  tradeStorageService,
} from '@adhd-trading-dashboard/shared';

export class TradeJournalApiImpl implements TradeJournalApi {
  private eventListeners: Partial<TradeJournalEvents> = {};
  private state: TradeJournalState = {
    trades: [],
    isLoading: false,
    error: null,
    selectedTrade: null,
    filters: {},
  };

  /**
   * Get trade data by ID
   */
  async getTradeData(id: number): Promise<Trade | null> {
    try {
      this.setState({ isLoading: true, error: null });
      const trade = await tradeStorageService.getTradeById(id);
      this.setState({ isLoading: false });
      return trade;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get trade data';
      this.setState({ isLoading: false, error: errorMessage });
      return null;
    }
  }

  /**
   * Validate setup components
   */
  validateSetup(setup: SetupComponents): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate required fields
    if (!setup.constant) {
      errors.push('Constant element is required');
    }

    if (!setup.entry) {
      errors.push('Entry method is required');
    }

    // Validate combinations
    if (setup.constant && setup.variable) {
      // Add specific validation rules for setup combinations
      if (setup.constant === 'NWOG' && setup.variable === 'London-H/L') {
        // This is a valid combination
      } else if (setup.constant === 'Strong-FVG' && setup.variable === 'NY-H/L') {
        // This is also valid
      }
      // Add more validation rules as needed
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get recent trades
   */
  async getRecentTrades(limit: number = 10): Promise<Trade[]> {
    try {
      this.setState({ isLoading: true, error: null });
      const trades = await tradeStorageService.getAllTrades();

      // Sort by date and limit
      const recentTrades = trades
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);

      this.setState({ isLoading: false, trades: recentTrades });
      return recentTrades;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get recent trades';
      this.setState({ isLoading: false, error: errorMessage });
      return [];
    }
  }

  /**
   * Calculate trade metrics
   */
  calculateMetrics(trades: Trade[]) {
    const totalTrades = trades.length;

    if (totalTrades === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        avgPnL: 0,
        totalPnL: 0,
      };
    }

    const wins = trades.filter((trade) => trade.win_loss === 'Win').length;
    const winRate = (wins / totalTrades) * 100;

    const totalPnL = trades.reduce((sum, trade) => {
      const pnl = parseFloat(trade.profit_loss?.toString() || '0');
      return sum + pnl;
    }, 0);

    const avgPnL = totalPnL / totalTrades;

    return {
      totalTrades,
      winRate,
      avgPnL,
      totalPnL,
    };
  }

  /**
   * Export trades data
   */
  async exportTrades(trades: Trade[], format: 'csv' | 'json'): Promise<string> {
    if (format === 'json') {
      return JSON.stringify(trades, null, 2);
    }

    // CSV export
    if (trades.length === 0) {
      return 'No trades to export';
    }

    const headers = [
      'Date',
      'Symbol',
      'Direction',
      'Entry Price',
      'Exit Price',
      'Size',
      'P&L',
      'Win/Loss',
      'Setup',
      'Notes',
    ];

    const csvRows = [
      headers.join(','),
      ...trades.map((trade) =>
        [
          trade.date,
          trade.symbol,
          trade.direction,
          trade.entry_price,
          trade.exit_price,
          trade.size,
          trade.profit_loss,
          trade.win_loss,
          trade.setup_constant || '',
          (trade.notes || '').replace(/,/g, ';'), // Replace commas to avoid CSV issues
        ].join(',')
      ),
    ];

    return csvRows.join('\n');
  }

  /**
   * Event management
   */
  addEventListener<K extends keyof TradeJournalEvents>(
    event: K,
    listener: TradeJournalEvents[K]
  ): void {
    this.eventListeners[event] = listener;
  }

  removeEventListener<K extends keyof TradeJournalEvents>(event: K): void {
    delete this.eventListeners[event];
  }

  /**
   * State management
   */
  getState(): TradeJournalState {
    return { ...this.state };
  }

  private setState(updates: Partial<TradeJournalState>): void {
    this.state = { ...this.state, ...updates };
  }

  /**
   * Emit events
   */
  private emit<K extends keyof TradeJournalEvents>(
    event: K,
    ...args: Parameters<TradeJournalEvents[K]>
  ): void {
    const listener = this.eventListeners[event];
    if (listener) {
      // @ts-ignore - TypeScript has trouble with this pattern
      listener(...args);
    }
  }

  /**
   * Public methods to emit events (called by other parts of the app)
   */
  emitTradeCreated(trade: Trade): void {
    this.emit('onTradeCreated', trade);
  }

  emitTradeUpdated(trade: Trade): void {
    this.emit('onTradeUpdated', trade);
  }

  emitTradeDeleted(tradeId: number): void {
    this.emit('onTradeDeleted', tradeId);
  }

  emitTradesFiltered(trades: Trade[]): void {
    this.emit('onTradesFiltered', trades);
  }
}

// Export singleton instance
export const tradeJournalApi = new TradeJournalApiImpl();
