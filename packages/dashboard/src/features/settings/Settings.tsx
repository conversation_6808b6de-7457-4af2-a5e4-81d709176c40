/**
 * Settings Component
 *
 * This component displays application settings and user preferences.
 */

import React from 'react';
import styled from 'styled-components';
import { useSettings } from './hooks/useSettings';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const ContentSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const SettingGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md} 0;
  border-bottom: 1px solid #4b5563;

  &:last-child {
    border-bottom: none;
  }
`;

const SettingRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SettingLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const SettingDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: #9ca3af;
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const Toggle = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: ${({ theme }) => theme.colors.primary};
  }

  &:checked + span:before {
    transform: translateX(26px);
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #4b5563;
  transition: ${({ theme }) => theme.transitions.normal};
  border-radius: 34px;

  &:before {
    position: absolute;
    content: '';
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: ${({ theme }) => theme.transitions.normal};
    border-radius: 50%;
  }
`;

const SaveButton = styled.button`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.primary};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color ${({ theme }) => theme.transitions.fast};
  align-self: flex-end;
  margin-top: ${({ theme }) => theme.spacing.lg};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

/**
 * Settings Component
 *
 * Main component for application settings and preferences
 */
const Settings: React.FC = () => {
  const { settings, handleChange, handleSave } = useSettings();

  return (
    <PageContainer>
      <PageHeader>
        <Title>Settings</Title>
      </PageHeader>

      <ContentSection>
        <SectionTitle>Appearance</SectionTitle>

        <SettingGroup>
          <SettingRow>
            <div>
              <SettingLabel>Theme</SettingLabel>
              <SettingDescription>Choose your preferred theme</SettingDescription>
            </div>
            <Select value={settings.theme} onChange={(e) => handleChange('theme', e.target.value)}>
              <option value="f1">Formula 1</option>
              <option value="light">Light</option>
            </Select>
          </SettingRow>
        </SettingGroup>
      </ContentSection>

      <ContentSection>
        <SectionTitle>General Settings</SectionTitle>

        <SettingGroup>
          <SettingRow>
            <div>
              <SettingLabel>Data Refresh Interval</SettingLabel>
              <SettingDescription>How often to refresh dashboard data (minutes)</SettingDescription>
            </div>
            <Input
              type="number"
              min="1"
              max="60"
              value={settings.refreshInterval}
              onChange={(e) => handleChange('refreshInterval', parseInt(e.target.value))}
              style={{ width: '80px' }}
            />
          </SettingRow>
        </SettingGroup>

        <SettingGroup>
          <SettingRow>
            <div>
              <SettingLabel>Notifications</SettingLabel>
              <SettingDescription>Enable desktop notifications</SettingDescription>
            </div>
            <Toggle>
              <ToggleInput
                type="checkbox"
                checked={settings.showNotifications}
                onChange={(e) => handleChange('showNotifications', e.target.checked)}
              />
              <ToggleSlider />
            </Toggle>
          </SettingRow>
        </SettingGroup>

        <SettingGroup>
          <SettingRow>
            <div>
              <SettingLabel>Advanced Metrics</SettingLabel>
              <SettingDescription>Show additional performance metrics</SettingDescription>
            </div>
            <Toggle>
              <ToggleInput
                type="checkbox"
                checked={settings.enableAdvancedMetrics}
                onChange={(e) => handleChange('enableAdvancedMetrics', e.target.checked)}
              />
              <ToggleSlider />
            </Toggle>
          </SettingRow>
        </SettingGroup>

        <SettingGroup>
          <SettingRow>
            <div>
              <SettingLabel>Auto-Save Journal</SettingLabel>
              <SettingDescription>Automatically save trade entries as you type</SettingDescription>
            </div>
            <Toggle>
              <ToggleInput
                type="checkbox"
                checked={settings.autoSaveJournal}
                onChange={(e) => handleChange('autoSaveJournal', e.target.checked)}
              />
              <ToggleSlider />
            </Toggle>
          </SettingRow>
        </SettingGroup>

        <SaveButton onClick={handleSave}>Save Settings</SaveButton>
      </ContentSection>
    </PageContainer>
  );
};

export default Settings;
