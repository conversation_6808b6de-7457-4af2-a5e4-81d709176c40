/**
 * Key Levels Component
 *
 * A component for displaying key price levels.
 */
import React from 'react';
import { Card, Badge } from '@adhd-trading-dashboard/shared';
import { KeyPriceLevel } from '../types';
import styled from 'styled-components';

export interface KeyLevelsProps {
  /** The key price levels */
  keyLevels: KeyPriceLevel[];
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const LevelsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const LevelCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const Symbol = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
`;

const LevelsRow = styled.div`
  display: flex;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const LevelLabel = styled.div`
  width: 100px;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: #9ca3af;
`;

const LevelValue = styled.div<{ type: 'support' | 'resistance' | 'pivot' }>`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, type }) => {
    switch (type) {
      case 'support':
        return theme.colors.profit;
      case 'resistance':
        return theme.colors.loss;
      case 'pivot':
        return theme.colors.accent;
      default:
        return theme.colors.textPrimary;
    }
  }};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: #9ca3af;
  font-style: italic;
`;

/**
 * Key Levels Component
 *
 * A component for displaying key price levels.
 */
export const KeyLevels: React.FC<KeyLevelsProps> = ({
  keyLevels,
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  // Loading state
  if (isLoading) {
    return (
      <Card title="Key Price Levels">
        <div style={{ padding: '24px', textAlign: 'center' }}>Loading key levels...</div>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card title="Key Price Levels">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {error}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  // Empty state
  if (!keyLevels || keyLevels.length === 0) {
    return (
      <Card title="Key Price Levels">
        <EmptyState>
          No key price levels available.
          {onRefresh && (
            <div style={{ marginTop: '16px' }}>
              <button
                onClick={onRefresh}
                style={{
                  padding: '8px 16px',
                  background: '#f0f0f0',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                }}
              >
                Refresh
              </button>
            </div>
          )}
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card
      title="Key Price Levels"
      actions={onRefresh ? [{ label: 'Refresh', onClick: onRefresh, icon: '🔄' }] : undefined}
    >
      <Container className={className}>
        <LevelsGrid>
          {keyLevels.map((level, index) => (
            <LevelCard key={index}>
              <Symbol>
                {level.symbol}
                <Badge variant="primary" style={{ marginLeft: '8px' }}>
                  {level.support.length + level.resistance.length} levels
                </Badge>
              </Symbol>

              <LevelsRow>
                <LevelLabel>Resistance</LevelLabel>
                <LevelValue type="resistance">{level.resistance.join(' | ')}</LevelValue>
              </LevelsRow>

              {level.pivotPoint && (
                <LevelsRow>
                  <LevelLabel>Pivot</LevelLabel>
                  <LevelValue type="pivot">{level.pivotPoint}</LevelValue>
                </LevelsRow>
              )}

              <LevelsRow>
                <LevelLabel>Support</LevelLabel>
                <LevelValue type="support">{level.support.join(' | ')}</LevelValue>
              </LevelsRow>
            </LevelCard>
          ))}
        </LevelsGrid>
      </Container>
    </Card>
  );
};
