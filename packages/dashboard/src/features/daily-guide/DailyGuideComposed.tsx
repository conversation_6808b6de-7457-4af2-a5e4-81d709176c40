/**
 * Daily Guide Component (Composition Pattern)
 *
 * REFACTORED VERSION: Uses composition pattern with DashboardSection
 * to reduce coupling between individual daily guide components.
 */

import React from 'react';
import styled from 'styled-components';
import { Button, DashboardSection } from '@adhd-trading-dashboard/shared';
import { MarketOverview } from './components/MarketOverview';
import { TradingPlan } from './components/TradingPlan';
import { KeyLevels } from './components/KeyLevels';
import { MarketNews } from './components/MarketNews';
import { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const DateDisplay = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const RefreshButton = styled(Button)`
  margin-left: ${({ theme }) => theme.spacing.md};
`;

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
`;

const Column = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const DailyGuideContent: React.FC = () => {
  const { isLoading, error, currentDate, refreshData } = useDailyGuide();

  const handleRefresh = () => {
    refreshData();
  };

  const refreshAction = (
    <RefreshButton
      variant="outline"
      size="small"
      onClick={handleRefresh}
      disabled={isLoading}
    >
      🔄 Refresh
    </RefreshButton>
  );

  return (
    <PageContainer>
      <PageHeader>
        <Title>Daily Trading Guide</Title>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DateDisplay>{currentDate}</DateDisplay>
          {refreshAction}
        </div>
      </PageHeader>

      <GridContainer>
        <Column>
          {/* COMPOSITION PATTERN: Using DashboardSection instead of SectionCard */}
          <DashboardSection 
            name="market-overview" 
            title="Market Overview"
            isLoading={isLoading}
            error={error}
            actions={refreshAction}
            collapsible={true}
            defaultCollapsed={false}
          >
            <MarketOverview />
          </DashboardSection>

          <DashboardSection 
            name="key-levels" 
            title="Key Price Levels"
            isLoading={isLoading}
            error={error}
            collapsible={true}
            defaultCollapsed={false}
          >
            <KeyLevels />
          </DashboardSection>

          <DashboardSection 
            name="market-news" 
            title="Market News & Events"
            isLoading={isLoading}
            error={error}
            collapsible={true}
            defaultCollapsed={true}
          >
            <MarketNews />
          </DashboardSection>
        </Column>

        <Column>
          <DashboardSection 
            name="trading-plan" 
            title="Today's Trading Plan"
            isLoading={isLoading}
            error={error}
            collapsible={true}
            defaultCollapsed={false}
          >
            <TradingPlan />
          </DashboardSection>
        </Column>
      </GridContainer>
    </PageContainer>
  );
};

const DailyGuideComposed: React.FC = () => {
  return (
    <DailyGuideProvider>
      <DailyGuideContent />
    </DailyGuideProvider>
  );
};

export default DailyGuideComposed;
