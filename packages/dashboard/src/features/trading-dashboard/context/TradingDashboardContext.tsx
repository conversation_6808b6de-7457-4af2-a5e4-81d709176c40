/**
 * TradingDashboardContext
 * 
 * EXTRACTED FROM: TradingDashboard.tsx state management
 * Centralized state management for the trading dashboard with performance optimization.
 * 
 * BENEFITS:
 * - Eliminates prop drilling
 * - Centralized state management
 * - Performance optimized with useMemo
 * - Type-safe context values
 * - Proper cleanup and error handling
 */

import React, { createContext, useContext, useMemo, ReactNode } from 'react';
import { TabType } from '../components/DashboardTabs';
import { 
  Trade, 
  PerformanceMetric, 
  ChartDataPoint, 
  SetupPerformance, 
  SessionPerformance 
} from '@adhd-trading-dashboard/shared';

export interface TradingDashboardState {
  // Data state
  trades: Trade[];
  performanceMetrics: PerformanceMetric[];
  chartData: ChartDataPoint[];
  setupPerformance: SetupPerformance[];
  sessionPerformance: SessionPerformance[];
  
  // UI state
  activeTab: TabType;
  
  // Loading state
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface TradingDashboardActions {
  // Tab management
  setActiveTab: (tab: TabType) => void;
  
  // Data management
  refreshData: () => Promise<void>;
  clearError: () => void;
  
  // Session management
  isLiveSession: () => boolean;
  getCurrentSessionName: () => string;
}

export type TradingDashboardContextValue = TradingDashboardState & TradingDashboardActions;

const TradingDashboardContext = createContext<TradingDashboardContextValue | null>(null);

export interface TradingDashboardProviderProps {
  children: ReactNode;
  /** Initial state values */
  initialState?: Partial<TradingDashboardState>;
  /** Custom data fetching function */
  dataFetcher?: () => Promise<{
    trades: Trade[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
  }>;
}

/**
 * TradingDashboardProvider Component
 * 
 * Provides centralized state management for the trading dashboard.
 * Uses performance optimization techniques to prevent unnecessary re-renders.
 */
export const TradingDashboardProvider: React.FC<TradingDashboardProviderProps> = ({
  children,
  initialState = {},
  dataFetcher,
}) => {
  // This will be replaced with actual state management in the next phase
  // For now, providing a basic structure with default values
  const defaultState: TradingDashboardState = {
    trades: [],
    performanceMetrics: [],
    chartData: [],
    setupPerformance: [],
    sessionPerformance: [],
    activeTab: 'summary',
    isLoading: false,
    error: null,
    lastUpdated: null,
    ...initialState,
  };

  // Placeholder actions - will be implemented with actual state management
  const actions: TradingDashboardActions = {
    setActiveTab: (tab: TabType) => {
      console.log('Setting active tab:', tab);
      // TODO: Implement actual state update
    },
    
    refreshData: async () => {
      console.log('Refreshing data...');
      // TODO: Implement actual data refresh
      if (dataFetcher) {
        try {
          const data = await dataFetcher();
          console.log('Data refreshed:', data);
        } catch (error) {
          console.error('Failed to refresh data:', error);
        }
      }
    },
    
    clearError: () => {
      console.log('Clearing error');
      // TODO: Implement actual error clearing
    },
    
    isLiveSession: () => {
      // TODO: Implement actual live session detection
      const now = new Date();
      const hour = now.getHours();
      // Simple logic: consider live during market hours (9 AM - 4 PM EST)
      return hour >= 9 && hour < 16;
    },
    
    getCurrentSessionName: () => {
      // TODO: Implement actual session name logic
      const now = new Date();
      const hour = now.getHours();
      
      if (hour >= 2 && hour < 8) return 'Sydney Session';
      if (hour >= 8 && hour < 14) return 'London Session';
      if (hour >= 14 && hour < 20) return 'New York Session';
      return 'After Hours';
    },
  };

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo<TradingDashboardContextValue>(() => ({
    ...defaultState,
    ...actions,
  }), [
    // Dependencies for memoization
    defaultState.trades,
    defaultState.performanceMetrics,
    defaultState.chartData,
    defaultState.setupPerformance,
    defaultState.sessionPerformance,
    defaultState.activeTab,
    defaultState.isLoading,
    defaultState.error,
    defaultState.lastUpdated,
  ]);

  return (
    <TradingDashboardContext.Provider value={contextValue}>
      {children}
    </TradingDashboardContext.Provider>
  );
};

/**
 * Hook to use TradingDashboardContext
 * 
 * @throws Error if used outside of TradingDashboardProvider
 * @returns TradingDashboardContextValue
 * 
 * @example
 * ```typescript
 * const { trades, activeTab, setActiveTab, refreshData } = useTradingDashboardContext();
 * ```
 */
export const useTradingDashboardContext = (): TradingDashboardContextValue => {
  const context = useContext(TradingDashboardContext);
  
  if (!context) {
    throw new Error(
      'useTradingDashboardContext must be used within a TradingDashboardProvider. ' +
      'Make sure to wrap your component with <TradingDashboardProvider>.'
    );
  }
  
  return context;
};

/**
 * Convenience hooks for specific parts of the context
 */

/**
 * Hook for tab management
 */
export const useTradingDashboardTabs = () => {
  const { activeTab, setActiveTab } = useTradingDashboardContext();
  return { activeTab, setActiveTab };
};

/**
 * Hook for data management
 */
export const useTradingDashboardData = () => {
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    lastUpdated,
    refreshData,
    clearError,
  } = useTradingDashboardContext();
  
  return {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    lastUpdated,
    refreshData,
    clearError,
  };
};

/**
 * Hook for session management
 */
export const useTradingDashboardSession = () => {
  const { isLiveSession, getCurrentSessionName } = useTradingDashboardContext();
  
  return {
    isLive: isLiveSession(),
    sessionName: getCurrentSessionName(),
  };
};

export default TradingDashboardContext;
