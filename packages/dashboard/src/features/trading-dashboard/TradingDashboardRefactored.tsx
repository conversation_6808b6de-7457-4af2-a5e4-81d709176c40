/**
 * TradingDashboardRefactored Component
 *
 * PHASE 3 COMPLETE: Final refactored implementation
 * This is the complete refactored version using all extracted components.
 *
 * PROGRESS:
 * ✅ F1Header - Extracted and working
 * ✅ DashboardTabs - Extracted and working
 * ✅ TradingDashboardContext - Real data integration
 * ✅ useTradingDashboardData - Performance optimized
 * ✅ QuickTradeForm - Validation and submission
 * ✅ TradingDashboardContainer - Final orchestrator
 */

import React from 'react';
import { TradingDashboardContainer } from './components/TradingDashboardContainer';

export interface TradingDashboardRefactoredProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}

/**
 * TradingDashboardRefactored Component
 *
 * Simplified wrapper that uses the final TradingDashboardContainer.
 * This demonstrates the complete refactored architecture.
 */
export const TradingDashboardRefactored: React.FC<TradingDashboardRefactoredProps> = ({
  className,
  initialTab = 'summary',
}) => {
  return <TradingDashboardContainer className={className} initialTab={initialTab} />;
};

export default TradingDashboardRefactored;
