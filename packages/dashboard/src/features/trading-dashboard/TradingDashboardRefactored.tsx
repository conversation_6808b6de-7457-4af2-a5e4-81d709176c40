/**
 * TradingDashboardRefactored Component
 * 
 * PHASE 1 IMPLEMENTATION: Using extracted F1Header, DashboardTabs, and Context
 * This is a working version using the newly extracted components.
 * 
 * PROGRESS:
 * ✅ F1Header - Extracted and working
 * ✅ DashboardTabs - Extracted and working  
 * ✅ TradingDashboardContext - Basic structure created
 * 🔄 useTradingDashboardData - Next phase
 * 🔄 QuickTradeForm - Next phase
 * 🔄 TradingDashboardContainer - Final phase
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  TradingDashboardProvider, 
  useTradingDashboardTabs,
  useTradingDashboardSession,
  useTradingDashboardData
} from './context/TradingDashboardContext';
import { F1Header, DashboardTabs, TabType } from './components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const ContentArea = styled.div`
  min-height: 400px;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;

const PlaceholderContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const TabContent = styled.div`
  animation: fadeIn 0.3s ease-in-out;
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`;

/**
 * Content component that uses the context
 */
const TradingDashboardContent: React.FC = () => {
  const { activeTab, setActiveTab } = useTradingDashboardTabs();
  const { isLive, sessionName } = useTradingDashboardSession();
  const { refreshData, isLoading } = useTradingDashboardData();
  
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
      // Simulate refresh delay for demo
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setIsRefreshing(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'summary':
        return (
          <TabContent>
            <h2>📊 Summary Dashboard</h2>
            <PlaceholderContent>
              <div>🏎️ F1 Racing Dashboard Summary</div>
              <div>Performance metrics, recent trades, and key insights will appear here.</div>
              <div style={{ fontSize: '12px', opacity: 0.7 }}>
                This content will be populated in Phase 2 with the data hook implementation.
              </div>
            </PlaceholderContent>
          </TabContent>
        );
      
      case 'trades':
        return (
          <TabContent>
            <h2>📈 Trades</h2>
            <PlaceholderContent>
              <div>📋 Complete Trades Table</div>
              <div>All your trades with filtering, sorting, and detailed analysis.</div>
              <div style={{ fontSize: '12px', opacity: 0.7 }}>
                Trade data will be loaded from IndexedDB in Phase 2.
              </div>
            </PlaceholderContent>
          </TabContent>
        );
      
      case 'setups':
        return (
          <TabContent>
            <h2>🎯 Setup Analysis</h2>
            <PlaceholderContent>
              <div>📊 Setup Performance Analysis</div>
              <div>Performance breakdown by setup type, pattern quality, and more.</div>
              <div style={{ fontSize: '12px', opacity: 0.7 }}>
                Setup analysis will be calculated from trade data in Phase 2.
              </div>
            </PlaceholderContent>
          </TabContent>
        );
      
      case 'analytics':
        return (
          <TabContent>
            <h2>🔬 Analytics & Quick Trade</h2>
            <PlaceholderContent>
              <div>📈 Advanced Analytics + Quick Trade Entry</div>
              <div>Combined view with analytics and quick trade form.</div>
              <div style={{ fontSize: '12px', opacity: 0.7 }}>
                QuickTradeForm component will be extracted in Phase 3.
              </div>
            </PlaceholderContent>
          </TabContent>
        );
      
      default:
        return (
          <PlaceholderContent>
            <div>❓ Unknown Tab</div>
            <div>This tab content is not implemented yet.</div>
          </PlaceholderContent>
        );
    }
  };

  if (isLoading) {
    return (
      <DashboardContainer>
        <PlaceholderContent>
          <LoadingSpinner size="lg" />
          <div>Loading Trading Dashboard...</div>
        </PlaceholderContent>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      {/* ✅ EXTRACTED: F1Header Component */}
      <F1Header
        isLive={isLive}
        sessionName={sessionName}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
      />

      {/* ✅ EXTRACTED: DashboardTabs Component */}
      <DashboardTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        persistState={true}
        syncWithUrl={false}
      />

      {/* Content Area */}
      <ContentArea>
        {renderTabContent()}
      </ContentArea>

      {/* Phase Progress Indicator */}
      <div style={{ 
        fontSize: '12px', 
        color: '#9ca3af', 
        textAlign: 'center',
        padding: '16px',
        borderTop: '1px solid #4b5563',
        marginTop: '16px'
      }}>
        🚀 <strong>Phase 1 Complete:</strong> F1Header ✅ | DashboardTabs ✅ | Context ✅ | 
        <strong> Next:</strong> Data Hook (Phase 2) → Form (Phase 3) → Container (Phase 3)
      </div>
    </DashboardContainer>
  );
};

/**
 * TradingDashboardRefactored Component
 * 
 * Main component that provides context and renders the dashboard content.
 * This demonstrates the new architecture with extracted components.
 */
export const TradingDashboardRefactored: React.FC = () => {
  return (
    <TradingDashboardProvider>
      <TradingDashboardContent />
    </TradingDashboardProvider>
  );
};

export default TradingDashboardRefactored;
