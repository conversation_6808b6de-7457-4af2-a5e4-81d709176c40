/**
 * TradingDashboardRefactored Component
 *
 * PHASE 1 IMPLEMENTATION: Using extracted F1Header, DashboardTabs, and Context
 * This is a working version using the newly extracted components.
 *
 * PROGRESS:
 * ✅ F1Header - Extracted and working
 * ✅ DashboardTabs - Extracted and working
 * ✅ TradingDashboardContext - Basic structure created
 * 🔄 useTradingDashboardData - Next phase
 * 🔄 QuickTradeForm - Next phase
 * 🔄 TradingDashboardContainer - Final phase
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import {
  TradingDashboardProvider,
  useTradingDashboardTabs,
  useTradingDashboardSession,
  useTradingDashboardData,
} from './context/TradingDashboardContext';
import { F1Header, DashboardTabs, TabType } from './components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const ContentArea = styled.div`
  min-height: 400px;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;

const PlaceholderContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const MetricCard = styled.div`
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  text-align: center;
`;

const MetricTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const MetricValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xl || '20px'};
  font-weight: ${({ theme }) => theme.fontWeights?.bold || '700'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const DataSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '18px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  padding-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const TradesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  max-height: 300px;
  overflow-y: auto;
`;

const TradeItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;

const TradeInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xxs || '2px'};
`;

const TradePnL = styled.div<{ $positive: boolean }>`
  color: ${({ theme, $positive }) =>
    $positive ? theme.colors?.profit || '#4caf50' : theme.colors?.loss || '#f44336'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
`;

const TabContent = styled.div`
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

/**
 * Content component that uses the context
 */
const TradingDashboardContent: React.FC = () => {
  const { activeTab, setActiveTab } = useTradingDashboardTabs();
  const { isLive, sessionName } = useTradingDashboardSession();
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    refreshData,
    isLoading,
    error,
    lastUpdated,
  } = useTradingDashboardData();

  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
      // Simulate refresh delay for demo
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } finally {
      setIsRefreshing(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'summary':
        return (
          <TabContent>
            <SectionTitle>📊 Performance Metrics</SectionTitle>
            <MetricsGrid>
              {performanceMetrics.map((metric, index) => (
                <MetricCard key={index}>
                  <MetricTitle>{metric.title}</MetricTitle>
                  <MetricValue>{metric.value}</MetricValue>
                </MetricCard>
              ))}
            </MetricsGrid>

            <DataSection>
              <SectionTitle>📈 Recent Trades ({trades.length} total)</SectionTitle>
              {trades.length > 0 ? (
                <TradesList>
                  {trades.slice(0, 5).map((trade) => (
                    <TradeItem key={trade.id}>
                      <TradeInfo>
                        <div>
                          <strong>{trade.setup}</strong> - {trade.market}
                        </div>
                        <div>
                          {trade.date} | {trade.session}
                        </div>
                      </TradeInfo>
                      <TradePnL $positive={trade.pnl >= 0}>${trade.pnl.toFixed(2)}</TradePnL>
                    </TradeItem>
                  ))}
                </TradesList>
              ) : (
                <PlaceholderContent style={{ height: '150px' }}>
                  <div>No trades found</div>
                  <div>Start trading to see your performance metrics!</div>
                </PlaceholderContent>
              )}
            </DataSection>

            {lastUpdated && (
              <div
                style={{
                  fontSize: '12px',
                  color: '#9ca3af',
                  textAlign: 'center',
                  marginTop: '16px',
                }}
              >
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
            )}
          </TabContent>
        );

      case 'trades':
        return (
          <TabContent>
            <SectionTitle>📈 All Trades ({trades.length} total)</SectionTitle>
            {trades.length > 0 ? (
              <TradesList>
                {trades.map((trade) => (
                  <TradeItem key={trade.id}>
                    <TradeInfo>
                      <div>
                        <strong>{trade.setup}</strong> - {trade.market} {trade.direction}
                      </div>
                      <div>
                        {trade.date} | {trade.session} | Entry: {trade.entry}
                      </div>
                      <div>
                        R-Multiple: {trade.rMultiple.toFixed(2)} | Quality: {trade.patternQuality}
                      </div>
                    </TradeInfo>
                    <TradePnL $positive={trade.pnl >= 0}>${trade.pnl.toFixed(2)}</TradePnL>
                  </TradeItem>
                ))}
              </TradesList>
            ) : (
              <PlaceholderContent>
                <div>📋 No trades found</div>
                <div>Your trading history will appear here once you start logging trades.</div>
              </PlaceholderContent>
            )}
          </TabContent>
        );

      case 'setups':
        return (
          <TabContent>
            <SectionTitle>🎯 Setup Performance Analysis</SectionTitle>
            {setupPerformance.length > 0 ? (
              <DataSection>
                <TradesList>
                  {setupPerformance.map((setup, index) => (
                    <TradeItem key={index}>
                      <TradeInfo>
                        <div>
                          <strong>{setup.name}</strong>
                        </div>
                        <div>
                          Win Rate: {setup.winRate.toFixed(1)}% | Trades: {setup.totalTrades}
                        </div>
                        <div>Avg R-Multiple: {setup.avgRMultiple.toFixed(2)}</div>
                      </TradeInfo>
                      <TradePnL $positive={setup.pnl >= 0}>${setup.pnl.toFixed(2)}</TradePnL>
                    </TradeItem>
                  ))}
                </TradesList>
              </DataSection>
            ) : (
              <PlaceholderContent>
                <div>🎯 No setup data available</div>
                <div>Setup performance analysis will appear here once you have trade data.</div>
              </PlaceholderContent>
            )}

            <SectionTitle>📊 Session Performance Analysis</SectionTitle>
            {sessionPerformance.length > 0 ? (
              <DataSection>
                <TradesList>
                  {sessionPerformance.map((session, index) => (
                    <TradeItem key={index}>
                      <TradeInfo>
                        <div>
                          <strong>{session.name}</strong>
                        </div>
                        <div>
                          Win Rate: {session.winRate.toFixed(1)}% | Trades: {session.totalTrades}
                        </div>
                        <div>Avg R-Multiple: {session.avgRMultiple.toFixed(2)}</div>
                      </TradeInfo>
                      <TradePnL $positive={session.pnl >= 0}>${session.pnl.toFixed(2)}</TradePnL>
                    </TradeItem>
                  ))}
                </TradesList>
              </DataSection>
            ) : (
              <PlaceholderContent>
                <div>📊 No session data available</div>
                <div>Session performance analysis will appear here once you have trade data.</div>
              </PlaceholderContent>
            )}
          </TabContent>
        );

      case 'analytics':
        return (
          <TabContent>
            <h2>🔬 Analytics & Quick Trade</h2>
            <PlaceholderContent>
              <div>📈 Advanced Analytics + Quick Trade Entry</div>
              <div>Combined view with analytics and quick trade form.</div>
              <div style={{ fontSize: '12px', opacity: 0.7 }}>
                QuickTradeForm component will be extracted in Phase 3.
              </div>
            </PlaceholderContent>
          </TabContent>
        );

      default:
        return (
          <PlaceholderContent>
            <div>❓ Unknown Tab</div>
            <div>This tab content is not implemented yet.</div>
          </PlaceholderContent>
        );
    }
  };

  if (isLoading) {
    return (
      <DashboardContainer>
        <PlaceholderContent>
          <LoadingSpinner size="lg" />
          <div>Loading Trading Dashboard...</div>
        </PlaceholderContent>
      </DashboardContainer>
    );
  }

  if (error) {
    return (
      <DashboardContainer>
        <PlaceholderContent>
          <div style={{ color: '#f44336' }}>❌ Error Loading Dashboard</div>
          <div>{error}</div>
          <button
            onClick={handleRefresh}
            style={{
              marginTop: '16px',
              padding: '8px 16px',
              background: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            Retry
          </button>
        </PlaceholderContent>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      {/* ✅ EXTRACTED: F1Header Component */}
      <F1Header
        isLive={isLive}
        sessionName={sessionName}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
      />

      {/* ✅ EXTRACTED: DashboardTabs Component */}
      <DashboardTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        persistState={true}
        syncWithUrl={false}
      />

      {/* Content Area */}
      <ContentArea>{renderTabContent()}</ContentArea>

      {/* Phase Progress Indicator */}
      <div
        style={{
          fontSize: '12px',
          color: '#9ca3af',
          textAlign: 'center',
          padding: '16px',
          borderTop: '1px solid #4b5563',
          marginTop: '16px',
        }}
      >
        🚀 <strong>Phase 2 Complete:</strong> F1Header ✅ | DashboardTabs ✅ | Context ✅ | Data
        Hook ✅ |<strong>Real Data Integration ✅</strong> |<strong> Next:</strong> Form (Phase 3) →
        Container (Phase 3) → Migration
      </div>
    </DashboardContainer>
  );
};

/**
 * TradingDashboardRefactored Component
 *
 * Main component that provides context and renders the dashboard content.
 * This demonstrates the new architecture with extracted components.
 */
export const TradingDashboardRefactored: React.FC = () => {
  return (
    <TradingDashboardProvider>
      <TradingDashboardContent />
    </TradingDashboardProvider>
  );
};

export default TradingDashboardRefactored;
