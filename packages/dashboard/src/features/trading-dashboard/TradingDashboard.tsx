/**
 * Trading Dashboard Component
 *
 * Main component for the Trading Dashboard feature
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradingDashboard } from './hooks/useTradingDashboard';
import MetricsPanel from './components/MetricsPanel';
import PerformanceChart from './components/PerformanceChart';
import RecentTradesTable from './components/RecentTradesTable';
import SetupAnalysis from './components/SetupAnalysis';
import { TradeFormBasicFields } from '../trade-journal/components/trade-form';

// Tab types - Enhanced F1 Racing Dashboard
type TabType = 'summary' | 'trades' | 'setups' | 'analytics';

const DashboardContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textPrimary};
  background: ${({ theme }) => theme.colors.background};
  min-height: 100vh;
`;

// F1 Racing Header
const F1Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  padding: ${({ theme }) => theme.spacing.lg};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.surface} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, ${({ theme }) => theme.colors.primary} 0%, transparent 100%);
  }
`;

// F1 Title
const F1Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.h2};
  font-weight: bold;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;

  span {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

// Live Indicator
const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.primary};
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes.sm};

  &::before {
    content: '●';
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid #4b5563;
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  margin: 0;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const StatusBadge = styled.span`
  background-color: #4b5563;
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 500;
  margin-left: ${({ theme }) => theme.spacing.sm};
`;

// F1 Racing Tab Navigation - Clean Horizontal Style
const TabsContainer = styled.div`
  display: flex;
  gap: 0;
  margin: ${({ theme }) => theme.spacing.lg} 0 ${({ theme }) => theme.spacing.xl} 0;
  border-bottom: 1px solid #4b5563;
  position: relative;
`;

const Tab = styled.button<{ active: boolean }>`
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  border: none;
  background: transparent;
  color: ${({ active, theme }) => (active ? theme.colors.textPrimary : '#9ca3af')};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.normal};
  font-weight: ${({ active }) => (active ? '600' : '400')};
  font-size: ${({ theme }) => theme.fontSizes.md};
  position: relative;
  border-bottom: 2px solid transparent;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ theme }) => theme.colors.primary};
    transform: scaleX(${({ active }) => (active ? 1 : 0)});
    transition: transform ${({ theme }) => theme.transitions.normal};
  }

  &:hover {
    color: ${({ active, theme }) => (active ? theme.colors.textPrimary : theme.colors.textPrimary)};

    &::after {
      transform: scaleX(1);
      background: ${({ active, theme }) => (active ? theme.colors.primary : '#9ca3af')};
    }
  }
`;

const RefreshButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
  }
`;

const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal};
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

// Analytics Tab Layout
const AnalyticsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
  }
`;

const ChartsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const TradeFormSection = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing.lg};
  border: 1px solid #4b5563;
  height: fit-content;
`;

const FormTitle = styled.h3`
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: bold;
`;

/**
 * TradingDashboard Component
 *
 * Main component for the Trading Dashboard feature
 */
export const TradingDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('summary');

  // Simple trade form state for Analytics tab
  const [tradeFormValues, setTradeFormValues] = useState<TradeFormData>({
    date: new Date().toISOString().split('T')[0],
    symbol: 'MNQ',
    direction: 'long',
    quantity: 1,
    entryPrice: 0,
    exitPrice: 0,
    profit: 0,
    model: '',
    session: '',
    setup: '',
    patternQuality: '',
    dolTarget: '',
    rdType: '',
    drawOnLiquidity: '',
    entryVersion: '',
    notes: '',
    tags: [],
  });

  const handleTradeFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setTradeFormValues((prev) => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    }));
  };
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    fetchDashboardData,
  } = useTradingDashboard();

  const handleRefresh = () => {
    fetchDashboardData();
  };

  return (
    <DashboardContainer>
      {/* F1 Racing Header */}
      <F1Header>
        <F1Title>
          🏎️ TRADING <span>2025</span> DASHBOARD
        </F1Title>
        <LiveIndicator>LIVE SESSION</LiveIndicator>
      </F1Header>

      <Header>
        <div>
          <Title>Trading Dashboard</Title>
          <StatusBadge>SESSION 1</StatusBadge>
        </div>
        <RefreshButton onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </RefreshButton>
      </Header>

      <TabsContainer>
        <Tab active={activeTab === 'summary'} onClick={() => setActiveTab('summary')}>
          Summary
        </Tab>
        <Tab active={activeTab === 'trades'} onClick={() => setActiveTab('trades')}>
          Trades
        </Tab>
        <Tab active={activeTab === 'setups'} onClick={() => setActiveTab('setups')}>
          Setups
        </Tab>
        <Tab active={activeTab === 'analytics'} onClick={() => setActiveTab('analytics')}>
          Analytics
        </Tab>
      </TabsContainer>

      {error && <div style={{ color: 'red', marginBottom: '16px' }}>Error: {error}</div>}

      {activeTab === 'summary' && (
        <>
          <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
          <PerformanceChart data={chartData} isLoading={isLoading} />
          <RecentTradesTable trades={trades.slice(0, 5)} isLoading={isLoading} />
        </>
      )}

      {activeTab === 'trades' && <RecentTradesTable trades={trades} isLoading={isLoading} />}

      {activeTab === 'setups' && (
        <SetupAnalysis
          setupPerformance={setupPerformance}
          sessionPerformance={sessionPerformance}
          isLoading={isLoading}
        />
      )}

      {activeTab === 'analytics' && (
        <AnalyticsContainer>
          <ChartsSection>
            <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
            <PerformanceChart data={chartData} isLoading={isLoading} />
            <SetupAnalysis
              setupPerformance={setupPerformance}
              sessionPerformance={sessionPerformance}
              isLoading={isLoading}
            />
          </ChartsSection>

          <TradeFormSection>
            <FormTitle>🏎️ Quick Trade Entry</FormTitle>
            <TradeFormBasicFields
              formValues={tradeFormValues}
              handleChange={handleTradeFormChange}
              validationErrors={{}}
            />
          </TradeFormSection>
        </AnalyticsContainer>
      )}

      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}
    </DashboardContainer>
  );
};

export default TradingDashboard;
