/**
 * DashboardTabs Component
 * 
 * EXTRACTED FROM: TradingDashboard.tsx (lines 122-162 + 320-333)
 * F1 racing-themed tab navigation with accessibility and persistence.
 * 
 * BENEFITS:
 * - Reusable tab navigation component
 * - F1 racing aesthetic with red accents
 * - Keyboard navigation support
 * - URL synchronization capability
 * - Local storage persistence
 */

import React, { useEffect } from 'react';
import styled, { css } from 'styled-components';

export type TabType = 'summary' | 'trades' | 'setups' | 'analytics';

export interface Tab {
  id: TabType;
  label: string;
  icon?: string;
  disabled?: boolean;
}

export interface DashboardTabsProps {
  /** Currently active tab */
  activeTab: TabType;
  /** Callback when tab changes */
  onTabChange: (tab: TabType) => void;
  /** Available tabs */
  tabs?: Tab[];
  /** Custom className */
  className?: string;
  /** Whether to persist tab state in localStorage */
  persistState?: boolean;
  /** Storage key for persistence */
  storageKey?: string;
  /** Whether to sync with URL */
  syncWithUrl?: boolean;
}

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
  overflow-x: auto;
  
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

const TabButton = styled.button<{ $active: boolean; $disabled: boolean }>`
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing?.md || '12px'} ${theme.spacing?.lg || '16px'}`};
  font-size: ${({ theme }) => theme.fontSizes?.md || '16px'};
  font-weight: ${({ theme, $active }) =>
    $active 
      ? theme.fontWeights?.semibold || '600'
      : theme.fontWeights?.regular || '400'
  };
  color: ${({ theme, $active, $disabled }) => {
    if ($disabled) return theme.colors?.textDisabled || '#6b7280';
    return $active 
      ? theme.colors?.primary || '#dc2626'
      : theme.colors?.textSecondary || '#9ca3af';
  }};
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  border-bottom: 2px solid ${({ theme, $active }) => 
    $active 
      ? theme.colors?.primary || '#dc2626'
      : 'transparent'
  };
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  position: relative;

  /* F1 racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
      opacity: 0.5;
      transition: opacity 0.2s ease;
    }
  }

  /* Focus styles for accessibility */
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors?.primary || '#dc2626'};
    border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  }

  /* Active state enhancement */
  ${({ $active, theme }) =>
    $active &&
    css`
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background: ${theme.colors?.primary || '#dc2626'};
        border-radius: 50%;
      }
    `}

  /* Disabled state */
  ${({ $disabled }) =>
    $disabled &&
    css`
      opacity: 0.5;
      pointer-events: none;
    `}
`;

const TabIcon = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;

const TabLabel = styled.span`
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;

/**
 * Default tabs configuration
 */
const defaultTabs: Tab[] = [
  { id: 'summary', label: 'Summary', icon: '📊' },
  { id: 'trades', label: 'Trades', icon: '📈' },
  { id: 'setups', label: 'Setups', icon: '🎯' },
  { id: 'analytics', label: 'Analytics', icon: '🔬' },
];

/**
 * DashboardTabs Component
 * 
 * A reusable F1 racing-themed tab navigation component with accessibility,
 * persistence, and URL synchronization capabilities.
 * 
 * @example
 * ```typescript
 * <DashboardTabs
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 *   persistState={true}
 *   syncWithUrl={true}
 * />
 * ```
 */
export const DashboardTabs: React.FC<DashboardTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
  className,
  persistState = true,
  storageKey = 'trading-dashboard-active-tab',
  syncWithUrl = false,
}) => {
  // Load persisted tab state on mount
  useEffect(() => {
    if (!persistState) return;

    try {
      const savedTab = localStorage.getItem(storageKey);
      if (savedTab && tabs.some(tab => tab.id === savedTab)) {
        onTabChange(savedTab as TabType);
      }
    } catch (error) {
      console.warn('Failed to load persisted tab state:', error);
    }
  }, [persistState, storageKey, onTabChange, tabs]);

  // Persist tab state when it changes
  useEffect(() => {
    if (!persistState) return;

    try {
      localStorage.setItem(storageKey, activeTab);
    } catch (error) {
      console.warn('Failed to persist tab state:', error);
    }
  }, [activeTab, persistState, storageKey]);

  // URL synchronization (basic implementation)
  useEffect(() => {
    if (!syncWithUrl) return;

    const url = new URL(window.location.href);
    url.searchParams.set('tab', activeTab);
    window.history.replaceState({}, '', url.toString());
  }, [activeTab, syncWithUrl]);

  /**
   * Handle tab click with validation
   */
  const handleTabClick = (tabId: TabType) => {
    const tab = tabs.find(t => t.id === tabId);
    if (tab && !tab.disabled) {
      onTabChange(tabId);
    }
  };

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = (event: React.KeyboardEvent, tabId: TabType) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTabClick(tabId);
    } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      event.preventDefault();
      
      const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
      const direction = event.key === 'ArrowLeft' ? -1 : 1;
      let nextIndex = currentIndex + direction;
      
      // Wrap around
      if (nextIndex < 0) nextIndex = tabs.length - 1;
      if (nextIndex >= tabs.length) nextIndex = 0;
      
      // Skip disabled tabs
      while (tabs[nextIndex]?.disabled && nextIndex !== currentIndex) {
        nextIndex += direction;
        if (nextIndex < 0) nextIndex = tabs.length - 1;
        if (nextIndex >= tabs.length) nextIndex = 0;
      }
      
      if (!tabs[nextIndex]?.disabled) {
        handleTabClick(tabs[nextIndex].id);
      }
    }
  };

  return (
    <TabsContainer className={className} role="tablist">
      {tabs.map((tab) => (
        <TabButton
          key={tab.id}
          $active={activeTab === tab.id}
          $disabled={tab.disabled || false}
          onClick={() => handleTabClick(tab.id)}
          onKeyDown={(e) => handleKeyDown(e, tab.id)}
          role="tab"
          aria-selected={activeTab === tab.id}
          aria-controls={`tabpanel-${tab.id}`}
          tabIndex={activeTab === tab.id ? 0 : -1}
          disabled={tab.disabled}
          aria-label={`${tab.label} tab`}
        >
          {tab.icon && <TabIcon>{tab.icon}</TabIcon>}
          <TabLabel>{tab.label}</TabLabel>
        </TabButton>
      ))}
    </TabsContainer>
  );
};

export default DashboardTabs;
