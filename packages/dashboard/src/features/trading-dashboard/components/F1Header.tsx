/**
 * F1Header Component
 * 
 * EXTRACTED FROM: TradingDashboard.tsx (lines 28-94 + 302-308)
 * F1 racing-themed header with live session indicator and refresh functionality.
 * 
 * BENEFITS:
 * - Reusable F1-themed header across features
 * - Consistent live session indicator
 * - Centralized refresh functionality
 * - Responsive design with F1 aesthetic
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';

export interface F1HeaderProps {
  /** Whether the trading session is live */
  isLive?: boolean;
  /** Name of the current trading session */
  sessionName?: string;
  /** Refresh callback function */
  onRefresh?: () => void;
  /** Whether refresh is in progress */
  isRefreshing?: boolean;
  /** Custom className for styling */
  className?: string;
  /** Additional header actions */
  actions?: React.ReactNode;
}

const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors?.surface || '#1f2937'} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  position: relative;
  overflow: hidden;

  /* F1 racing accent line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || '#dc2626'} 0%,
      transparent 100%
    );
  }

  /* Responsive design */
  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
    text-align: center;
  }
`;

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const MainTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '24px'};
  font-weight: ${({ theme }) => theme.fontWeights?.bold || '700'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;

  span {
    color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }
`;

const LiveIndicator = styled.div<{ $isLive: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ theme, $isLive }) => 
    $isLive 
      ? theme.colors?.primary || '#dc2626'
      : theme.colors?.textSecondary || '#9ca3af'
  };
  font-weight: ${({ theme }) => theme.fontWeights?.bold || '700'};
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};

  &::before {
    content: '●';
    animation: ${({ $isLive }) => $isLive ? 'f1-pulse 2s infinite' : 'none'};
  }

  @keyframes f1-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const SessionInfo = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
`;

const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const RefreshButton = styled.button<{ $isRefreshing: boolean }>`
  background-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  padding: ${({ theme }) => `${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'}`};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  min-width: 100px;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors?.border || '#4b5563'};
    cursor: not-allowed;
    transform: none;
  }

  ${({ $isRefreshing }) =>
    $isRefreshing &&
    css`
      pointer-events: none;
    `}
`;

const RefreshIcon = styled.span<{ $isRefreshing: boolean }>`
  display: inline-block;
  transition: transform 0.3s ease;
  
  ${({ $isRefreshing }) =>
    $isRefreshing &&
    css`
      animation: spin 1s linear infinite;
    `}

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

/**
 * F1Header Component
 * 
 * A reusable F1 racing-themed header component with live session indicator
 * and refresh functionality. Extracted from TradingDashboard for reusability.
 * 
 * @example
 * ```typescript
 * <F1Header
 *   isLive={true}
 *   sessionName="London Session"
 *   onRefresh={handleRefresh}
 *   isRefreshing={isLoading}
 * />
 * ```
 */
export const F1Header: React.FC<F1HeaderProps> = ({
  isLive = false,
  sessionName = 'Trading Session',
  onRefresh,
  isRefreshing = false,
  className,
  actions,
}) => {
  return (
    <HeaderContainer className={className}>
      <TitleSection>
        <MainTitle>
          🏎️ TRADING <span>2025</span> DASHBOARD
        </MainTitle>
        <LiveIndicator $isLive={isLive}>
          {isLive ? 'LIVE SESSION' : 'SESSION CLOSED'}
        </LiveIndicator>
        {sessionName && (
          <SessionInfo>
            {sessionName}
          </SessionInfo>
        )}
      </TitleSection>

      <ActionsSection>
        {actions}
        {onRefresh && (
          <RefreshButton
            onClick={onRefresh}
            disabled={isRefreshing}
            $isRefreshing={isRefreshing}
            aria-label={isRefreshing ? 'Refreshing data' : 'Refresh data'}
          >
            {isRefreshing ? (
              <>
                <LoadingSpinner size="xs" variant="white" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshIcon $isRefreshing={false}>🔄</RefreshIcon>
                Refresh Data
              </>
            )}
          </RefreshButton>
        )}
      </ActionsSection>
    </HeaderContainer>
  );
};

export default F1Header;
