/**
 * TradingDashboard with Feature Flag Support
 * 
 * MIGRATION STRATEGY: Gradual rollout with feature flags
 * Allows safe A/B testing between original and refactored versions.
 * 
 * BENEFITS:
 * - Zero-risk deployment
 * - Gradual user migration
 * - Easy rollback capability
 * - Performance comparison
 * - User feedback collection
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

// Original implementation
import TradingDashboardOriginal from './TradingDashboard';

// Refactored implementation
import { TradingDashboardContainer } from './components/TradingDashboardContainer';

export interface FeatureFlagConfig {
  /** Whether to enable the refactored dashboard */
  enableRefactoredDashboard: boolean;
  /** Percentage of users to show refactored version (0-100) */
  rolloutPercentage: number;
  /** Specific user IDs to always show refactored version */
  allowlistedUsers: string[];
  /** Whether to allow manual toggle for testing */
  allowManualToggle: boolean;
}

export interface TradingDashboardWithFeatureFlagProps {
  /** Feature flag configuration */
  featureFlags?: Partial<FeatureFlagConfig>;
  /** User ID for rollout percentage calculation */
  userId?: string;
  /** Custom className */
  className?: string;
}

const FeatureFlagContainer = styled.div`
  position: relative;
`;

const FeatureFlagToggle = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '12px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  box-shadow: ${({ theme }) => theme.shadows?.md || '0 4px 6px rgba(0, 0, 0, 0.1)'};
`;

const ToggleButton = styled.button<{ $active: boolean }>`
  background: ${({ theme, $active }) => 
    $active 
      ? theme.colors?.primary || '#dc2626'
      : theme.colors?.background || '#0f0f0f'
  };
  color: ${({ theme, $active }) => 
    $active 
      ? 'white'
      : theme.colors?.textSecondary || '#9ca3af'
  };
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  margin: 0 ${({ theme }) => theme.spacing?.xxs || '2px'};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '12px'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:hover {
    background: ${({ theme, $active }) => 
      $active 
        ? theme.colors?.primaryDark || '#b91c1c'
        : theme.colors?.surface || '#1f2937'
    };
  }
`;

const VersionBadge = styled.div<{ $version: 'original' | 'refactored' }>`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: ${({ theme, $version }) => 
    $version === 'refactored' 
      ? theme.colors?.success || '#4caf50'
      : theme.colors?.warning || '#ff9800'
  };
  color: white;
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '12px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  box-shadow: ${({ theme }) => theme.shadows?.md || '0 4px 6px rgba(0, 0, 0, 0.1)'};
`;

/**
 * Default feature flag configuration
 */
const defaultFeatureFlags: FeatureFlagConfig = {
  enableRefactoredDashboard: false,
  rolloutPercentage: 0,
  allowlistedUsers: [],
  allowManualToggle: process.env.NODE_ENV === 'development',
};

/**
 * Feature flag utilities
 */
const FeatureFlagUtils = {
  /**
   * Check if user should see refactored version based on rollout percentage
   */
  shouldShowRefactored: (userId: string, rolloutPercentage: number): boolean => {
    if (rolloutPercentage === 0) return false;
    if (rolloutPercentage === 100) return true;
    
    // Use user ID hash for consistent assignment
    const hash = FeatureFlagUtils.hashString(userId);
    return (hash % 100) < rolloutPercentage;
  },

  /**
   * Simple string hash function for consistent user assignment
   */
  hashString: (str: string): number => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  },

  /**
   * Get feature flag configuration from environment or localStorage
   */
  getFeatureFlags: (): Partial<FeatureFlagConfig> => {
    try {
      // Check localStorage for development overrides
      const localFlags = localStorage.getItem('trading-dashboard-feature-flags');
      if (localFlags) {
        return JSON.parse(localFlags);
      }
    } catch (error) {
      console.warn('Failed to parse feature flags from localStorage:', error);
    }

    // Check environment variables
    return {
      enableRefactoredDashboard: process.env.REACT_APP_ENABLE_REFACTORED_DASHBOARD === 'true',
      rolloutPercentage: parseInt(process.env.REACT_APP_ROLLOUT_PERCENTAGE || '0', 10),
      allowManualToggle: process.env.NODE_ENV === 'development',
    };
  },

  /**
   * Save feature flag overrides to localStorage (development only)
   */
  saveFeatureFlags: (flags: Partial<FeatureFlagConfig>): void => {
    if (process.env.NODE_ENV === 'development') {
      try {
        localStorage.setItem('trading-dashboard-feature-flags', JSON.stringify(flags));
      } catch (error) {
        console.warn('Failed to save feature flags to localStorage:', error);
      }
    }
  },
};

/**
 * TradingDashboard with Feature Flag Support
 * 
 * Provides gradual migration between original and refactored implementations
 * with feature flags, rollout percentage, and manual toggle for testing.
 */
export const TradingDashboardWithFeatureFlag: React.FC<TradingDashboardWithFeatureFlagProps> = ({
  featureFlags: propFeatureFlags = {},
  userId = 'anonymous',
  className,
}) => {
  // Merge feature flags from props, environment, and localStorage
  const mergedFlags: FeatureFlagConfig = {
    ...defaultFeatureFlags,
    ...FeatureFlagUtils.getFeatureFlags(),
    ...propFeatureFlags,
  };

  // Determine if user should see refactored version
  const shouldShowRefactored = 
    mergedFlags.enableRefactoredDashboard &&
    (mergedFlags.allowlistedUsers.includes(userId) ||
     FeatureFlagUtils.shouldShowRefactored(userId, mergedFlags.rolloutPercentage));

  const [useRefactored, setUseRefactored] = useState(shouldShowRefactored);
  const [showToggle, setShowToggle] = useState(mergedFlags.allowManualToggle);

  // Update when feature flags change
  useEffect(() => {
    setUseRefactored(shouldShowRefactored);
  }, [shouldShowRefactored]);

  // Handle manual toggle (development only)
  const handleToggle = (version: 'original' | 'refactored') => {
    if (!mergedFlags.allowManualToggle) return;
    
    const newUseRefactored = version === 'refactored';
    setUseRefactored(newUseRefactored);
    
    // Save override to localStorage
    FeatureFlagUtils.saveFeatureFlags({
      ...mergedFlags,
      enableRefactoredDashboard: newUseRefactored,
      rolloutPercentage: newUseRefactored ? 100 : 0,
    });
  };

  // Log feature flag decision for debugging
  useEffect(() => {
    console.log('TradingDashboard Feature Flag Decision:', {
      userId,
      useRefactored,
      featureFlags: mergedFlags,
      shouldShowRefactored,
    });
  }, [userId, useRefactored, mergedFlags, shouldShowRefactored]);

  return (
    <FeatureFlagContainer className={className}>
      {/* Development Toggle */}
      {showToggle && (
        <FeatureFlagToggle>
          <div style={{ marginBottom: '4px' }}>Dashboard Version:</div>
          <div>
            <ToggleButton
              $active={!useRefactored}
              onClick={() => handleToggle('original')}
            >
              Original
            </ToggleButton>
            <ToggleButton
              $active={useRefactored}
              onClick={() => handleToggle('refactored')}
            >
              Refactored
            </ToggleButton>
          </div>
        </FeatureFlagToggle>
      )}

      {/* Version Badge */}
      <VersionBadge $version={useRefactored ? 'refactored' : 'original'}>
        {useRefactored ? '🚀 Refactored' : '📊 Original'}
      </VersionBadge>

      {/* Render appropriate version */}
      {useRefactored ? (
        <TradingDashboardContainer />
      ) : (
        <TradingDashboardOriginal />
      )}
    </FeatureFlagContainer>
  );
};

export default TradingDashboardWithFeatureFlag;
