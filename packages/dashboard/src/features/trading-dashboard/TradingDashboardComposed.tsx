/**
 * Trading Dashboard Component (Composition Pattern)
 *
 * REFACTORED VERSION: Uses composition pattern instead of direct imports
 * to reduce coupling and improve maintainability.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { DashboardSection, TradeMetrics } from '@adhd-trading-dashboard/shared';
import { useTradingDashboard } from './hooks/useTradingDashboard';

// Tab types - Enhanced F1 Racing Dashboard
type TabType = 'summary' | 'trades' | 'setups' | 'analytics';

const DashboardContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textPrimary};
  background: ${({ theme }) => theme.colors.background};
  min-height: 100vh;
`;

// F1 Racing Header
const F1Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  padding: ${({ theme }) => theme.spacing.lg};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.surface} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, ${({ theme }) => theme.colors.primary} 0%, transparent 100%);
  }
`;

const F1Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.h2};
  font-weight: bold;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;

  span {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.primary};
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes.sm};

  &::before {
    content: '●';
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

// F1 Racing Tab Navigation
const TabsContainer = styled.div`
  display: flex;
  gap: 0;
  margin: ${({ theme }) => theme.spacing.lg} 0 ${({ theme }) => theme.spacing.xl} 0;
  border-bottom: 1px solid #4b5563;
  position: relative;
`;

const Tab = styled.button<{ active: boolean }>`
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  border: none;
  background: transparent;
  color: ${({ active, theme }) => (active ? theme.colors.textPrimary : '#9ca3af')};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.normal};
  font-weight: ${({ active }) => (active ? '600' : '400')};
  font-size: ${({ theme }) => theme.fontSizes.md};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ theme }) => theme.colors.primary};
    transform: scaleX(${({ active }) => (active ? 1 : 0)});
    transition: transform ${({ theme }) => theme.transitions.normal};
  }

  &:hover::after {
    transform: scaleX(1);
    background: ${({ active, theme }) => (active ? theme.colors.primary : '#9ca3af')};
  }
`;

const RefreshButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
  }
`;

/**
 * TradingDashboard Component (Composition Pattern)
 *
 * IMPROVED: Uses DashboardSection components for composition instead of
 * directly importing feature-specific components.
 */
export const TradingDashboardComposed: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('summary');

  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    fetchDashboardData,
  } = useTradingDashboard();

  // Transform data for TradeMetrics component
  const metricsData = performanceMetrics ? [
    { label: 'Total Trades', value: performanceMetrics.totalTrades },
    { label: 'Win Rate', value: `${performanceMetrics.winRate.toFixed(1)}%`, positive: performanceMetrics.winRate > 50 },
    { label: 'Total P&L', value: `$${performanceMetrics.totalPnL.toFixed(2)}`, positive: performanceMetrics.totalPnL > 0, negative: performanceMetrics.totalPnL < 0 },
    { label: 'Avg R-Multiple', value: performanceMetrics.avgRMultiple.toFixed(2), positive: performanceMetrics.avgRMultiple > 1 },
  ] : [];

  const handleRefresh = () => {
    fetchDashboardData();
  };

  return (
    <DashboardContainer>
      {/* F1 Racing Header */}
      <F1Header>
        <F1Title>
          🏎️ TRADING <span>2025</span> DASHBOARD
        </F1Title>
        <LiveIndicator>LIVE SESSION</LiveIndicator>
      </F1Header>

      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h1 style={{ margin: 0, fontSize: '2rem' }}>Trading Dashboard</h1>
        <RefreshButton onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </RefreshButton>
      </div>

      <TabsContainer>
        <Tab active={activeTab === 'summary'} onClick={() => setActiveTab('summary')}>
          Summary
        </Tab>
        <Tab active={activeTab === 'trades'} onClick={() => setActiveTab('trades')}>
          Trades
        </Tab>
        <Tab active={activeTab === 'setups'} onClick={() => setActiveTab('setups')}>
          Setups
        </Tab>
        <Tab active={activeTab === 'analytics'} onClick={() => setActiveTab('analytics')}>
          Analytics
        </Tab>
      </TabsContainer>

      {error && <div style={{ color: 'red', marginBottom: '16px' }}>Error: {error}</div>}

      {/* COMPOSITION PATTERN: Using DashboardSection components */}
      {activeTab === 'summary' && (
        <>
          <DashboardSection 
            name="metrics" 
            title="Performance Metrics"
            isLoading={isLoading}
            error={error}
          >
            <TradeMetrics metrics={metricsData} isLoading={isLoading} />
          </DashboardSection>

          <DashboardSection 
            name="performance" 
            title="Performance Chart"
            isLoading={isLoading}
            error={error}
          >
            {/* Chart component would go here */}
            <div>Performance chart placeholder</div>
          </DashboardSection>

          <DashboardSection 
            name="recent-trades" 
            title="Recent Trades"
            isLoading={isLoading}
            error={error}
          >
            {/* Recent trades table would go here */}
            <div>Recent trades table placeholder</div>
          </DashboardSection>
        </>
      )}

      {activeTab === 'trades' && (
        <DashboardSection 
          name="all-trades" 
          title="All Trades"
          isLoading={isLoading}
          error={error}
        >
          {/* All trades table would go here */}
          <div>All trades table placeholder</div>
        </DashboardSection>
      )}

      {activeTab === 'setups' && (
        <DashboardSection 
          name="setup-analysis" 
          title="Setup Analysis"
          isLoading={isLoading}
          error={error}
        >
          {/* Setup analysis would go here */}
          <div>Setup analysis placeholder</div>
        </DashboardSection>
      )}

      {activeTab === 'analytics' && (
        <>
          <DashboardSection 
            name="analytics-metrics" 
            title="Analytics Metrics"
            isLoading={isLoading}
            error={error}
          >
            <TradeMetrics metrics={metricsData} isLoading={isLoading} />
          </DashboardSection>

          <DashboardSection 
            name="analytics-charts" 
            title="Analytics Charts"
            isLoading={isLoading}
            error={error}
          >
            {/* Analytics charts would go here */}
            <div>Analytics charts placeholder</div>
          </DashboardSection>
        </>
      )}
    </DashboardContainer>
  );
};

export default TradingDashboardComposed;
