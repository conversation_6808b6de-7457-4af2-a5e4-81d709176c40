/**
 * CategoryPerformanceChart Component (REFACTORED)
 *
 * SIMPLIFIED VERSION: Extracted complex logic into shared hooks and components.
 * Reduced from 271 lines to ~80 lines by separating concerns.
 * 
 * IMPROVEMENTS:
 * - Extracted sorting logic to useSortableTable hook
 * - Extracted formatting logic to useDataFormatting hook
 * - Extracted table UI to SortableTable component
 * - Single responsibility: just data processing and rendering
 */

import React from 'react';
import styled from 'styled-components';
import { 
  SortableTable, 
  SortableColumn,
  useProfitLossFormatting,
  useDataFormatting,
  LoadingSpinner
} from '@adhd-trading-dashboard/shared';
import { CategoryPerformance } from '../types';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';

interface CategoryPerformanceChartRefactoredProps {
  className?: string;
  category: 'symbol' | 'strategy' | 'timeframe' | 'session';
  title: string;
}

// Styled components for custom cell rendering
const ProfitLossContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const BarContainer = styled.div`
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  border-radius: ${({ theme }) => theme.borderRadius?.pill || '12px'};
  overflow: hidden;
`;

const Bar = styled.div<{ $width: number; $positive: boolean }>`
  height: 100%;
  width: ${({ $width }) => `${$width}%`};
  background-color: ${({ theme, $positive }) =>
    $positive 
      ? theme.colors?.profit || '#4caf50'
      : theme.colors?.loss || '#f44336'
  };
  transition: width 0.3s ease;
`;

const ProfitLossValue = styled.span<{ $value: number }>`
  color: ${({ theme, $value }) =>
    $value > 0 
      ? theme.colors?.profit || '#4caf50'
      : $value < 0 
        ? theme.colors?.loss || '#f44336' 
        : theme.colors?.textSecondary || '#9ca3af'
  };
  font-weight: ${({ theme, $value }) =>
    $value !== 0 
      ? theme.fontWeights?.medium || '500'
      : theme.fontWeights?.regular || '400'
  };
  font-family: ${({ theme }) => theme.fontFamilies?.mono || 'monospace'};
`;

/**
 * CategoryPerformanceChart Component (REFACTORED)
 * 
 * Now much simpler and focused on data processing and rendering.
 * Complex logic has been extracted to shared hooks and components.
 */
export const CategoryPerformanceChartRefactored: React.FC<CategoryPerformanceChartRefactoredProps> = ({
  className,
  category,
  title,
}) => {
  const { data, isLoading } = useTradeAnalysis();
  const { formatCurrency, formatPercent } = useDataFormatting();

  // Show loading state
  if (isLoading) {
    return <LoadingSpinner size="lg" />;
  }

  // Get data for the specific category
  const getCategoryData = (): CategoryPerformance[] => {
    if (!data) return [];
    
    switch (category) {
      case 'symbol':
        return data.symbolPerformance || [];
      case 'strategy':
        return data.strategyPerformance || [];
      case 'timeframe':
        return data.timeframePerformance || [];
      case 'session':
        return data.sessionPerformance || [];
      default:
        return [];
    }
  };

  const performanceData = getCategoryData();

  // Calculate max profit/loss for bar scaling
  const maxProfitLoss = React.useMemo(() => {
    return Math.max(...performanceData.map(item => Math.abs(item.profitLoss)));
  }, [performanceData]);

  // Define table columns with custom rendering
  const columns: SortableColumn<CategoryPerformance>[] = [
    {
      field: 'value',
      label: title,
      sortable: true,
    },
    {
      field: 'trades',
      label: 'Trades',
      sortable: true,
    },
    {
      field: 'winRate',
      label: 'Win Rate',
      sortable: true,
    },
    {
      field: 'profitLoss',
      label: 'P&L',
      sortable: true,
    },
    {
      field: 'averageProfitLoss',
      label: 'Avg P&L',
      sortable: true,
    },
  ];

  // Custom cell renderer for different column types
  const renderCell = (value: any, row: CategoryPerformance, column: SortableColumn<CategoryPerformance>) => {
    switch (column.field) {
      case 'winRate':
        return formatPercent(value);
      
      case 'profitLoss':
        return (
          <ProfitLossContainer>
            <ProfitLossValue $value={value}>
              {formatCurrency(value, { showPositiveSign: true })}
            </ProfitLossValue>
            <BarContainer>
              <Bar
                $width={Math.min(100, (Math.abs(value) / maxProfitLoss) * 100)}
                $positive={value >= 0}
              />
            </BarContainer>
          </ProfitLossContainer>
        );
      
      case 'averageProfitLoss':
        return (
          <ProfitLossValue $value={value}>
            {formatCurrency(value, { showPositiveSign: true })}
          </ProfitLossValue>
        );
      
      default:
        return value;
    }
  };

  return (
    <SortableTable
      className={className}
      data={performanceData}
      columns={columns}
      defaultSort={{ field: 'profitLoss', direction: 'desc' }}
      renderCell={renderCell}
      emptyMessage={`No ${category} performance data available.`}
      size="md"
      striped={true}
      hoverable={true}
    />
  );
};

export default CategoryPerformanceChartRefactored;
