/**
 * Trade Analysis Component (Composition Pattern)
 *
 * REFACTORED VERSION: Uses composition pattern with DashboardSection
 * to reduce coupling between individual analysis components.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { DashboardSection } from '@adhd-trading-dashboard/shared';
import { TradeAnalysisProvider, useTradeAnalysis } from './hooks/TradeAnalysisContext';
import { FilterPanel } from './components/FilterPanel';
import { PerformanceSummary } from './components/PerformanceSummary';
import { TradesTable } from './components/TradesTable';
import { CategoryPerformanceChart } from './components/CategoryPerformanceChart';
import { TimePerformanceChart } from './components/TimePerformanceChart';
import { TradeDetail } from './components/TradeDetail';

type ViewType = 'summary' | 'trades' | 'symbols' | 'strategies' | 'timeframes' | 'time';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const ViewTabs = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid #4b5563;
  padding-bottom: ${({ theme }) => theme.spacing.xs};
`;

const ViewTab = styled.button<{ active: boolean }>`
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: ${({ theme, active }) =>
    active ? theme.fontWeights.semibold : theme.fontWeights.regular};
  color: ${({ theme, active }) => (active ? theme.colors.primary : '#9ca3af')};
  cursor: pointer;
  border-bottom: 2px solid ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

const TradeAnalysisContent: React.FC = () => {
  const { data, isLoading, error, selectedTradeId, preferences, updatePreferences } =
    useTradeAnalysis();
  const [activeView, setActiveView] = useState<ViewType>(
    (preferences.defaultView as ViewType) || 'summary'
  );

  const handleViewChange = (view: ViewType) => {
    setActiveView(view);
    updatePreferences({ defaultView: view });
  };

  const renderContent = () => {
    switch (activeView) {
      case 'summary':
        return (
          <>
            {/* COMPOSITION PATTERN: Using DashboardSection instead of DataCard */}
            <DashboardSection 
              name="performance-summary" 
              title="Performance Summary"
              isLoading={isLoading}
              error={error}
            >
              <PerformanceSummary />
            </DashboardSection>

            <DashboardSection 
              name="time-of-day-performance" 
              title="Performance by Time of Day"
              isLoading={isLoading}
              error={error}
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DashboardSection>

            <DashboardSection 
              name="day-of-week-performance" 
              title="Performance by Day of Week"
              isLoading={isLoading}
              error={error}
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DashboardSection>
          </>
        );

      case 'trades':
        return (
          <>
            <DashboardSection 
              name="trades-table" 
              title="Trades"
              isLoading={isLoading}
              error={error}
            >
              <TradesTable />
            </DashboardSection>

            {selectedTradeId && (
              <DashboardSection 
                name="trade-detail" 
                title="Trade Detail"
                isLoading={isLoading}
                error={error}
              >
                <TradeDetail />
              </DashboardSection>
            )}
          </>
        );

      case 'symbols':
        return (
          <DashboardSection 
            name="symbol-performance" 
            title="Performance by Symbol"
            isLoading={isLoading}
            error={error}
          >
            <CategoryPerformanceChart category="symbol" title="Symbol" />
          </DashboardSection>
        );

      case 'strategies':
        return (
          <DashboardSection 
            name="strategy-performance" 
            title="Performance by Strategy"
            isLoading={isLoading}
            error={error}
          >
            <CategoryPerformanceChart category="strategy" title="Strategy" />
          </DashboardSection>
        );

      case 'timeframes':
        return (
          <>
            <DashboardSection 
              name="timeframe-performance" 
              title="Performance by Timeframe"
              isLoading={isLoading}
              error={error}
            >
              <CategoryPerformanceChart category="timeframe" title="Timeframe" />
            </DashboardSection>

            <DashboardSection 
              name="session-performance" 
              title="Performance by Session"
              isLoading={isLoading}
              error={error}
            >
              <CategoryPerformanceChart category="session" title="Session" />
            </DashboardSection>
          </>
        );

      case 'time':
        return (
          <>
            <DashboardSection 
              name="time-analysis-tod" 
              title="Performance by Time of Day"
              isLoading={isLoading}
              error={error}
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DashboardSection>

            <DashboardSection 
              name="time-analysis-dow" 
              title="Performance by Day of Week"
              isLoading={isLoading}
              error={error}
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DashboardSection>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Title>Trade Analysis</Title>
      </PageHeader>

      {/* Filter Panel as a DashboardSection */}
      <DashboardSection 
        name="filters" 
        title="Filters"
        collapsible={true}
        defaultCollapsed={false}
      >
        <FilterPanel />
      </DashboardSection>

      <ViewTabs>
        <ViewTab active={activeView === 'summary'} onClick={() => handleViewChange('summary')}>
          Summary
        </ViewTab>
        <ViewTab active={activeView === 'trades'} onClick={() => handleViewChange('trades')}>
          Trades
        </ViewTab>
        <ViewTab active={activeView === 'symbols'} onClick={() => handleViewChange('symbols')}>
          Symbols
        </ViewTab>
        <ViewTab
          active={activeView === 'strategies'}
          onClick={() => handleViewChange('strategies')}
        >
          Strategies
        </ViewTab>
        <ViewTab
          active={activeView === 'timeframes'}
          onClick={() => handleViewChange('timeframes')}
        >
          Timeframes
        </ViewTab>
        <ViewTab active={activeView === 'time'} onClick={() => handleViewChange('time')}>
          Time Analysis
        </ViewTab>
      </ViewTabs>

      {renderContent()}
    </PageContainer>
  );
};

const TradeAnalysisComposed: React.FC = () => {
  return (
    <TradeAnalysisProvider>
      <TradeAnalysisContent />
    </TradeAnalysisProvider>
  );
};

export default TradeAnalysisComposed;
