/**
 * Performance Dashboard Component (Composition Pattern)
 *
 * REFACTORED VERSION: Uses composition pattern with DashboardSection
 * to reduce coupling between individual dashboard components.
 */

import React, { useEffect } from "react";
import styled from "styled-components";
import { DashboardSection, Button } from '@adhd-trading-dashboard/shared';
import { MetricsPanel } from "./components/MetricsPanel";
import { PerformanceChart } from "./components/PerformanceChart";
import { RecentTradesPanel } from "./components/RecentTradesPanel";
import { useDashboardData } from "./hooks/useDashboardData";

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const RefreshButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
  }
`;

const DashboardComposed: React.FC = () => {
  const { metrics, chartData, recentTrades, isLoading, fetchDashboardData } =
    useDashboardData();

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const handleRefresh = () => {
    fetchDashboardData();
  };

  const refreshAction = (
    <RefreshButton onClick={handleRefresh} disabled={isLoading}>
      {isLoading ? 'Refreshing...' : '🔄 Refresh Data'}
    </RefreshButton>
  );

  return (
    <PageContainer>
      <PageHeader>
        <Title>Performance Dashboard</Title>
        {refreshAction}
      </PageHeader>

      {/* COMPOSITION PATTERN: Using DashboardSection for each component */}
      <DashboardSection 
        name="metrics" 
        title="Trading Metrics"
        isLoading={isLoading}
        actions={refreshAction}
        collapsible={true}
        defaultCollapsed={false}
      >
        <MetricsPanel metrics={metrics} isLoading={isLoading} />
      </DashboardSection>

      <DashboardSection 
        name="performance-chart" 
        title="Performance Chart"
        isLoading={isLoading}
        collapsible={true}
        defaultCollapsed={false}
      >
        <PerformanceChart data={chartData} />
      </DashboardSection>

      <DashboardSection 
        name="recent-trades" 
        title="Recent Trades"
        isLoading={isLoading}
        collapsible={true}
        defaultCollapsed={false}
      >
        <RecentTradesPanel trades={recentTrades} isLoading={isLoading} />
      </DashboardSection>
    </PageContainer>
  );
};

export default DashboardComposed;
