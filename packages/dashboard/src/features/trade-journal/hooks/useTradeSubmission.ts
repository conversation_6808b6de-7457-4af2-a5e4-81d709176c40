/**
 * Trade Submission Hook
 *
 * Custom hook for handling trade form submission
 */

import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { TradeFormValues } from '../types'; // These now import from centralized types
import { tradeStorageService, Trade } from '@adhd-trading-dashboard/shared';

/**
 * Hook for handling trade form submission
 * @param formValues The form values to submit
 * @param isEditMode Whether the form is in edit mode
 * @param isNewTrade Whether the form is for a new trade
 * @param tradeData The existing trade data (if editing)
 * @param validateBasicInfoTab Function to validate the basic info tab
 * @param validateCurrentTab Function to validate the current tab
 * @param activeTab The active tab
 * @param setActiveTab Function to set the active tab
 * @param setError Function to set the error message
 * @param setSuccess Function to set the success message
 */
export function useTradeSubmission(
  formValues: TradeFormValues,
  isEditMode: boolean,
  isNewTrade: boolean,
  tradeData: Trade | null,
  validateBasicInfoTab: () => boolean,
  validateCurrentTab: () => boolean,
  activeTab: string,
  setActiveTab: (tab: string) => void,
  setError: (error: string | null) => void,
  setSuccess: (success: string | null) => void
) {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle form submission
   * @param e The form event
   */
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Only validate the Basic Info tab for submission
      if (!validateBasicInfoTab()) {
        setError('Please complete the required fields in the Basic Info tab.');
        setActiveTab('basic'); // Switch to basic tab to show errors
        return;
      }

      // Validate current tab if it's not the basic tab
      if (activeTab !== 'basic' && !validateCurrentTab()) {
        setError('Please fix the validation errors in the current tab before submitting.');
        return;
      }

      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      try {
        // Prepare pattern quality score data if all criteria are filled
        const patternQualityData = {
          clarity: formValues.patternQualityClarity || '',
          confluence: formValues.patternQualityConfluence || '',
          context: formValues.patternQualityContext || '',
          risk: formValues.patternQualityRisk || '',
          reward: formValues.patternQualityReward || '',
          timeframe: formValues.patternQualityTimeframe || '',
          volume: formValues.patternQualityVolume || '',
        };

        // Check if all pattern quality criteria are filled
        const allCriteriaFilled = Object.values(patternQualityData).every((value) => value !== '');

        // Create a copy of form values for submission
        const submissionValues = { ...formValues };

        // Add pattern quality score data if all criteria are filled
        if (allCriteriaFilled) {
          // Import calculation functions
          const { calculateTotalScore, convertScoreToRating } = await import(
            '../constants/patternQuality'
          );

          // Calculate total score and rating
          const totalScore = calculateTotalScore(patternQualityData);
          const rating = convertScoreToRating(totalScore);

          // Add pattern quality score data to submission values
          submissionValues.patternQualityScore = {
            total: totalScore,
            rating,
            criteria: patternQualityData,
            notes: formValues.patternQualityNotes || '',
          };
        }

        // Add DOL analysis data if type is selected
        if (formValues.dolType) {
          // Add DOL analysis data to submission values
          submissionValues.dolAnalysis = {
            dolType: formValues.dolType,
            dolStrength: formValues.dolStrength || '',
            dolReaction: formValues.dolReaction || '',
            dolContext: formValues.dolContext || [],
            priceAction: formValues.dolPriceAction || '',
            volumeProfile: formValues.dolVolumeProfile || '',
            timeOfDay: formValues.dolTimeOfDay || '',
            marketStructure: formValues.dolMarketStructure || '',
            effectiveness: formValues.dolEffectiveness ? parseInt(formValues.dolEffectiveness) : 5,
            notes: formValues.dolNotes || '',
          };
        }

        console.log('Form submitted:', submissionValues);

        // Prepare trade data object using the new schema format
        const tradeRecord = {
          date: formValues.date,
          model_type: formValues.modelType || 'Unknown',
          session: formValues.session,
          direction: (formValues.direction === 'long' ? 'Long' : 'Short') as 'Long' | 'Short',
          market: formValues.market,
          entry_price: parseFloat(formValues.entryPrice) || 0,
          exit_price: parseFloat(formValues.exitPrice) || 0,
          r_multiple: formValues.rMultiple ? parseFloat(formValues.rMultiple) : undefined,
          achieved_pl: parseFloat(formValues.profit) || 0,
          win_loss: (formValues.result === 'win'
            ? 'Win'
            : formValues.result === 'loss'
            ? 'Loss'
            : undefined) as 'Win' | 'Loss' | undefined,
          pattern_quality_rating: formValues.patternQuality
            ? parseFloat(formValues.patternQuality)
            : undefined,
          entry_time: formValues.entryTime,
          exit_time: formValues.exitTime,
          rd_time: formValues.rdTime,
          risk_points: formValues.riskPoints ? parseFloat(formValues.riskPoints) : undefined,
          no_of_contracts: parseInt(formValues.quantity) || 0,
          notes: formValues.notes,
          // Setup Components (modular setup construction)
          setup_constant: formValues.setupComponents?.constant,
          setup_action: formValues.setupComponents?.action,
          setup_variable: formValues.setupComponents?.variable,
          setup_entry: formValues.setupComponents?.entry,
          setupComponents: formValues.setupComponents,
        };

        // Prepare FVG details if available
        const fvgDetails = formValues.entryVersion
          ? {
              trade_id: 0, // Will be set by the service
              rd_type: formValues.modelType,
              entry_version: formValues.entryVersion,
              draw_on_liquidity: formValues.specificDOLType,
            }
          : undefined;

        // Prepare setup classification if available
        const setupData =
          formValues.primarySetupType || formValues.liquidityTaken
            ? {
                trade_id: 0, // Will be set by the service
                primary_setup: formValues.primarySetupType,
                secondary_setup: formValues.secondarySetupType,
                liquidity_taken: formValues.liquidityTaken,
                additional_fvgs: formValues.additionalFVGs?.join(', '),
                dol: formValues.dolTargetType,
              }
            : undefined;

        // Prepare analysis data if available
        const analysisData =
          formValues.dolType || formValues.dolNotes
            ? {
                trade_id: 0, // Will be set by the service
                dol_target_type: formValues.dolTargetType,
                path_quality: formValues.dolPriceAction,
                clustering: formValues.dolVolumeProfile,
                dol_notes: formValues.dolNotes,
              }
            : undefined;

        const completeTradeData = {
          trade: tradeRecord,
          fvg_details: fvgDetails,
          setup: setupData,
          analysis: analysisData,
        };

        // Determine if we're in edit mode by checking if tradeData has an ID
        const isEditingExistingTrade = isEditMode && tradeData && tradeData.id;

        if (isEditingExistingTrade) {
          // Update existing trade
          const tradeId = typeof tradeData.id === 'string' ? parseInt(tradeData.id) : tradeData.id;
          await tradeStorageService.updateTradeWithDetails(tradeId, completeTradeData);
          console.log('Trade updated in IndexedDB successfully', tradeId);
        } else {
          // Create new trade
          const savedTradeId = await tradeStorageService.saveTradeWithDetails(completeTradeData);
          console.log('New trade saved to IndexedDB successfully', savedTradeId);
        }

        // Set success message based on whether we're editing or creating
        if (isEditMode) {
          setSuccess(`Trade for ${formValues.symbol} on ${formValues.date} updated successfully!`);
        } else {
          setSuccess(
            `New trade for ${formValues.symbol} on ${formValues.date} created successfully!`
          );
        }

        // Wait a moment to show the success message before navigating
        setTimeout(() => {
          // Navigate back to journal page after successful submission
          console.log('Navigating back to journal page after successful submission');
          navigate('/journal');
        }, 1500);
      } catch (err) {
        setError('Failed to save trade. Please try again.');
        console.error('Error submitting form:', err);
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      formValues,
      validateBasicInfoTab,
      validateCurrentTab,
      activeTab,
      setActiveTab,
      setError,
      setSuccess,
      isEditMode,
      tradeData,
      navigate,
    ]
  );

  return {
    handleSubmit,
    isSubmitting,
  };
}

export type TradeSubmissionHook = ReturnType<typeof useTradeSubmission>;
