/**
 * Trade Form Page
 *
 * This page displays a form for adding or editing a trade entry.
 * Uses a tabbed interface for progressive disclosure of form fields.
 */

import React from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradeForm } from './hooks/useTradeForm';
import { TabPanel } from '@adhd-trading-dashboard/shared';
import PatternQualityAssessment from './components/trade-pattern-quality/PatternQualityAssessment';
import DOLAnalysis from './components/trade-dol-analysis/TradeDOLAnalysis';
import {
  TradeFormHeader,
  TradeFormBasicFields,
  TradeFormTimingFields,
  TradeFormRiskFields,
  TradeFormStrategyFields,
  TradeFormActions,
  TradeFormMessages,
  TradeFormLoading,
} from './components/trade-form';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const ContentSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: relative; /* Required for absolute positioning of loading overlay */
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

/**
 * TradeForm Component
 *
 * Displays a form for adding or editing trade entries with a tabbed interface
 * for progressive disclosure of form fields.
 */
const TradeForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Enhanced debugging for ID parameter
  console.log(`TradeForm component mounted with ID parameter: "${id}"`);
  console.log(`Current URL: ${window.location.href}`);
  console.log(`Is edit mode: ${id && id !== 'new'}`);
  console.log(`Is new trade: ${id === 'new'}`);

  const {
    formValues,
    setFormValues,
    handleChange,
    handleSubmit,
    isSubmitting,
    isLoading,
    error,
    success,
    validationErrors,
    isNewTrade,
    activeTab,
    handleTabChange,
    calculateProfitLoss,
  } = useTradeForm(id);

  return (
    <PageContainer>
      <TradeFormHeader isNewTrade={isNewTrade} formValues={formValues} />

      <ContentSection>
        <TradeFormLoading isLoading={isLoading} />

        <Form onSubmit={handleSubmit}>
          <TradeFormMessages error={error} success={success} />

          <TabPanel
            tabs={[
              {
                id: 'basic',
                label: 'Basic Info*',
                content: (
                  <TradeFormBasicFields
                    formValues={formValues}
                    handleChange={handleChange}
                    validationErrors={validationErrors}
                    calculateProfitLoss={calculateProfitLoss}
                    setFormValues={setFormValues}
                  />
                ),
              },
              {
                id: 'timing',
                label: 'Timing',
                content: (
                  <TradeFormTimingFields
                    formValues={formValues}
                    handleChange={handleChange}
                    validationErrors={validationErrors}
                  />
                ),
              },
              {
                id: 'risk',
                label: 'Risk Management',
                content: (
                  <TradeFormRiskFields
                    formValues={formValues}
                    handleChange={handleChange}
                    validationErrors={validationErrors}
                  />
                ),
              },
              {
                id: 'strategy',
                label: 'Strategy',
                content: (
                  <TradeFormStrategyFields
                    formValues={formValues}
                    handleChange={handleChange}
                    validationErrors={validationErrors}
                    setFormValues={setFormValues}
                  />
                ),
              },
              {
                id: 'pattern-quality',
                label: 'Pattern Quality',
                content: (
                  <PatternQualityAssessment
                    formValues={formValues}
                    onChange={(field, value) => {
                      setFormValues((prev) => ({
                        ...prev,
                        [field]: value,
                      }));
                    }}
                  />
                ),
              },
              {
                id: 'dol-analysis',
                label: 'DOL Analysis',
                content: (
                  <DOLAnalysis
                    formValues={formValues}
                    onChange={(field, value) => {
                      setFormValues((prev) => ({
                        ...prev,
                        [field]: value,
                      }));
                    }}
                    validationErrors={validationErrors}
                  />
                ),
              },
            ]}
            defaultTab="basic"
            activeTab={activeTab}
            onTabClick={handleTabChange}
          />

          <TradeFormMessages error={null} success={null} showTabInfo={true} />

          <TradeFormActions
            isSubmitting={isSubmitting}
            isLoading={isLoading}
            isNewTrade={isNewTrade}
          />
        </Form>
      </ContentSection>
    </PageContainer>
  );
};

export default TradeForm;
