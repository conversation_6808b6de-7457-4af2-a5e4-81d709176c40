/**
 * DOL Analysis Component (Composition Pattern)
 * 
 * REFACTORED VERSION: Uses composition pattern with DashboardSection
 * to reduce coupling between individual DOL components.
 */

import React from 'react';
import styled from 'styled-components';
import { DashboardSection } from '@adhd-trading-dashboard/shared';
import {
  DOLTypeSelector,
  DOLStrengthSelector,
  DOLReactionSelector,
  DOLContextSelector,
  DOLDetailedAnalysis,
  DOLEffectivenessRating,
} from './index';

const AnalysisContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const Introduction = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
`;

const IntroTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const IntroText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const ValidationError = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

interface DOLAnalysisComposedProps {
  formValues: {
    dolType?: string;
    dolStrength?: string;
    dolReaction?: string;
    dolContext?: string[];
    dolPriceAction?: string;
    dolVolumeProfile?: string;
    dolTimeOfDay?: string;
    dolMarketStructure?: string;
    dolEffectiveness?: string;
    dolNotes?: string;
  };
  onChange: (field: string, value: any) => void;
  validationErrors: {
    [key: string]: string;
  };
}

const DOLAnalysisComposed: React.FC<DOLAnalysisComposedProps> = ({
  formValues,
  onChange,
  validationErrors,
}) => {
  return (
    <AnalysisContainer>
      <Introduction>
        <IntroTitle>Draw on Liquidity (DOL) Analysis</IntroTitle>
        <IntroText>
          Analyze how price interacted with liquidity levels in this trade. This analysis
          will help you understand market behavior around key levels and improve your
          ability to anticipate price movements.
        </IntroText>
      </Introduction>

      {/* Display validation errors if any */}
      {validationErrors.dolAnalysis && (
        <ValidationError>{validationErrors.dolAnalysis}</ValidationError>
      )}

      {/* COMPOSITION PATTERN: Using DashboardSection for each analysis component */}
      <DashboardSection 
        name="dol-type" 
        title="DOL Type"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLTypeSelector
          value={formValues.dolType || ''}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-strength" 
        title="DOL Strength"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLStrengthSelector
          value={formValues.dolStrength || ''}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-reaction" 
        title="DOL Reaction"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLReactionSelector
          value={formValues.dolReaction || ''}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-context" 
        title="DOL Context"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLContextSelector
          value={formValues.dolContext || []}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-detailed" 
        title="Detailed Analysis"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLDetailedAnalysis
          formValues={formValues}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-effectiveness" 
        title="Effectiveness Rating"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLEffectivenessRating
          value={formValues.dolEffectiveness || '5'}
          notes={formValues.dolNotes || ''}
          onChange={onChange}
        />
      </DashboardSection>
    </AnalysisContainer>
  );
};

export default DOLAnalysisComposed;
