/**
 * Setup Classification Component (Composition Pattern)
 * 
 * REFACTORED VERSION: Uses composition pattern with DashboardSection
 * to reduce coupling between individual setup classification components.
 */

import React from 'react';
import styled from 'styled-components';
import { DashboardSection } from '@adhd-trading-dashboard/shared';
import {
  PrimarySetupSelector,
  SecondarySetupSelector,
  LiquiditySelector,
  FVGSelector,
  DOLTargetSelector,
  ParentPDArraySelector,
} from './index';

const ClassificationContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const Introduction = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
`;

const IntroTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const IntroText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const ValidationError = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

interface SetupClassificationComposedProps {
  formValues: {
    primarySetupCategory?: string;
    primarySetupType?: string;
    secondarySetupCategory?: string;
    secondarySetupType?: string;
    liquidityTaken?: string;
    additionalFVGs?: string[];
    dolTargetType?: string;
    specificDOLType?: string;
    parentPDArray?: string;
  };
  onChange: (field: string, value: any) => void;
  validationErrors: {
    [key: string]: string;
  };
}

const SetupClassificationComposed: React.FC<SetupClassificationComposedProps> = ({
  formValues,
  onChange,
  validationErrors,
}) => {
  return (
    <ClassificationContainer>
      <Introduction>
        <IntroTitle>Setup Classification</IntroTitle>
        <IntroText>
          Classify your trade setup using the ICT methodology. This systematic approach
          helps identify patterns and improve your trading edge over time.
        </IntroText>
      </Introduction>

      {/* Display validation errors if any */}
      {validationErrors.setupClassification && (
        <ValidationError>{validationErrors.setupClassification}</ValidationError>
      )}

      {/* COMPOSITION PATTERN: Using DashboardSection for each classification component */}
      <DashboardSection 
        name="primary-setup" 
        title="Primary Setup"
        collapsible={true}
        defaultCollapsed={false}
      >
        <PrimarySetupSelector
          value={{
            category: formValues.primarySetupCategory,
            type: formValues.primarySetupType,
          }}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="secondary-setup" 
        title="Secondary Setup"
        collapsible={true}
        defaultCollapsed={false}
      >
        <SecondarySetupSelector
          value={{
            category: formValues.secondarySetupCategory,
            type: formValues.secondarySetupType,
          }}
          primarySetup={{
            category: formValues.primarySetupCategory,
            type: formValues.primarySetupType,
          }}
          onChange={onChange}
          error={validationErrors.secondarySetupType}
        />
      </DashboardSection>

      <DashboardSection 
        name="liquidity" 
        title="Liquidity Analysis"
        collapsible={true}
        defaultCollapsed={false}
      >
        <LiquiditySelector
          value={formValues.liquidityTaken || ''}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="fvg-analysis" 
        title="FVG Analysis"
        collapsible={true}
        defaultCollapsed={false}
      >
        <FVGSelector
          value={formValues.additionalFVGs || []}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="dol-target" 
        title="DOL Target"
        collapsible={true}
        defaultCollapsed={false}
      >
        <DOLTargetSelector
          value={{
            targetType: formValues.dolTargetType,
            specificType: formValues.specificDOLType,
          }}
          onChange={onChange}
        />
      </DashboardSection>

      <DashboardSection 
        name="parent-array" 
        title="Parent PD Array"
        collapsible={true}
        defaultCollapsed={false}
      >
        <ParentPDArraySelector
          value={formValues.parentPDArray || ''}
          onChange={onChange}
        />
      </DashboardSection>
    </ClassificationContainer>
  );
};

export default SetupClassificationComposed;
