/**
 * Setup Classification Components
 *
 * Centralized exports for all setup classification components to reduce coupling
 * and provide a single entry point for the setup classification feature.
 */

// Main component
export { default as SetupClassificationSection } from './SetupClassificationSection';

// Individual selector components
export { default as PrimarySetupSelector } from './PrimarySetupSelector';
export { default as SecondarySetupSelector } from './SecondarySetupSelector';
export { default as LiquiditySelector } from './LiquiditySelector';
export { default as FVGSelector } from './FVGSelector';
export { default as DOLTargetSelector } from './DOLTargetSelector';
export { default as ParentPDArraySelector } from './ParentPDArraySelector';

// Composed Setup Classification using DashboardSection pattern
export { default as SetupClassificationComposed } from './SetupClassificationComposed';
