/**
 * Setup Classification Section Component
 *
 * Main component for the setup classification section that combines all the individual components
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import {
  PrimarySetupSelector,
  SecondarySetupSelector,
  LiquiditySelector,
  FVGSelector,
  DOLTargetSelector,
  ParentPDArraySelector,
} from './index';

const SectionContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

const ValidationError = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

interface SetupClassificationSectionProps {
  formValues: {
    primarySetupCategory?: string;
    primarySetupType?: string;
    secondarySetupCategory?: string;
    secondarySetupType?: string;
    liquidityTaken?: string;
    additionalFVGs?: string[];
    dolTargetType?: string;
    specificDOLType?: string;
    parentPDArray?: string;
  };
  onChange: (field: string, value: any) => void;
  validationErrors: {
    [key: string]: string;
  };
}

const SetupClassificationSection: React.FC<SetupClassificationSectionProps> = ({
  formValues,
  onChange,
  validationErrors,
}) => {
  // Handle field change
  const handleFieldChange = (field: string, value: any) => {
    onChange(field, value);
  };

  return (
    <SectionContainer>
      {/* Display validation errors if any */}
      {validationErrors.setupClassification && (
        <ValidationError>{validationErrors.setupClassification}</ValidationError>
      )}

      {/* Primary Setup Selector */}
      <PrimarySetupSelector
        value={{
          category: formValues.primarySetupCategory,
          type: formValues.primarySetupType,
        }}
        onChange={handleFieldChange}
      />

      <Divider />

      {/* Secondary Setup Selector */}
      <SecondarySetupSelector
        value={{
          category: formValues.secondarySetupCategory,
          type: formValues.secondarySetupType,
        }}
        primarySetup={{
          category: formValues.primarySetupCategory,
          type: formValues.primarySetupType,
        }}
        onChange={handleFieldChange}
        error={validationErrors.secondarySetupType}
      />

      <Divider />

      {/* Liquidity Selector */}
      <LiquiditySelector value={formValues.liquidityTaken || ''} onChange={handleFieldChange} />

      <Divider />

      {/* FVG Selector */}
      <FVGSelector value={formValues.additionalFVGs || []} onChange={handleFieldChange} />

      <Divider />

      {/* DOL Target Selector */}
      <DOLTargetSelector
        value={{
          targetType: formValues.dolTargetType,
          specificType: formValues.specificDOLType,
        }}
        onChange={handleFieldChange}
      />

      <Divider />

      {/* Parent PD Array Selector */}
      <ParentPDArraySelector value={formValues.parentPDArray || ''} onChange={handleFieldChange} />
    </SectionContainer>
  );
};

export default SetupClassificationSection;
