/**
 * Trade Form Basic Fields Component
 *
 * Displays the basic information fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData, SetupComponents } from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import SetupBuilder from '../../../trade-entry/components/SetupBuilder';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;

interface TradeFormBasicFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  calculateProfitLoss?: () => void;
  setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}

/**
 * Trade Form Basic Fields Component
 */
const TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
  calculateProfitLoss,
  setFormValues,
}) => {
  // Handle changes that should trigger profit calculation
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    if (calculateProfitLoss) {
      // Use setTimeout to ensure the form values are updated before calculation
      setTimeout(calculateProfitLoss, 0);
    }
  };

  return (
    <>
      <FormRow>
        <FormGroup>
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            name="date"
            type="date"
            value={formValues.date}
            onChange={handleChange}
            required
          />
          {validationErrors.date && <ValidationError>{validationErrors.date}</ValidationError>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="symbol">Symbol</Label>
          <Input
            id="symbol"
            name="symbol"
            type="text"
            value={formValues.symbol}
            onChange={handleChange}
            required
          />
          {validationErrors.symbol && <ValidationError>{validationErrors.symbol}</ValidationError>}
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="direction">Direction</Label>
          <Select
            id="direction"
            name="direction"
            value={formValues.direction}
            onChange={handleChange}
            required
          >
            <option value="long">Long</option>
            <option value="short">Short</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="result">Result</Label>
          <Select
            id="result"
            name="result"
            value={formValues.result}
            onChange={handleChange}
            required
          >
            <option value="win">Win</option>
            <option value="loss">Loss</option>
            <option value="breakeven">Breakeven</option>
          </Select>
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="entryPrice">Entry Price</Label>
          <Input
            id="entryPrice"
            name="entryPrice"
            type="number"
            step="0.01"
            value={formValues.entryPrice}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.entryPrice && (
            <ValidationError>{validationErrors.entryPrice}</ValidationError>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="exitPrice">Exit Price</Label>
          <Input
            id="exitPrice"
            name="exitPrice"
            type="number"
            step="0.01"
            value={formValues.exitPrice}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.exitPrice && (
            <ValidationError>{validationErrors.exitPrice}</ValidationError>
          )}
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="quantity">Quantity</Label>
          <Input
            id="quantity"
            name="quantity"
            type="number"
            value={formValues.quantity}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.quantity && (
            <ValidationError>{validationErrors.quantity}</ValidationError>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="profit">Profit/Loss ($)</Label>
          <Input
            id="profit"
            name="profit"
            type="number"
            step="0.01"
            value={formValues.profit}
            onChange={handleChange}
            required
          />
        </FormGroup>
      </FormRow>

      {/* F1 Racing Dashboard Fields */}
      <FormRow>
        <FormGroup>
          <Label htmlFor="model">Model</Label>
          <Select id="model" name="model" value={formValues.model || ''} onChange={handleChange}>
            <option value="">Select Model</option>
            <option value="RD-Cont">RD-Cont</option>
            <option value="FVG-RD">FVG-RD</option>
            <option value="Combined">Combined</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="session">Session</Label>
          <Select
            id="session"
            name="session"
            value={formValues.session || ''}
            onChange={handleChange}
          >
            <option value="">Select Session</option>
            <option value="NY Open">NY Open</option>
            <option value="London Open">London Open</option>
            <option value="Lunch Macro">Lunch Macro</option>
            <option value="MOC">MOC</option>
            <option value="Overnight">Overnight</option>
          </Select>
        </FormGroup>
      </FormRow>

      {/* Setup Construction Matrix */}
      {setFormValues && (
        <SetupBuilder
          onSetupChange={(components: SetupComponents) => {
            setFormValues((prev) => ({
              ...prev,
              setupComponents: components,
            }));
          }}
          initialComponents={formValues.setupComponents}
        />
      )}

      <FormRow>
        <FormGroup>
          <Label htmlFor="patternQuality">Pattern Quality (1-10)</Label>
          <Input
            id="patternQuality"
            name="patternQuality"
            type="number"
            min="1"
            max="10"
            value={formValues.patternQuality || ''}
            onChange={handleChange}
          />
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="dolTarget">DOL Target</Label>
          <Input
            id="dolTarget"
            name="dolTarget"
            type="text"
            value={formValues.dolTarget || ''}
            onChange={handleChange}
            placeholder="Draw on Liquidity target"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="rdType">RD Type</Label>
          <Select id="rdType" name="rdType" value={formValues.rdType || ''} onChange={handleChange}>
            <option value="">Select RD Type</option>
            <option value="Bullish">Bullish</option>
            <option value="Bearish">Bearish</option>
            <option value="Neutral">Neutral</option>
          </Select>
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="drawOnLiquidity">Draw on Liquidity</Label>
          <Select
            id="drawOnLiquidity"
            name="drawOnLiquidity"
            value={formValues.drawOnLiquidity || ''}
            onChange={handleChange}
          >
            <option value="">Select DOL Status</option>
            <option value="Hit">Hit</option>
            <option value="Missed">Missed</option>
            <option value="Partial">Partial</option>
            <option value="Pending">Pending</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="entryVersion">Entry Version</Label>
          <Input
            id="entryVersion"
            name="entryVersion"
            type="text"
            value={formValues.entryVersion || ''}
            onChange={handleChange}
            placeholder="Entry version/iteration"
          />
        </FormGroup>
      </FormRow>
    </>
  );
};

export default TradeFormBasicFields;
