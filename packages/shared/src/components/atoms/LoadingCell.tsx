/**
 * LoadingCell Component
 * 
 * EXTRACTED FROM: ProfitLossCell.tsx (reducing complexity)
 * Reusable loading state component for table cells and similar contexts.
 */

import React from 'react';
import styled, { css } from 'styled-components';

interface LoadingCellProps {
  /** Size variant for different contexts */
  size?: 'small' | 'medium' | 'large';
  /** Custom width for the loading placeholder */
  width?: string;
  /** Custom className for styling */
  className?: string;
  /** Accessibility label */
  'aria-label'?: string;
}

interface StyledLoadingCellProps {
  $size: 'small' | 'medium' | 'large';
  $width?: string;
}

const StyledLoadingCell = styled.span<StyledLoadingCellProps>`
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  opacity: 0.6;
  position: relative;
  
  ${({ $size, theme }) => {
    switch ($size) {
      case 'small':
        return css`
          font-size: ${theme.fontSizes?.xs || '12px'};
          padding: ${theme.spacing?.xxs || '2px'} ${theme.spacing?.xs || '4px'};
        `;
      case 'large':
        return css`
          font-size: ${theme.fontSizes?.lg || '18px'};
          padding: ${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'};
        `;
      default:
        return css`
          font-size: ${theme.fontSizes?.sm || '14px'};
          padding: ${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'};
        `;
    }
  }}

  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
`;

const LoadingPlaceholder = styled.span<{ $width?: string }>`
  display: inline-block;
  width: ${({ $width }) => $width || '60px'};
  height: 1em;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 2px;
`;

/**
 * LoadingCell Component
 * 
 * A reusable loading state component for table cells and similar contexts.
 * Provides consistent loading animation and sizing across the application.
 */
export const LoadingCell: React.FC<LoadingCellProps> = ({
  size = 'medium',
  width,
  className,
  'aria-label': ariaLabel,
}) => {
  return (
    <StyledLoadingCell
      className={className}
      $size={size}
      $width={width}
      aria-label={ariaLabel || 'Loading data'}
      role="cell"
      aria-busy="true"
    >
      <LoadingPlaceholder $width={width} />
    </StyledLoadingCell>
  );
};

export default LoadingCell;
