/**
 * Input Component
 *
 * A customizable input component that follows the design system.
 */
import React, { useState, useRef } from 'react';
import styled, { css } from 'styled-components';

export type InputSize = 'small' | 'medium' | 'large';

// Create a custom type that extends the HTML input attributes but overrides the size property
type CustomInputHTMLAttributes = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  'onChange' | 'size'
> & {
  size?: InputSize;
};

export interface InputProps extends CustomInputHTMLAttributes {
  /** The value of the input */
  value: string;
  /** Function called when the input value changes */
  onChange: (value: string) => void;
  /** The placeholder text */
  placeholder?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** The error message */
  error?: string;
  /** The input type */
  type?: string;
  /** The input name */
  name?: string;
  /** The input id */
  id?: string;
  /** Additional CSS class names */
  className?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Input autocomplete attribute */
  autoComplete?: string;
  /** Label for the input */
  label?: string;
  /** Helper text to display below the input */
  helperText?: string;
  /** Icon to display at the start of the input */
  startIcon?: React.ReactNode;
  /** Icon to display at the end of the input */
  endIcon?: React.ReactNode;
  /** Whether the input is in a loading state */
  loading?: boolean;
  /** Whether the input is in a success state */
  success?: boolean;
  /** Whether the input should have a clear button */
  clearable?: boolean;
  /** Function called when the input is cleared */
  onClear?: () => void;
  /** Maximum character count */
  maxLength?: number;
  /** Whether to show character count */
  showCharCount?: boolean;
  /** Size of the input */
  size?: 'small' | 'medium' | 'large';
  /** Whether the input should be full width */
  fullWidth?: boolean;
}

// Define the props that InputWrapper will accept
type InputWrapperProps = {
  fullWidth?: boolean;
};

const InputWrapper = styled.div<InputWrapperProps>`
  display: flex;
  flex-direction: column;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};
  position: relative;
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
`;

// Define the props that InputContainer will accept
type InputContainerProps = {
  hasError?: boolean;
  hasSuccess?: boolean;
  disabled?: boolean;
  $size?: InputSize;
  hasStartIcon?: boolean;
  hasEndIcon?: boolean;
  isFocused?: boolean;
};

const InputContainer = styled.div<InputContainerProps>`
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid
    ${({ theme, hasError, hasSuccess, isFocused }) => {
      if (hasError) return theme.colors.error;
      if (hasSuccess) return theme.colors.success;
      if (isFocused) return theme.colors.primary;
      return theme.colors.border;
    }};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};

  ${({ disabled, theme }) =>
    disabled &&
    css`
      opacity: 0.6;
      background-color: ${theme.colors.background};
      cursor: not-allowed;
    `}

  ${({ isFocused, theme, hasError, hasSuccess }) =>
    isFocused &&
    css`
      box-shadow: 0 0 0 2px
        ${hasError
          ? `${theme.colors.error}33`
          : hasSuccess
          ? `${theme.colors.success}33`
          : `${theme.colors.primary}33`};
    `}

  ${({ $size }) => {
    switch ($size) {
      case 'small':
        return css`
          height: 32px;
        `;
      case 'large':
        return css`
          height: 48px;
        `;
      default:
        return css`
          height: 40px;
        `;
    }
  }}
`;

const IconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

// Define the props that StyledInput will accept
type StyledInputProps = {
  hasStartIcon?: boolean;
  hasEndIcon?: boolean;
  $size?: InputSize;
};

const StyledInput = styled.input<StyledInputProps>`
  flex: 1;
  border: none;
  background: transparent;
  color: ${({ theme }) => theme.colors.textPrimary};
  width: 100%;
  outline: none;

  &:disabled {
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${({ theme }) => theme.colors.textDisabled};
  }

  ${({ hasStartIcon }) =>
    hasStartIcon &&
    css`
      padding-left: 0;
    `}

  ${({ hasEndIcon }) =>
    hasEndIcon &&
    css`
      padding-right: 0;
    `}

  ${({ $size, theme }) => {
    if ($size === 'small') {
      return css`
        font-size: ${theme.fontSizes.xs};
        padding: ${theme.spacing.xxs} ${theme.spacing.xs};
      `;
    } else if ($size === 'large') {
      return css`
        font-size: ${theme.fontSizes.md};
        padding: ${theme.spacing.sm} ${theme.spacing.md};
      `;
    } else {
      return css`
        font-size: ${theme.fontSizes.sm};
        padding: ${theme.spacing.xs} ${theme.spacing.sm};
      `;
    }
  }}
`;

const ClearButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.textDisabled};
  padding: 0 ${({ theme }) => theme.spacing.xs};
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${({ theme }) => theme.colors.textSecondary};
  }

  &:focus {
    outline: none;
  }
`;

const HelperTextContainer = styled.div<{ hasError?: boolean; hasSuccess?: boolean }>`
  display: flex;
  justify-content: space-between;
  margin-top: ${({ theme }) => theme.spacing.xxs};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError, hasSuccess }) => {
    if (hasError) return theme.colors.error;
    if (hasSuccess) return theme.colors.success;
    return theme.colors.textSecondary;
  }};
`;

/**
 * Input Component
 *
 * A customizable input component that follows the design system.
 */
export const Input: React.FC<InputProps> = ({
  value,
  onChange,
  placeholder = '',
  disabled = false,
  error = '',
  type = 'text',
  name = '',
  id = '',
  className = '',
  required = false,
  autoComplete = '',
  label = '',
  helperText = '',
  startIcon,
  endIcon,
  loading = false,
  success = false,
  clearable = false,
  onClear,
  maxLength,
  showCharCount = false,
  size = 'medium' as InputSize,
  fullWidth = false,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClear = () => {
    if (onClear) {
      onClear();
    } else {
      onChange('');
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    if (rest.onFocus) {
      rest.onFocus(e);
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    if (rest.onBlur) {
      rest.onBlur(e);
    }
  };

  // Clear button should only show when there's a value and the input is not disabled
  const showClearButton = clearable && value && !disabled;

  // Character count
  const charCount = value?.length || 0;
  const showCount = showCharCount || (maxLength !== undefined && maxLength > 0);

  return (
    <InputWrapper className={className} fullWidth={fullWidth}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && ' *'}
        </Label>
      )}

      <InputContainer
        hasError={!!error}
        hasSuccess={!!success}
        disabled={!!disabled}
        $size={size}
        hasStartIcon={!!startIcon}
        hasEndIcon={!!(endIcon || showClearButton)}
        isFocused={!!isFocused}
      >
        {startIcon && <IconContainer>{startIcon}</IconContainer>}

        <StyledInput
          ref={inputRef}
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={!!(disabled || loading)}
          name={name}
          id={id}
          required={!!required}
          autoComplete={autoComplete}
          hasStartIcon={!!startIcon}
          hasEndIcon={!!(endIcon || showClearButton)}
          // Pass size as a custom prop to avoid conflict with HTML input size
          $size={size}
          maxLength={maxLength}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...rest}
        />

        {showClearButton && (
          <ClearButton type="button" onClick={handleClear} tabIndex={-1}>
            ✕
          </ClearButton>
        )}

        {endIcon && <IconContainer>{endIcon}</IconContainer>}
      </InputContainer>

      {(error || helperText || showCount) && (
        <HelperTextContainer hasError={!!error} hasSuccess={!!success}>
          <div>{error || helperText}</div>
          {showCount && (
            <div>
              {charCount}
              {maxLength !== undefined && `/${maxLength}`}
            </div>
          )}
        </HelperTextContainer>
      )}
    </InputWrapper>
  );
};
