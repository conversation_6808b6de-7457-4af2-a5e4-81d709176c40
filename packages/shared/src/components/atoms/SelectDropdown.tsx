/**
 * SelectDropdown Component
 *
 * A reusable dropdown component for selecting from a list of options.
 * MOVED TO SHARED: This component was being imported across multiple features.
 */

import React from "react";
import styled from "styled-components";

interface SelectOption {
  value: string;
  label: string;
}

interface SelectDropdownProps {
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: SelectOption[];
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

const SelectContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: border-color ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.chartGrid};
    cursor: not-allowed;
  }
`;

/**
 * SelectDropdown Component
 *
 * A reusable dropdown component for selecting from a list of options.
 */
const SelectDropdown: React.FC<SelectDropdownProps> = ({
  id,
  name,
  value,
  onChange,
  options,
  label,
  required = false,
  disabled = false,
  className,
  placeholder,
}) => {
  return (
    <SelectContainer className={className}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && <span style={{ color: "red" }}> *</span>}
        </Label>
      )}
      <Select
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>
    </SelectContainer>
  );
};

export default SelectDropdown;
