/**
 * Loading Placeholder Component
 *
 * A component for displaying loading states with customizable appearance.
 */
import React from 'react';
import styled, { css, keyframes } from 'styled-components';

export type LoadingPlaceholderVariant = 'default' | 'card' | 'text' | 'list';
export type LoadingPlaceholderSize = 'small' | 'medium' | 'large' | 'custom';

export interface LoadingPlaceholderProps {
  /** The variant of the loading placeholder */
  variant?: LoadingPlaceholderVariant;
  /** The size of the loading placeholder */
  size?: LoadingPlaceholderSize;
  /** Custom height (only used when size is 'custom') */
  height?: string;
  /** Custom width (only used when size is 'custom') */
  width?: string;
  /** Text to display in the loading placeholder */
  text?: string;
  /** Whether to show a spinner */
  showSpinner?: boolean;
  /** Additional CSS class names */
  className?: string;
}

// Size styles
const sizeStyles = {
  small: css`
    height: 100px;
  `,
  medium: css`
    height: 200px;
  `,
  large: css`
    height: 300px;
  `,
  custom: (props: { customHeight: string; customWidth?: string }) => css`
    height: ${props.customHeight};
    width: ${props.customWidth || '100%'};
  `,
};

// Variant styles
const variantStyles = {
  default: css`
    background-color: ${({ theme }) => theme.colors.background};
    border-radius: ${({ theme }) => theme.borderRadius.md};
  `,
  card: css`
    background-color: ${({ theme }) => theme.colors.surface};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  `,
  text: css`
    background-color: transparent;
    height: auto !important;
    min-height: 1.5em;
  `,
  list: css`
    background-color: ${({ theme }) => theme.colors.background};
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  `,
};

// Spinner animation
const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const Container = styled.div<{
  variant: LoadingPlaceholderVariant;
  size: LoadingPlaceholderSize;
  customHeight: string;
  customWidth?: string;
}>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /* Apply size styles */
  ${({ size, customHeight, customWidth }) => {
    if (size === 'custom') {
      return sizeStyles.custom({ customHeight, customWidth });
    }
    return sizeStyles[size];
  }}

  /* Apply variant styles */
  ${({ variant }) => variantStyles[variant]}
`;

const Spinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.background};
  border-top: 3px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const Text = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

/**
 * Loading Placeholder Component
 *
 * A component for displaying loading states with customizable appearance.
 */
export const LoadingPlaceholder: React.FC<LoadingPlaceholderProps> = ({
  variant = 'default' as LoadingPlaceholderVariant,
  size = 'medium' as LoadingPlaceholderSize,
  height = '200px',
  width = '',
  text = 'Loading...',
  showSpinner = true,
  className = '',
}) => {
  return (
    <Container
      variant={variant}
      size={size}
      customHeight={height}
      customWidth={width}
      className={className}
    >
      {showSpinner && <Spinner />}
      {text && <Text>{text}</Text>}
    </Container>
  );
};
