/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
import React, { useState } from 'react';
import styled, { css } from 'styled-components';

export interface SelectOption {
  /** The value of the option */
  value: string;
  /** The label to display for the option */
  label: string;
  /** Whether the option is disabled */
  disabled?: boolean;
  /** Optional group for the option */
  group?: string;
}

export type SelectSize = 'small' | 'medium' | 'large';

// Create a custom type that extends the HTML select attributes but overrides the size property
type CustomSelectHTMLAttributes = Omit<
  React.SelectHTMLAttributes<HTMLSelectElement>,
  'onChange' | 'size'
> & {
  size?: SelectSize;
};

export interface SelectProps extends CustomSelectHTMLAttributes {
  /** The options to display */
  options: SelectOption[];
  /** The selected value */
  value: string;
  /** Function called when the selection changes */
  onChange: (value: string) => void;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** The error message */
  error?: string;
  /** The select name */
  name?: string;
  /** The select id */
  id?: string;
  /** Additional CSS class names */
  className?: string;
  /** Whether the select is required */
  required?: boolean;
  /** Placeholder text (first option) */
  placeholder?: string;
  /** Label for the select */
  label?: string;
  /** Helper text to display below the select */
  helperText?: string;
  /** Size of the select */
  size?: 'small' | 'medium' | 'large';
  /** Whether the select should be full width */
  fullWidth?: boolean;
  /** Whether the select is in a loading state */
  loading?: boolean;
  /** Whether the select is in a success state */
  success?: boolean;
  /** Icon to display at the start of the select */
  startIcon?: React.ReactNode;
}

// Define the props that SelectWrapper will accept
type SelectWrapperProps = {
  fullWidth?: boolean;
};

const SelectWrapper = styled.div<SelectWrapperProps>`
  display: flex;
  flex-direction: column;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};
  position: relative;
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
`;

// Define the props that SelectContainer will accept
type SelectContainerProps = {
  hasError?: boolean;
  hasSuccess?: boolean;
  disabled?: boolean;
  $size?: SelectSize;
  hasStartIcon?: boolean;
  isFocused?: boolean;
};

const SelectContainer = styled.div<SelectContainerProps>`
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid
    ${({ theme, hasError, hasSuccess, isFocused }) => {
      if (hasError) return theme.colors.error;
      if (hasSuccess) return theme.colors.success;
      if (isFocused) return theme.colors.primary;
      return theme.colors.border;
    }};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};

  ${({ disabled, theme }) =>
    disabled &&
    css`
      opacity: 0.6;
      background-color: ${theme.colors.background};
      cursor: not-allowed;
    `}

  ${({ isFocused, theme, hasError, hasSuccess }) =>
    isFocused &&
    css`
      box-shadow: 0 0 0 2px
        ${hasError
          ? `${theme.colors.error}33`
          : hasSuccess
          ? `${theme.colors.success}33`
          : `${theme.colors.primary}33`};
    `}

  ${({ $size }) => {
    switch ($size) {
      case 'small':
        return css`
          height: 32px;
        `;
      case 'large':
        return css`
          height: 48px;
        `;
      default:
        return css`
          height: 40px;
        `;
    }
  }}
`;

const IconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

// Define the props that StyledSelect will accept
type StyledSelectProps = {
  hasStartIcon?: boolean;
  $size?: SelectSize;
};

const StyledSelect = styled.select<StyledSelectProps>`
  flex: 1;
  border: none;
  background: transparent;
  color: ${({ theme }) => theme.colors.textPrimary};
  width: 100%;
  outline: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right ${({ theme }) => theme.spacing.sm} center;
  background-size: 16px;
  padding-right: ${({ theme }) => theme.spacing.xl};

  &:disabled {
    cursor: not-allowed;
  }

  ${({ hasStartIcon }) =>
    hasStartIcon &&
    css`
      padding-left: 0;
    `}

  ${({ $size, theme }) => {
    if ($size === 'small') {
      return css`
        font-size: ${theme.fontSizes.xs};
        padding: ${theme.spacing.xxs} ${theme.spacing.xs};
      `;
    } else if ($size === 'large') {
      return css`
        font-size: ${theme.fontSizes.md};
        padding: ${theme.spacing.sm} ${theme.spacing.md};
      `;
    } else {
      return css`
        font-size: ${theme.fontSizes.sm};
        padding: ${theme.spacing.xs} ${theme.spacing.sm};
      `;
    }
  }}
`;

const HelperTextContainer = styled.div<{ hasError?: boolean; hasSuccess?: boolean }>`
  display: flex;
  justify-content: space-between;
  margin-top: ${({ theme }) => theme.spacing.xxs};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError, hasSuccess }) => {
    if (hasError) return theme.colors.error;
    if (hasSuccess) return theme.colors.success;
    return theme.colors.textSecondary;
  }};
`;

const OptionGroup = styled.optgroup`
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
export const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  disabled = false,
  error = '',
  name = '',
  id = '',
  className = '',
  required = false,
  placeholder = '',
  label = '',
  helperText = '',
  size = 'medium' as SelectSize,
  fullWidth = true,
  loading = false,
  success = false,
  startIcon,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: React.FocusEvent<HTMLSelectElement>) => {
    setIsFocused(true);
    if (rest.onFocus) {
      rest.onFocus(e);
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLSelectElement>) => {
    setIsFocused(false);
    if (rest.onBlur) {
      rest.onBlur(e);
    }
  };

  // Group options by their group property if any
  const groupedOptions: Record<string, SelectOption[]> = {};
  const ungroupedOptions: SelectOption[] = [];

  options.forEach((option) => {
    if (option.group) {
      if (!groupedOptions[option.group]) {
        groupedOptions[option.group] = [];
      }
      groupedOptions[option.group].push(option);
    } else {
      ungroupedOptions.push(option);
    }
  });

  const hasGroups = Object.keys(groupedOptions).length > 0;

  return (
    <SelectWrapper className={className} fullWidth={fullWidth}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && ' *'}
        </Label>
      )}

      <SelectContainer
        hasError={!!error}
        hasSuccess={!!success}
        disabled={!!(disabled || loading)}
        $size={size}
        hasStartIcon={!!startIcon}
        isFocused={!!isFocused}
      >
        {startIcon && <IconContainer>{startIcon}</IconContainer>}

        <StyledSelect
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={!!(disabled || loading)}
          name={name}
          id={id}
          required={!!required}
          hasStartIcon={!!startIcon}
          // Pass size as a custom prop to avoid conflict with HTML select size
          $size={size}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...rest}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}

          {hasGroups ? (
            <>
              {/* Render ungrouped options first */}
              {ungroupedOptions.map((option) => (
                <option key={option.value} value={option.value} disabled={option.disabled}>
                  {option.label}
                </option>
              ))}

              {/* Then render grouped options */}
              {Object.entries(groupedOptions).map(([group, options]) => (
                <OptionGroup key={group} label={group}>
                  {options.map((option) => (
                    <option key={option.value} value={option.value} disabled={option.disabled}>
                      {option.label}
                    </option>
                  ))}
                </OptionGroup>
              ))}
            </>
          ) : (
            // Render all options without groups
            options.map((option) => (
              <option key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </option>
            ))
          )}
        </StyledSelect>
      </SelectContainer>

      {(error || helperText) && (
        <HelperTextContainer hasError={!!error} hasSuccess={!!success}>
          <div>{error || helperText}</div>
        </HelperTextContainer>
      )}
    </SelectWrapper>
  );
};
