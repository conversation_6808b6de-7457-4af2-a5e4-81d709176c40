/**
 * EnhancedFormField Component
 * 
 * ENHANCED VERSION: Uses the useFormField hook for advanced form behavior
 * while maintaining compatibility with the existing FormField component.
 * 
 * BENEFITS:
 * - Built-in validation and state management
 * - Consistent styling and behavior
 * - Automatic accessibility attributes
 * - Type-safe configuration
 * - Reduced boilerplate in forms
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { useFormField, FormFieldConfig, validationRules } from '../../hooks/useFormField';

export interface EnhancedFormFieldProps extends FormFieldConfig {
  /** Field name/id */
  name: string;
  /** Field label */
  label?: string;
  /** Placeholder text */
  placeholder?: string;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
  /** Field size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Help text to display below the field */
  helpText?: string;
  /** Input type for HTML input element */
  inputType?: 'input' | 'textarea' | 'select';
  /** Options for select fields */
  options?: Array<{ value: string; label: string }>;
  /** Number of rows for textarea */
  rows?: number;
  /** Callback when value changes */
  onChange?: (value: any) => void;
  /** Callback when field is blurred */
  onBlur?: () => void;
}

interface StyledFieldProps {
  $hasError: boolean;
  $disabled: boolean;
  $size: 'sm' | 'md' | 'lg';
}

const FieldContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const Label = styled.label<{ $required: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  
  ${({ $required }) =>
    $required &&
    css`
      &::after {
        content: ' *';
        color: ${({ theme }) => theme.colors?.error || '#dc2626'};
      }
    `}
`;

const baseInputStyles = css<StyledFieldProps>`
  width: 100%;
  border: 1px solid ${({ theme, $hasError }) => 
    $hasError 
      ? theme.colors?.error || '#dc2626'
      : theme.colors?.border || '#4b5563'
  };
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  background-color: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme, $size }) => {
    switch ($size) {
      case 'sm':
        return theme.fontSizes?.sm || '14px';
      case 'lg':
        return theme.fontSizes?.lg || '18px';
      default:
        return theme.fontSizes?.md || '16px';
    }
  }};
  padding: ${({ theme, $size }) => {
    switch ($size) {
      case 'sm':
        return `${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'}`;
      case 'lg':
        return `${theme.spacing?.md || '12px'} ${theme.spacing?.lg || '16px'}`;
      default:
        return `${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'}`;
    }
  }};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 2px ${({ theme }) => 
      theme.colors?.primary ? `${theme.colors.primary}20` : 'rgba(220, 38, 38, 0.2)'
    };
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors?.chartGrid || '#374151'};
    color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  }
`;

const StyledInput = styled.input<StyledFieldProps>`
  ${baseInputStyles}
`;

const StyledTextarea = styled.textarea<StyledFieldProps>`
  ${baseInputStyles}
  resize: vertical;
  min-height: 80px;
`;

const StyledSelect = styled.select<StyledFieldProps>`
  ${baseInputStyles}
  cursor: pointer;
`;

const ErrorMessage = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '12px'};
  color: ${({ theme }) => theme.colors?.error || '#dc2626'};
`;

const HelpText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '12px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;

/**
 * EnhancedFormField Component
 * 
 * An enhanced form field component that uses the useFormField hook
 * for advanced validation and state management.
 * 
 * @example
 * ```typescript
 * // Email field with validation
 * <EnhancedFormField
 *   name="email"
 *   label="Email Address"
 *   type="email"
 *   required={true}
 *   validationRules={[validationRules.email()]}
 *   placeholder="Enter your email"
 * />
 * 
 * // Select field
 * <EnhancedFormField
 *   name="category"
 *   label="Trade Category"
 *   inputType="select"
 *   required={true}
 *   options={[
 *     { value: 'scalp', label: 'Scalp Trade' },
 *     { value: 'swing', label: 'Swing Trade' },
 *   ]}
 * />
 * ```
 */
export const EnhancedFormField: React.FC<EnhancedFormFieldProps> = ({
  name,
  label,
  placeholder,
  disabled = false,
  className,
  size = 'md',
  helpText,
  inputType = 'input',
  options = [],
  rows = 4,
  onChange,
  onBlur,
  ...fieldConfig
}) => {
  const field = useFormField({
    ...fieldConfig,
    validateOnBlur: true,
  });

  // Handle external onChange
  React.useEffect(() => {
    if (onChange) {
      onChange(field.value);
    }
  }, [field.value, onChange]);

  // Handle external onBlur
  const handleBlur = (e: React.FocusEvent<any>) => {
    field.handleBlur(e);
    if (onBlur) {
      onBlur();
    }
  };

  const inputProps = {
    id: name,
    name,
    value: field.value,
    onChange: field.handleChange,
    onBlur: handleBlur,
    disabled,
    placeholder,
    $hasError: !!field.error,
    $disabled: disabled,
    $size: size,
  };

  const renderInput = () => {
    switch (inputType) {
      case 'textarea':
        return (
          <StyledTextarea
            {...inputProps}
            rows={rows}
          />
        );
      
      case 'select':
        return (
          <StyledSelect {...inputProps}>
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </StyledSelect>
        );
      
      default:
        return (
          <StyledInput
            {...inputProps}
            type={fieldConfig.type || 'text'}
          />
        );
    }
  };

  return (
    <FieldContainer className={className}>
      {label && (
        <Label htmlFor={name} $required={!!fieldConfig.required}>
          {label}
        </Label>
      )}
      
      {renderInput()}

      {field.error && field.touched && (
        <ErrorMessage role="alert">
          {field.error}
        </ErrorMessage>
      )}

      {helpText && !field.error && (
        <HelpText>
          {helpText}
        </HelpText>
      )}
    </FieldContainer>
  );
};

export default EnhancedFormField;
