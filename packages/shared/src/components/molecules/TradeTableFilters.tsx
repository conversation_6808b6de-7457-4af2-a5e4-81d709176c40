/**
 * Trade Table Filters
 *
 * Filtering controls for trading data tables.
 */
import React from 'react';
import styled from 'styled-components';
import { Input } from '../atoms/Input';
import { Select } from '../atoms/Select';
import { Button } from '../atoms/Button';
import { TradeFilters } from '../../types/trading';

// Typed constants for field names
const TRADE_FILTER_FIELDS = {
  MODEL_TYPE: 'model_type' as const,
  WIN_LOSS: 'win_loss' as const,
  DATE_FROM: 'dateFrom' as const,
  DATE_TO: 'dateTo' as const,
  SESSION: 'session' as const,
  DIRECTION: 'direction' as const,
  MARKET: 'market' as const,
  MIN_R_MULTIPLE: 'min_r_multiple' as const,
  MAX_R_MULTIPLE: 'max_r_multiple' as const,
  MIN_PATTERN_QUALITY: 'min_pattern_quality' as const,
  MAX_PATTERN_QUALITY: 'max_pattern_quality' as const,
} as const;

export interface TradeTableFiltersProps {
  /** Current filter values */
  filters: TradeFilters;
  /** Function called when filters change */
  onFiltersChange: (filters: TradeFilters) => void;
  /** Function called when filters are reset */
  onReset?: () => void;
  /** Whether the filters are in a loading state */
  isLoading?: boolean;
  /** Whether to show advanced filters */
  showAdvanced?: boolean;
  /** Function to toggle advanced filters */
  onToggleAdvanced?: () => void;
}

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;

const FilterRow = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: end;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  min-width: 120px;
`;

const FilterLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

const FilterActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: center;
  margin-left: auto;

  @media (max-width: 768px) {
    margin-left: 0;
    justify-content: flex-end;
  }
`;

const AdvancedFilters = styled.div<{ isVisible: boolean }>`
  display: ${({ isVisible }) => (isVisible ? 'flex' : 'none')};
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '16px'};
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;

const RangeInputGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  align-items: center;
`;

const RangeLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

/**
 * Trade Table Filters Component
 */
export const TradeTableFilters: React.FC<TradeTableFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
  isLoading = false,
  showAdvanced = false,
  onToggleAdvanced,
}) => {
  const handleFilterChange = (key: keyof TradeFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleReset = () => {
    const resetFilters: TradeFilters = {};
    onFiltersChange(resetFilters);
    onReset?.();
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined && value !== '' && value !== null
  );

  return (
    <FiltersContainer>
      <FilterRow>
        <FilterGroup>
          <FilterLabel>Date From</FilterLabel>
          <Input
            type="date"
            value={filters.dateFrom || ''}
            onChange={(value) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_FROM, value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Date To</FilterLabel>
          <Input
            type="date"
            value={filters.dateTo || ''}
            onChange={(value) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_TO, value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Model Type</FilterLabel>
          <Select
            options={[
              { value: '', label: 'All Models' },
              { value: 'RD-Cont', label: 'RD-Cont' },
              { value: 'FVG-RD', label: 'FVG-RD' },
              { value: 'True-RD', label: 'True-RD' },
              { value: 'IMM-RD', label: 'IMM-RD' },
              { value: 'Dispersed-RD', label: 'Dispersed-RD' },
              { value: 'Wide-Gap-RD', label: 'Wide-Gap-RD' },
            ]}
            value={filters.model_type || ''}
            onChange={(value) => handleFilterChange(TRADE_FILTER_FIELDS.MODEL_TYPE, value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Session</FilterLabel>
          <Select
            options={[
              { value: '', label: 'All Sessions' },
              { value: 'Pre-Market', label: 'Pre-Market' },
              { value: 'NY Open', label: 'NY Open' },
              { value: '10:50-11:10', label: '10:50-11:10' },
              { value: '11:50-12:10', label: '11:50-12:10' },
              { value: 'Lunch Macro', label: 'Lunch Macro' },
              { value: '13:50-14:10', label: '13:50-14:10' },
              { value: '14:50-15:10', label: '14:50-15:10' },
              { value: '15:15-15:45', label: '15:15-15:45' },
              { value: 'MOC', label: 'MOC' },
              { value: 'Post MOC', label: 'Post MOC' },
            ]}
            value={filters.session || ''}
            onChange={(value) => handleFilterChange(TRADE_FILTER_FIELDS.SESSION, value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Direction</FilterLabel>
          <Select
            options={[
              { value: '', label: 'All Directions' },
              { value: 'Long', label: 'Long' },
              { value: 'Short', label: 'Short' },
            ]}
            value={filters.direction || ''}
            onChange={(value) =>
              handleFilterChange(TRADE_FILTER_FIELDS.DIRECTION, value as 'Long' | 'Short' | '')
            }
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Result</FilterLabel>
          <Select
            options={[
              { value: '', label: 'All Results' },
              { value: 'Win', label: 'Win' },
              { value: 'Loss', label: 'Loss' },
            ]}
            value={filters.win_loss || ''}
            onChange={(value) =>
              handleFilterChange(TRADE_FILTER_FIELDS.WIN_LOSS, value as 'Win' | 'Loss' | '')
            }
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterActions>
          {onToggleAdvanced && (
            <Button variant="outline" size="small" onClick={onToggleAdvanced} disabled={isLoading}>
              {showAdvanced ? 'Hide' : 'Show'} Advanced
            </Button>
          )}

          <Button
            variant="outline"
            size="small"
            onClick={handleReset}
            disabled={isLoading || !hasActiveFilters}
          >
            Reset
          </Button>
        </FilterActions>
      </FilterRow>

      <AdvancedFilters isVisible={showAdvanced}>
        <FilterRow>
          <FilterGroup>
            <FilterLabel>Market</FilterLabel>
            <Select
              options={[
                { value: '', label: 'All Markets' },
                { value: 'MNQ', label: 'MNQ' },
                { value: 'NQ', label: 'NQ' },
                { value: 'ES', label: 'ES' },
                { value: 'MES', label: 'MES' },
                { value: 'YM', label: 'YM' },
                { value: 'MYM', label: 'MYM' },
              ]}
              value={filters.market || ''}
              onChange={(value) => handleFilterChange(TRADE_FILTER_FIELDS.MARKET, value)}
              disabled={isLoading}
            />
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>R Multiple Range</FilterLabel>
            <RangeInputGroup>
              <Input
                type="number"
                placeholder="Min"
                step="0.1"
                value={filters.min_r_multiple || ''}
                onChange={(value) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MIN_R_MULTIPLE,
                    value ? Number(value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
              <RangeLabel>to</RangeLabel>
              <Input
                type="number"
                placeholder="Max"
                step="0.1"
                value={filters.max_r_multiple || ''}
                onChange={(value) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MAX_R_MULTIPLE,
                    value ? Number(value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
            </RangeInputGroup>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Pattern Quality Range</FilterLabel>
            <RangeInputGroup>
              <Input
                type="number"
                placeholder="Min"
                min="1"
                max="5"
                step="0.1"
                value={filters.min_pattern_quality || ''}
                onChange={(value) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MIN_PATTERN_QUALITY,
                    value ? Number(value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
              <RangeLabel>to</RangeLabel>
              <Input
                type="number"
                placeholder="Max"
                min="1"
                max="5"
                step="0.1"
                value={filters.max_pattern_quality || ''}
                onChange={(value) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MAX_PATTERN_QUALITY,
                    value ? Number(value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
            </RangeInputGroup>
          </FilterGroup>
        </FilterRow>
      </AdvancedFilters>
    </FiltersContainer>
  );
};
