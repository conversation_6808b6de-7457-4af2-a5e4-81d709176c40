/**
 * Empty State Component
 *
 * A component for displaying empty states with customizable appearance.
 */
import React from 'react';
import styled, { css } from 'styled-components';
import { Button } from '../atoms/Button';

export type EmptyStateVariant = 'default' | 'compact' | 'card';
export type EmptyStateSize = 'small' | 'medium' | 'large';

export interface EmptyStateProps {
  /** The title of the empty state */
  title?: string;
  /** The description of the empty state */
  description?: string;
  /** The icon to display (as a component) */
  icon?: React.ReactNode;
  /** The action button text */
  actionText?: string;
  /** Function called when the action button is clicked */
  onAction?: () => void;
  /** The variant of the empty state */
  variant?: EmptyStateVariant;
  /** The size of the empty state */
  size?: EmptyStateSize;
  /** Additional CSS class names */
  className?: string;
  /** Additional content to display */
  children?: React.ReactNode;
}

// Define styled components first to avoid reference errors
const Title = styled.h3<{ size: EmptyStateSize }>`
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};

  ${({ size, theme }) => {
    const sizeMap = {
      small: theme.fontSizes.md,
      medium: theme.fontSizes.lg,
      large: theme.fontSizes.xl,
    };

    return css`
      font-size: ${sizeMap[size]};
    `;
  }}
`;

const Description = styled.p<{ size: EmptyStateSize }>`
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
  color: ${({ theme }) => theme.colors.textSecondary};

  ${({ size, theme }) => {
    const sizeMap = {
      small: theme.fontSizes.sm,
      medium: theme.fontSizes.md,
      large: theme.fontSizes.lg,
    };

    return css`
      font-size: ${sizeMap[size]};
    `;
  }}
`;

// Size styles - removed nested component references to avoid circular dependencies

// Variant styles
const variantStyles = {
  default: css`
    background-color: transparent;
  `,
  compact: css`
    background-color: transparent;
    text-align: left;
    align-items: flex-start;
  `,
  card: css`
    background-color: ${({ theme }) => theme.colors.surface};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  `,
};

const Container = styled.div<{
  variant: EmptyStateVariant;
  size: EmptyStateSize;
}>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;

  /* Apply variant styles */
  ${({ variant }) => variantStyles[variant]}

  /* Apply size styles - using a function to avoid styled-components warning about circular references */
  ${({ size, theme }) => {
    switch (size) {
      case 'small':
        return css`
          padding: ${theme.spacing.md};
          min-height: 120px;
        `;
      case 'large':
        return css`
          padding: ${theme.spacing.xl};
          min-height: 300px;
        `;
      default: // 'medium'
        return css`
          padding: ${theme.spacing.lg};
          min-height: 200px;
        `;
    }
  }}
`;

const IconContainer = styled.div<{ size: EmptyStateSize }>`
  margin-bottom: ${({ theme }) => theme.spacing.md};

  ${({ size, theme }) => {
    const sizeMap = {
      small: '32px',
      medium: '48px',
      large: '64px',
    };

    return css`
      font-size: ${sizeMap[size]};

      svg {
        width: ${sizeMap[size]};
        height: ${sizeMap[size]};
        color: ${theme.colors.textSecondary};
      }
    `;
  }}
`;

// Title and Description components are defined at the top of the file

const ActionContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const ChildrenContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
  width: 100%;
`;

/**
 * Empty State Component
 *
 * A component for displaying empty states with customizable appearance.
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  title = '',
  description = '',
  icon,
  actionText = '',
  onAction,
  variant = 'default' as EmptyStateVariant,
  size = 'medium' as EmptyStateSize,
  className = '',
  children,
}) => {
  return (
    <Container variant={variant} size={size} className={className}>
      {icon && <IconContainer size={size}>{icon}</IconContainer>}

      {title && <Title size={size}>{title}</Title>}
      {description && <Description size={size}>{description}</Description>}

      {actionText && onAction && (
        <ActionContainer>
          <Button variant="primary" size={size === 'small' ? 'small' : 'medium'} onClick={onAction}>
            {actionText}
          </Button>
        </ActionContainer>
      )}

      {children && <ChildrenContainer>{children}</ChildrenContainer>}
    </Container>
  );
};
