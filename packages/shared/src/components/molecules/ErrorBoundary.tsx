/**
 * Error Boundary Component
 *
 * A React error boundary component that catches errors in its child component tree
 * and displays a fallback UI instead of crashing the entire application.
 *
 * This is a unified error boundary that can be used at any level of the application.
 */
import { Component, ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';

// Error boundary props
export interface ErrorBoundaryProps {
  /** The children to render */
  children: ReactNode;
  /** Custom fallback component to render when an error occurs */
  fallback?: ReactNode | ((props: { error: Error; resetError: () => void }) => ReactNode);
  /** Function to call when an error occurs */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  /** Whether to reset the error boundary when the children prop changes */
  resetOnPropsChange?: boolean;
  /** Whether to reset the error boundary when the component unmounts */
  resetOnUnmount?: boolean;
  /** Name of the boundary for identification in logs */
  name?: string;
  /** Whether this is a feature-level boundary */
  isFeatureBoundary?: boolean;
  /** Function to call when the user chooses to skip a feature (only for feature boundaries) */
  onSkip?: () => void;
}

// Error boundary state
interface ErrorBoundaryState {
  /** Whether an error has occurred */
  hasError: boolean;
  /** The error that occurred */
  error: Error | null;
}

// Styled components
const ErrorContainer = styled.div<{ isAppLevel?: boolean }>`
  padding: 1.5rem;
  margin: ${(props) => (props.isAppLevel ? '0' : '1rem 0')};
  border-radius: 0.5rem;
  background-color: ${(props) => (props.isAppLevel ? '#1a1f2c' : '#f44336')};
  color: #ffffff;
  ${(props) =>
    props.isAppLevel &&
    `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `}
`;

const ErrorCard = styled.div`
  background-color: #252a37;
  border-radius: 0.5rem;
  padding: 2rem;
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ErrorTitle = styled.h3<{ isAppLevel?: boolean }>`
  margin-top: 0;
  font-size: ${(props) => (props.isAppLevel ? '1.5rem' : '1.25rem')};
  font-weight: 700;
  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};
`;

const ErrorMessage = styled.p<{ isAppLevel?: boolean }>`
  margin-bottom: 1rem;
  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};
`;

const ErrorDetails = styled.details`
  margin-bottom: 1rem;

  summary {
    cursor: pointer;
    color: #2196f3;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
`;

const ErrorStack = styled.pre`
  font-size: 0.875rem;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow: auto;
  max-height: 200px;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
`;

const RetryButton = styled.button`
  background-color: #ffffff;
  color: #f44336;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const SkipButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const ReloadButton = styled(RetryButton)`
  margin-top: 1rem;
  width: 100%;
`;

/**
 * Default fallback UI for the error boundary
 */
const DefaultFallback = ({
  error,
  resetError,
  isAppLevel,
  name,
  onSkip,
}: {
  error: Error;
  resetError: () => void;
  isAppLevel?: boolean;
  name?: string;
  onSkip?: () => void;
}) => {
  const handleReload = () => {
    window.location.reload();
  };

  if (isAppLevel) {
    return (
      <ErrorContainer isAppLevel>
        <ErrorCard>
          <ErrorTitle isAppLevel>Something went wrong</ErrorTitle>
          <ErrorMessage isAppLevel>
            We're sorry, but an unexpected error has occurred. Please try reloading the application.
          </ErrorMessage>
          <ErrorDetails>
            <summary>Technical Details</summary>
            <ErrorMessage>{error.message}</ErrorMessage>
            {error.stack && <ErrorStack>{error.stack}</ErrorStack>}
          </ErrorDetails>
          <ReloadButton onClick={handleReload}>Reload Application</ReloadButton>
        </ErrorCard>
      </ErrorContainer>
    );
  }

  return (
    <ErrorContainer>
      <ErrorTitle>{name ? `Error in ${name}` : 'Something went wrong'}</ErrorTitle>
      <ErrorMessage>
        {name
          ? `We encountered a problem while loading ${name}. You can try again${
              onSkip ? ' or skip this feature' : ''
            }.`
          : 'An unexpected error occurred. Please try again.'}
      </ErrorMessage>
      <ErrorDetails>
        <summary>Technical Details</summary>
        <ErrorMessage>{error.message}</ErrorMessage>
        {error.stack && <ErrorStack>{error.stack}</ErrorStack>}
      </ErrorDetails>
      <ButtonContainer>
        <RetryButton onClick={resetError}>Try Again</RetryButton>
        {onSkip && <SkipButton onClick={onSkip}>Skip This Feature</SkipButton>}
      </ButtonContainer>
    </ErrorContainer>
  );
};

/**
 * Error Boundary Component
 *
 * A unified React error boundary component that catches errors in its child component tree
 * and displays a fallback UI instead of crashing the entire application.
 *
 * This component can be used at both the application level and feature level.
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    const { name } = this.props;
    const boundaryName = name ? `ErrorBoundary(${name})` : 'ErrorBoundary';
    console.error(`Error caught by ${boundaryName}:`, error, errorInfo);

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Here we could add Sentry integration
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.withScope((scope) => {
    //     scope.setTag('boundary', name || 'unnamed');
    //     scope.setExtra('componentStack', errorInfo.componentStack);
    //     window.Sentry.captureException(error);
    //   });
    // }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps): void {
    // Reset the error state if the children prop changes and resetOnPropsChange is true
    if (
      this.state.hasError &&
      this.props.resetOnPropsChange &&
      prevProps.children !== this.props.children
    ) {
      this.resetError();
    }
  }

  componentWillUnmount(): void {
    // Reset the error state if resetOnUnmount is true
    if (this.state.hasError && this.props.resetOnUnmount) {
      this.resetError();
    }
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback, name, isFeatureBoundary, onSkip } = this.props;

    if (hasError && error) {
      // Render the fallback UI if an error occurred
      if (typeof fallback === 'function') {
        return fallback({ error, resetError: this.resetError });
      } else if (fallback) {
        return fallback;
      }

      // Use the default fallback
      return (
        <DefaultFallback
          error={error}
          resetError={this.resetError}
          isAppLevel={!isFeatureBoundary}
          name={name}
          onSkip={onSkip}
        />
      );
    }

    // Otherwise, render the children
    return children;
  }
}

export default ErrorBoundary;
