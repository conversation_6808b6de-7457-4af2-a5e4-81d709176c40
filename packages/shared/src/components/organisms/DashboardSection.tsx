/**
 * Dashboard Section Component
 *
 * A composable dashboard section that can render different types of content
 * based on the section name. This implements the composition pattern to
 * reduce coupling between dashboard components.
 */

import React from 'react';
import styled from 'styled-components';

const SectionContainer = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  padding-bottom: ${({ theme }) => theme.spacing.sm};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const SectionTitle = styled.h2`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  margin: 0;
`;

const SectionActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const SectionContent = styled.div`
  min-height: 200px;
`;

const LoadingState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const ErrorState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: ${({ theme }) => theme.colors.danger};
  text-align: center;
`;

export interface DashboardSectionProps {
  /** Unique identifier for the section */
  name: string;
  /** Display title for the section */
  title?: string;
  /** Content to render in the section */
  children?: React.ReactNode;
  /** Action buttons or controls */
  actions?: React.ReactNode;
  /** Loading state */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Custom className for styling */
  className?: string;
  /** Whether the section can be collapsed */
  collapsible?: boolean;
  /** Whether the section is initially collapsed */
  defaultCollapsed?: boolean;
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  name,
  title,
  children,
  actions,
  isLoading = false,
  error = null,
  className,
  collapsible = false,
  defaultCollapsed = false,
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  const handleToggleCollapse = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  const displayTitle = title || name.charAt(0).toUpperCase() + name.slice(1);

  const renderContent = () => {
    if (error) {
      return (
        <ErrorState>
          <div>
            <div>Error loading {name}</div>
            <div style={{ fontSize: '0.9em', marginTop: '8px' }}>{error}</div>
          </div>
        </ErrorState>
      );
    }

    if (isLoading) {
      return <LoadingState>Loading {name}...</LoadingState>;
    }

    if (!children) {
      return <LoadingState>No {name} data available</LoadingState>;
    }

    return children;
  };

  return (
    <SectionContainer className={className} data-section={name}>
      <SectionHeader>
        <SectionTitle 
          onClick={handleToggleCollapse}
          style={{ cursor: collapsible ? 'pointer' : 'default' }}
        >
          {displayTitle}
          {collapsible && (
            <span style={{ marginLeft: '8px', fontSize: '0.8em' }}>
              {isCollapsed ? '▶' : '▼'}
            </span>
          )}
        </SectionTitle>
        {actions && <SectionActions>{actions}</SectionActions>}
      </SectionHeader>
      
      {!isCollapsed && (
        <SectionContent>
          {renderContent()}
        </SectionContent>
      )}
    </SectionContainer>
  );
};

export default DashboardSection;
