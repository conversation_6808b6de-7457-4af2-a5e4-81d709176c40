/**
 * Trade Analysis Component
 *
 * Shared component for trade analysis functionality.
 * This provides a reusable analysis interface that can be used across features.
 */

import React from 'react';
import styled from 'styled-components';

const AnalysisContainer = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
`;

const AnalysisTitle = styled.h3`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const AnalysisContent = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  line-height: 1.6;
`;

interface TradeAnalysisProps {
  title?: string;
  children?: React.ReactNode;
  isLoading?: boolean;
}

const TradeAnalysis: React.FC<TradeAnalysisProps> = ({ 
  title = "Trade Analysis", 
  children, 
  isLoading 
}) => {
  return (
    <AnalysisContainer>
      <AnalysisTitle>{title}</AnalysisTitle>
      <AnalysisContent>
        {isLoading ? (
          <div>Loading analysis...</div>
        ) : (
          children || <div>No analysis data available</div>
        )}
      </AnalysisContent>
    </AnalysisContainer>
  );
};

export default TradeAnalysis;
