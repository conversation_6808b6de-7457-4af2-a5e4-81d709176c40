/**
 * Trade Metrics Component
 *
 * Shared component for displaying trade performance metrics.
 * This replaces the scattered metrics components across features.
 */

import React from 'react';
import styled from 'styled-components';

const MetricsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const MetricCard = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
`;

const MetricLabel = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const MetricValue = styled.div<{ positive?: boolean; negative?: boolean }>`
  color: ${({ theme, positive, negative }) =>
    positive
      ? theme.colors.success
      : negative
      ? theme.colors.danger
      : theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
`;

interface TradeMetric {
  label: string;
  value: string | number;
  positive?: boolean;
  negative?: boolean;
}

interface TradeMetricsProps {
  metrics: TradeMetric[];
  isLoading?: boolean;
}

const TradeMetrics: React.FC<TradeMetricsProps> = ({ metrics, isLoading }) => {
  if (isLoading) {
    return (
      <MetricsContainer>
        {Array.from({ length: 4 }).map((_, index) => (
          <MetricCard key={index}>
            <MetricLabel>Loading...</MetricLabel>
            <MetricValue>--</MetricValue>
          </MetricCard>
        ))}
      </MetricsContainer>
    );
  }

  return (
    <MetricsContainer>
      {metrics.map((metric, index) => (
        <MetricCard key={index}>
          <MetricLabel>{metric.label}</MetricLabel>
          <MetricValue positive={metric.positive} negative={metric.negative}>
            {metric.value}
          </MetricValue>
        </MetricCard>
      ))}
    </MetricsContainer>
  );
};

export default TradeMetrics;
