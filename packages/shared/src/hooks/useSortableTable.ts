/**
 * useSortableTable Hook
 * 
 * EXTRACTED FROM: CategoryPerformanceChart and other table components
 * Centralizes sortable table logic for consistent behavior across components.
 * 
 * BENEFITS:
 * - Consistent sorting behavior
 * - Type-safe sorting configuration
 * - Reduced boilerplate in table components
 * - Automatic sort state management
 */

import { useState, useMemo, useCallback } from 'react';

export type SortDirection = 'asc' | 'desc';

export interface SortConfig<T> {
  field: keyof T;
  direction: SortDirection;
}

export interface SortableColumn<T> {
  field: keyof T;
  label: string;
  sortable?: boolean;
  sortFn?: (a: T, b: T) => number;
}

export interface UseSortableTableConfig<T> {
  data: T[];
  columns: SortableColumn<T>[];
  defaultSort?: {
    field: keyof T;
    direction: SortDirection;
  };
}

export interface UseSortableTableReturn<T> {
  sortedData: T[];
  sortConfig: SortConfig<T> | null;
  handleSort: (field: keyof T) => void;
  getSortIcon: (field: keyof T) => string | null;
  isSorted: (field: keyof T) => boolean;
  getSortDirection: (field: keyof T) => SortDirection | null;
}

/**
 * Default sort functions for common data types
 */
export const sortFunctions = {
  string: <T>(field: keyof T) => (a: T, b: T): number => {
    const aVal = String(a[field] || '');
    const bVal = String(b[field] || '');
    return aVal.localeCompare(bVal);
  },

  number: <T>(field: keyof T) => (a: T, b: T): number => {
    const aVal = Number(a[field]) || 0;
    const bVal = Number(b[field]) || 0;
    return aVal - bVal;
  },

  date: <T>(field: keyof T) => (a: T, b: T): number => {
    const aVal = new Date(a[field] as any).getTime();
    const bVal = new Date(b[field] as any).getTime();
    return aVal - bVal;
  },

  boolean: <T>(field: keyof T) => (a: T, b: T): number => {
    const aVal = Boolean(a[field]);
    const bVal = Boolean(b[field]);
    return Number(aVal) - Number(bVal);
  },
};

/**
 * Custom hook for managing sortable table state and logic
 * 
 * @param config - Configuration object for the sortable table
 * @returns Object with sorted data and sort management functions
 * 
 * @example
 * ```typescript
 * const tableConfig = useSortableTable({
 *   data: trades,
 *   columns: [
 *     { field: 'symbol', label: 'Symbol', sortable: true },
 *     { field: 'profitLoss', label: 'P&L', sortable: true, sortFn: sortFunctions.number('profitLoss') },
 *     { field: 'date', label: 'Date', sortable: true, sortFn: sortFunctions.date('date') },
 *   ],
 *   defaultSort: { field: 'profitLoss', direction: 'desc' },
 * });
 * 
 * // In component render:
 * <th onClick={() => tableConfig.handleSort('symbol')}>
 *   Symbol {tableConfig.getSortIcon('symbol')}
 * </th>
 * ```
 */
export const useSortableTable = <T>({
  data,
  columns,
  defaultSort,
}: UseSortableTableConfig<T>): UseSortableTableReturn<T> => {
  const [sortConfig, setSortConfig] = useState<SortConfig<T> | null>(
    defaultSort ? { field: defaultSort.field, direction: defaultSort.direction } : null
  );

  /**
   * Handles sorting when a column header is clicked
   */
  const handleSort = useCallback((field: keyof T) => {
    const column = columns.find(col => col.field === field);
    if (!column?.sortable) return;

    setSortConfig(current => {
      if (current?.field === field) {
        // Toggle direction if same field
        return {
          field,
          direction: current.direction === 'asc' ? 'desc' : 'asc',
        };
      } else {
        // Set new field with default direction (desc for numbers, asc for strings)
        const defaultDirection: SortDirection = 
          typeof data[0]?.[field] === 'number' ? 'desc' : 'asc';
        return {
          field,
          direction: defaultDirection,
        };
      }
    });
  }, [columns, data]);

  /**
   * Sorts the data based on current sort configuration
   */
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    const column = columns.find(col => col.field === sortConfig.field);
    if (!column) return data;

    const sortedArray = [...data].sort((a, b) => {
      let comparison = 0;

      if (column.sortFn) {
        // Use custom sort function if provided
        comparison = column.sortFn(a, b);
      } else {
        // Use default sort based on data type
        const aVal = a[sortConfig.field];
        const bVal = b[sortConfig.field];

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          comparison = aVal.localeCompare(bVal);
        } else if (typeof aVal === 'number' && typeof bVal === 'number') {
          comparison = aVal - bVal;
        } else {
          // Fallback to string comparison
          comparison = String(aVal).localeCompare(String(bVal));
        }
      }

      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });

    return sortedArray;
  }, [data, sortConfig, columns]);

  /**
   * Gets the sort icon for a specific field
   */
  const getSortIcon = useCallback((field: keyof T): string | null => {
    if (!sortConfig || sortConfig.field !== field) return null;
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  }, [sortConfig]);

  /**
   * Checks if a field is currently being sorted
   */
  const isSorted = useCallback((field: keyof T): boolean => {
    return sortConfig?.field === field;
  }, [sortConfig]);

  /**
   * Gets the sort direction for a specific field
   */
  const getSortDirection = useCallback((field: keyof T): SortDirection | null => {
    return sortConfig?.field === field ? sortConfig.direction : null;
  }, [sortConfig]);

  return {
    sortedData,
    sortConfig,
    handleSort,
    getSortIcon,
    isSorted,
    getSortDirection,
  };
};

export default useSortableTable;
