/**
 * useLoadingState Hook
 * 
 * EXTRACTED FROM: Multiple components with loading patterns
 * Centralizes loading state management with error handling and async operations.
 * 
 * BENEFITS:
 * - Consistent loading patterns across components
 * - Built-in error handling
 * - Automatic loading state management
 * - Type-safe async operations
 */

import { useState, useCallback } from 'react';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  isSuccess: boolean;
  isError: boolean;
}

export interface LoadingActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
  withLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>;
  withLoadingCallback: <T extends any[]>(
    asyncFn: (...args: T) => Promise<any>
  ) => (...args: T) => Promise<void>;
}

export type UseLoadingStateReturn = LoadingState & LoadingActions;

/**
 * Custom hook for managing loading states with error handling
 * 
 * @param initialLoading - Initial loading state (default: false)
 * @returns Object with loading state and actions
 * 
 * @example
 * ```typescript
 * const { isLoading, error, withLoading } = useLoadingState();
 * 
 * const fetchData = async () => {
 *   const data = await withLoading(() => api.getData());
 *   setData(data);
 * };
 * ```
 */
export const useLoadingState = (initialLoading: boolean = false): UseLoadingStateReturn => {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
    if (loading) {
      setError(null);
      setIsSuccess(false);
    }
  }, []);

  const setErrorState = useCallback((errorMessage: string | null) => {
    setError(errorMessage);
    setIsLoading(false);
    setIsSuccess(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setIsSuccess(false);
  }, []);

  /**
   * Wraps an async function with loading state management
   * Automatically sets loading to true, handles errors, and sets loading to false
   */
  const withLoading = useCallback(async <T>(asyncFn: () => Promise<T>): Promise<T> => {
    setLoading(true);
    try {
      const result = await asyncFn();
      setIsSuccess(true);
      setIsLoading(false);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setErrorState(errorMessage);
      throw err;
    }
  }, [setLoading, setErrorState]);

  /**
   * Creates a callback function wrapped with loading state management
   * Useful for event handlers and form submissions
   */
  const withLoadingCallback = useCallback(<T extends any[]>(
    asyncFn: (...args: T) => Promise<any>
  ) => {
    return async (...args: T): Promise<void> => {
      try {
        await withLoading(() => asyncFn(...args));
      } catch (error) {
        // Error is already handled by withLoading
        console.error('Operation failed:', error);
      }
    };
  }, [withLoading]);

  return {
    // State
    isLoading,
    error,
    isSuccess,
    isError: error !== null,
    
    // Actions
    setLoading,
    setError: setErrorState,
    clearError,
    reset,
    withLoading,
    withLoadingCallback,
  };
};

export default useLoadingState;
