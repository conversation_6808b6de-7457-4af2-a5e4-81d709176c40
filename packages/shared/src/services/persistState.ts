/**
 * Persist State
 *
 * A utility for persisting state to local storage.
 */
import { Reducer, Action } from '../state/createStoreContext';

/**
 * Persist state options
 */
export interface PersistStateOptions<S> {
  /** The key to use for local storage */
  key: string;
  /** The initial state */
  initialState: S;
  /** The version of the state schema */
  version?: number;
  /** A function to migrate state from a previous version */
  migrate?: (state: any, version: number) => S;
  /** A function to serialize the state */
  serialize?: (state: S) => string;
  /** A function to deserialize the state */
  deserialize?: (serialized: string) => S;
  /** A function to filter the state before persisting */
  filter?: (state: S) => Partial<S>;
  /** A function to merge the persisted state with the initial state */
  merge?: (persistedState: Partial<S>, initialState: S) => S;
  /** Whether to debug the persistence */
  debug?: boolean;
}

/**
 * Persist state result
 */
export interface PersistStateResult<S, A extends Action> {
  /** The persisted reducer */
  reducer: Reducer<S, A>;
  /** The persisted initial state */
  initialState: S;
  /** A function to clear the persisted state */
  clear: () => void;
}

/**
 * Create a persisted reducer
 *
 * @param reducer - The reducer function
 * @param options - The persist state options
 * @returns The persisted reducer and initial state
 */
export function persistState<S, A extends Action>(
  reducer: Reducer<S, A>,
  options: PersistStateOptions<S>
): PersistStateResult<S, A> {
  const {
    key,
    initialState,
    version = 1,
    migrate,
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    filter = (state) => state,
    merge = (persistedState, initialState) => ({ ...initialState, ...persistedState }),
    debug = false,
  } = options;

  // Load persisted state from local storage
  const loadState = (): Partial<S> | null => {
    try {
      const serializedState = localStorage.getItem(key);
      if (serializedState === null) {
        return null;
      }

      const { state, version: persistedVersion } = deserialize(serializedState);

      // Migrate state if version has changed
      if (persistedVersion !== version && migrate) {
        if (debug) {
          console.log(`Migrating state from version ${persistedVersion} to ${version}`);
        }
        return migrate(state, persistedVersion);
      }

      return state;
    } catch (err) {
      if (debug) {
        console.error('Error loading state from local storage:', err);
      }
      return null;
    }
  };

  // Save state to local storage
  const saveState = (state: S): void => {
    try {
      const filteredState = filter(state);
      const serializedState = serialize({
        state: filteredState,
        version,
      });
      localStorage.setItem(key, serializedState);
    } catch (err) {
      if (debug) {
        console.error('Error saving state to local storage:', err);
      }
    }
  };

  // Clear persisted state
  const clear = (): void => {
    try {
      localStorage.removeItem(key);
    } catch (err) {
      if (debug) {
        console.error('Error clearing state from local storage:', err);
      }
    }
  };

  // Load persisted state and merge with initial state
  const persistedState = loadState();
  const mergedInitialState = persistedState ? merge(persistedState, initialState) : initialState;

  if (debug && persistedState) {
    console.log('Loaded persisted state:', persistedState);
    console.log('Merged initial state:', mergedInitialState);
  }

  // Create persisted reducer
  const persistedReducer: Reducer<S, A> = (state, action) => {
    const newState = reducer(state, action);
    saveState(newState);
    return newState;
  };

  return {
    reducer: persistedReducer,
    initialState: mergedInitialState,
    clear,
  };
}
