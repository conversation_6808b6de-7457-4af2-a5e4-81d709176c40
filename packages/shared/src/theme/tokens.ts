/**
 * Theme Tokens
 *
 * Design tokens for the theme system
 */

// Base colors interface
export interface BaseColors {
  // F1 colors
  f1Red: string;
  f1RedDark: string;
  f1RedLight: string;
  f1Blue: string;
  f1BlueDark: string;
  f1BlueLight: string;

  // Neutrals
  white: string;
  black: string;
  gray50: string;
  gray100: string;
  gray200: string;
  gray300: string;
  gray400: string;
  gray500: string;
  gray600: string;
  gray700: string;
  gray800: string;
  gray900: string;

  // Status colors
  green: string;
  greenDark: string;
  greenLight: string;
  yellow: string;
  yellowDark: string;
  yellowLight: string;
  red: string;
  redDark: string;
  redLight: string;
  blue: string;
  blueDark: string;
  blueLight: string;
  purple: string;
  purpleDark: string;
  purpleLight: string;

  // Transparent colors
  whiteTransparent10: string;
  blackTransparent10: string;
}

// Color mode interface
export interface ColorMode {
  // Base colors
  background: string;
  surface: string;
  cardBackground: string;
  border: string;
  divider: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  textInverse: string;
  success: string;
  warning: string;
  error: string;
  info: string;

  // Chart colors
  chartGrid: string;
  chartLine: string;

  // Trading specific colors
  profit: string;
  loss: string;
  neutral: string;

  // Component specific colors
  tooltipBackground: string;
  modalBackground: string;
}

// Base colors
export const baseColors: BaseColors = {
  // F1 colors - Updated to match racing specifications
  f1Red: '#dc2626',
  f1RedDark: '#b91c1c',
  f1RedLight: '#ef4444',
  f1Blue: '#1e5bc6',
  f1BlueDark: '#1a4da8',
  f1BlueLight: '#4a7dd8',

  // Neutrals
  white: '#ffffff',
  black: '#000000',
  gray50: '#f9fafb',
  gray100: '#f3f4f6',
  gray200: '#e5e7eb',
  gray300: '#d1d5db',
  gray400: '#9ca3af',
  gray500: '#6b7280',
  gray600: '#4b5563',
  gray700: '#374151',
  gray800: '#1f2937',
  gray900: '#111827',

  // Status colors
  green: '#4caf50',
  greenDark: '#388e3c',
  greenLight: '#81c784',
  yellow: '#ffeb3b',
  yellowDark: '#fbc02d',
  yellowLight: '#fff59d',
  red: '#f44336',
  redDark: '#d32f2f',
  redLight: '#e57373',
  blue: '#2196f3',
  blueDark: '#1976d2',
  blueLight: '#64b5f6',
  purple: '#9c27b0',
  purpleDark: '#7b1fa2',
  purpleLight: '#ba68c8',

  // Transparent colors
  whiteTransparent10: 'rgba(255, 255, 255, 0.1)',
  blackTransparent10: 'rgba(0, 0, 0, 0.1)',
};

// Dark mode colors - Enhanced F1 Racing Theme
export const darkModeColors: ColorMode = {
  background: '#0f0f0f',
  surface: '#1a1a1a',
  cardBackground: '#1a1a1a',
  border: '#333333',
  divider: 'rgba(255, 255, 255, 0.1)',
  textPrimary: '#ffffff',
  textSecondary: '#aaaaaa',
  textDisabled: '#666666',
  textInverse: '#1a1f2c',
  success: baseColors.green,
  warning: baseColors.yellow,
  error: baseColors.red,
  info: baseColors.blue,

  // Chart colors
  chartGrid: 'rgba(255, 255, 255, 0.1)',
  chartLine: baseColors.f1Red,

  // Trading specific colors
  profit: baseColors.green,
  loss: baseColors.red,
  neutral: baseColors.gray400,

  // Component specific colors
  tooltipBackground: 'rgba(37, 42, 55, 0.9)',
  modalBackground: 'rgba(26, 31, 44, 0.8)',
};

// Light mode colors
export const lightModeColors: ColorMode = {
  background: '#f5f5f5',
  surface: '#ffffff',
  cardBackground: '#ffffff',
  border: '#e0e0e0',
  divider: 'rgba(0, 0, 0, 0.1)',
  textPrimary: '#333333',
  textSecondary: '#666666',
  textDisabled: '#999999',
  textInverse: '#ffffff',
  success: baseColors.green,
  warning: baseColors.yellow,
  error: baseColors.red,
  info: baseColors.blue,

  // Chart colors
  chartGrid: 'rgba(0, 0, 0, 0.1)',
  chartLine: baseColors.f1Red,

  // Trading specific colors
  profit: baseColors.green,
  loss: baseColors.red,
  neutral: baseColors.gray400,

  // Component specific colors
  tooltipBackground: 'rgba(255, 255, 255, 0.9)',
  modalBackground: 'rgba(255, 255, 255, 0.8)',
};

// Spacing
export const spacing = {
  xxs: '4px',
  xs: '8px',
  sm: '12px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
};

// Font sizes
export const fontSizes = {
  xs: '0.75rem',
  sm: '0.875rem',
  md: '1rem',
  lg: '1.125rem',
  xl: '1.25rem',
  xxl: '1.5rem',
  h1: '2.5rem',
  h2: '2rem',
  h3: '1.75rem',
  h4: '1.5rem',
  h5: '1.25rem',
  h6: '1rem',
};

// Font weights
export const fontWeights = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
};

// Line heights
export const lineHeights = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75,
};

// Font families
export const fontFamilies = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace",
};

// Breakpoints
export const breakpoints = {
  xs: '480px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
};

// Border radius
export const borderRadius = {
  xs: '2px',
  sm: '4px',
  md: '6px',
  lg: '8px',
  xl: '12px',
  pill: '9999px',
  circle: '50%',
};

// Shadows
export const shadows = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px rgba(0, 0, 0, 0.1)',
  lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
};

// Transitions
export const transitions = {
  fast: '0.1s',
  normal: '0.3s',
  slow: '0.5s',
};

// Z-index
export const zIndex = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100,
};
