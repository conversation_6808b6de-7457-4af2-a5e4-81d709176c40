/**
 * Profit/Loss Theme Configuration
 * 
 * EXTRACTED FROM: ProfitLossCell.tsx (reducing complexity)
 * Centralized styling logic and theme constants for profit/loss components.
 */

import { css } from 'styled-components';
import { Theme } from './types';

export type ProfitLossSize = 'small' | 'medium' | 'large';
export type ProfitLossVariant = 'profit' | 'loss' | 'neutral' | 'default';

/**
 * Size configurations for profit/loss components
 */
export const profitLossSizes = {
  small: css`
    font-size: ${({ theme }: { theme: Theme }) => theme.fontSizes?.xs || '12px'};
    padding: ${({ theme }: { theme: Theme }) => theme.spacing?.xxs || '2px'} ${({ theme }: { theme: Theme }) => theme.spacing?.xs || '4px'};
  `,
  medium: css`
    font-size: ${({ theme }: { theme: Theme }) => theme.fontSizes?.sm || '14px'};
    padding: ${({ theme }: { theme: Theme }) => theme.spacing?.xs || '4px'} ${({ theme }: { theme: Theme }) => theme.spacing?.sm || '8px'};
  `,
  large: css`
    font-size: ${({ theme }: { theme: Theme }) => theme.fontSizes?.lg || '18px'};
    padding: ${({ theme }: { theme: Theme }) => theme.spacing?.sm || '8px'} ${({ theme }: { theme: Theme }) => theme.spacing?.md || '12px'};
  `,
};

/**
 * Color configurations for profit/loss variants
 */
export const profitLossColors = {
  profit: css`
    color: ${({ theme }: { theme: Theme }) => theme.colors?.profit || theme.colors?.success || '#4caf50'};
    background-color: ${({ theme }: { theme: Theme }) => 
      theme.colors?.profit ? `${theme.colors.profit}15` : 'rgba(76, 175, 80, 0.1)'};
    border: 1px solid ${({ theme }: { theme: Theme }) => 
      theme.colors?.profit ? `${theme.colors.profit}30` : 'rgba(76, 175, 80, 0.2)'};
  `,
  loss: css`
    color: ${({ theme }: { theme: Theme }) => theme.colors?.loss || theme.colors?.error || '#f44336'};
    background-color: ${({ theme }: { theme: Theme }) => 
      theme.colors?.loss ? `${theme.colors.loss}15` : 'rgba(244, 67, 54, 0.1)'};
    border: 1px solid ${({ theme }: { theme: Theme }) => 
      theme.colors?.loss ? `${theme.colors.loss}30` : 'rgba(244, 67, 54, 0.2)'};
  `,
  neutral: css`
    color: ${({ theme }: { theme: Theme }) => theme.colors?.neutral || theme.colors?.textSecondary || '#757575'};
    background-color: ${({ theme }: { theme: Theme }) => 
      theme.colors?.neutral ? `${theme.colors.neutral}15` : 'rgba(117, 117, 117, 0.1)'};
    border: 1px solid ${({ theme }: { theme: Theme }) => 
      theme.colors?.neutral ? `${theme.colors.neutral}30` : 'rgba(117, 117, 117, 0.2)'};
  `,
  default: css`
    color: ${({ theme }: { theme: Theme }) => theme.colors?.textPrimary || '#ffffff'};
    background-color: transparent;
    border: 1px solid transparent;
  `,
};

/**
 * Base styles for profit/loss components
 */
export const profitLossBaseStyles = css`
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: ${({ theme }: { theme: Theme }) => theme.fontWeights?.semibold || '600'};
  font-family: ${({ theme }: { theme: Theme }) => theme.fontFamilies?.mono || 'monospace'};
  transition: ${({ theme }: { theme: Theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  border-radius: ${({ theme }: { theme: Theme }) => theme.borderRadius?.sm || '4px'};
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }: { theme: Theme }) => theme.shadows?.sm || '0 2px 4px rgba(0, 0, 0, 0.1)'};
  }
`;

/**
 * Loading animation styles
 */
export const profitLossLoadingStyles = css`
  opacity: 0.6;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
`;

/**
 * Helper function to get size styles
 */
export const getProfitLossSize = (size: ProfitLossSize) => {
  return profitLossSizes[size];
};

/**
 * Helper function to get color styles based on amount
 */
export const getProfitLossVariant = (
  isProfit: boolean,
  isLoss: boolean,
  isNeutral: boolean
): ProfitLossVariant => {
  if (isProfit) return 'profit';
  if (isLoss) return 'loss';
  if (isNeutral) return 'neutral';
  return 'default';
};

/**
 * Helper function to get color styles
 */
export const getProfitLossColors = (variant: ProfitLossVariant) => {
  return profitLossColors[variant];
};
