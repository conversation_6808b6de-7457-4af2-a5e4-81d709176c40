-- Trading Dashboard Database Schema
-- Designed for comprehensive trade tracking and analysis

-- Core trade information
CREATE TABLE trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- RD-Cont, FVG-RD, etc.
    session VARCHAR(50), -- NY Open, Lunch Macro, MOC, etc.
    setup TEXT, -- Trading setup description

    -- Timing columns
    entry_time TIME,
    exit_time TIME,
    fvg_time TIME,
    balance_time TIME,
    rd_time TIME,
    target_hit_time TIME,
    time_relative_to_model INTEGER, -- in minutes

    -- Basic trade data
    direction VARCHAR(5) CHECK (direction IN ('Long', 'Short')),
    market VARCHAR(10) DEFAULT 'MNQ',
    r_multiple DECIMAL(10,2),

    -- Performance metrics
    entry_price DECIMAL(10,2),
    exit_price DECIMAL(10,2),
    risk_points DECIMAL(10,2),
    achieved_pl DECIMAL(10,2),
    no_of_contracts DECIMAL(4,2) DEFAULT 1,

    -- Trade quality
    win_loss VARCHAR(4) CHECK (win_loss IN ('Win', 'Loss')),
    pattern_quality_rating DECIMAL(2,1) CHECK (pattern_quality_rating BETWEEN 1 AND 5),

    -- Setup Components (modular setup construction)
    setup_constant VARCHAR(100),
    setup_action VARCHAR(100),
    setup_variable VARCHAR(100),
    setup_entry VARCHAR(100),

    -- Notes and context
    notes TEXT,
    news TEXT,

    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- FVG-specific trade details
CREATE TABLE trade_fvg_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trade_id INTEGER NOT NULL,
    fvg_date DATE,
    rd_type VARCHAR(50), -- True-RD, IMM-RD, etc.
    entry_version VARCHAR(50), -- Simple-Entry, Complex-Entry, etc.
    draw_on_liquidity TEXT, -- MNOR-FVG, AM-FPFVG, etc.

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE
);

-- Additional trade context and setup information
CREATE TABLE trade_setups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trade_id INTEGER NOT NULL,
    primary_setup TEXT,
    secondary_setup TEXT,
    liquidity_taken TEXT,
    additional_fvgs TEXT,
    dol TEXT, -- Direction of Liquidity info

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE
);

-- TradingView links and DOL analysis
CREATE TABLE trade_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trade_id INTEGER NOT NULL,
    tradingview_link VARCHAR(500),
    dol_target_type VARCHAR(50),
    beyond_target VARCHAR(50),
    clustering VARCHAR(50),
    path_quality VARCHAR(50),
    idr_context VARCHAR(100),
    sequential_fvg_rd VARCHAR(100),
    dol_notes TEXT,

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE
);

-- Sessions and time blocks for analysis
CREATE TABLE trading_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    start_time TIME,
    end_time TIME,
    description TEXT
);

-- Insert common sessions
INSERT INTO trading_sessions (name, start_time, end_time, description) VALUES
('Pre-Market', '04:00:00', '09:30:00', 'Pre-market trading hours'),
('NY Open', '09:30:00', '10:30:00', 'New York opening hour'),
('10:50-11:10', '10:50:00', '11:10:00', 'Mid-morning macro window'),
('11:50-12:10', '11:50:00', '12:10:00', 'Pre-lunch macro window'),
('Lunch Macro', '12:00:00', '13:30:00', 'Lunch time trading'),
('13:50-14:10', '13:50:00', '14:10:00', 'Post-lunch macro window'),
('14:50-15:10', '14:50:00', '15:10:00', 'Pre-close macro window'),
('15:15-15:45', '15:15:00', '15:45:00', 'Late afternoon window'),
('MOC', '15:45:00', '16:00:00', 'Market on close'),
('Post MOC', '16:00:00', '20:00:00', 'After hours trading');

-- Indexes for performance
CREATE INDEX idx_trades_date ON trades(date);
CREATE INDEX idx_trades_model_type ON trades(model_type);
CREATE INDEX idx_trades_session ON trades(session);
CREATE INDEX idx_trades_win_loss ON trades(win_loss);
CREATE INDEX idx_trades_r_multiple ON trades(r_multiple);

-- Views for common queries
CREATE VIEW trade_summary AS
SELECT
    t.id,
    t.date,
    t.model_type,
    t.session,
    t.direction,
    t.r_multiple,
    t.win_loss,
    t.achieved_pl,
    t.pattern_quality_rating,
    tfd.rd_type,
    tfd.entry_version
FROM trades t
LEFT JOIN trade_fvg_details tfd ON t.id = tfd.trade_id;

CREATE VIEW performance_metrics AS
SELECT
    COUNT(*) as total_trades,
    SUM(CASE WHEN win_loss = 'Win' THEN 1 ELSE 0 END) as wins,
    SUM(CASE WHEN win_loss = 'Loss' THEN 1 ELSE 0 END) as losses,
    ROUND(
        (SUM(CASE WHEN win_loss = 'Win' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2
    ) as win_rate,
    ROUND(SUM(achieved_pl), 2) as total_pl,
    ROUND(AVG(r_multiple), 2) as avg_r_multiple,
    ROUND(AVG(pattern_quality_rating), 2) as avg_quality_rating
FROM trades
WHERE achieved_pl IS NOT NULL;

-- Example queries you'll commonly use:

-- Get all trades for a specific date range
-- SELECT * FROM trade_summary WHERE date BETWEEN '2025-04-01' AND '2025-04-30';

-- Performance by model type
-- SELECT model_type, COUNT(*) as trades,
--        SUM(CASE WHEN win_loss = 'Win' THEN 1 ELSE 0 END) as wins,
--        ROUND(AVG(r_multiple), 2) as avg_r_multiple
-- FROM trades GROUP BY model_type;

-- Best performing sessions
-- SELECT session, COUNT(*) as trades,
--        ROUND(AVG(r_multiple), 2) as avg_r_multiple,
--        SUM(achieved_pl) as total_pl
-- FROM trades GROUP BY session ORDER BY avg_r_multiple DESC;