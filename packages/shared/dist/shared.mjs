import Ie, { useState as se, useR<PERSON> as Mr, Component as xt, useEffect as xe, use<PERSON>em<PERSON> as te, use<PERSON><PERSON><PERSON> as pe, createContext as Ar, useContext as Or, useReducer as bt } from "react";
import u, { css as g, keyframes as Le, createGlobalStyle as yt, Theme<PERSON>rovider as vt } from "styled-components";
import { createPortal as wt } from "react-dom";
var St = /* @__PURE__ */ ((e) => (e.LONG = "LONG", e.SHORT = "SHORT", e))(St || {}), Et = /* @__PURE__ */ ((e) => (e.OPEN = "OPEN", e.CLOSED = "CLOSED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e.PENDING = "PENDING", e))(Et || {}), It = /* @__PURE__ */ ((e) => (e.MARKET = "MARKET", e.LIMIT = "LIMIT", e.STOP = "STOP", e.STOP_LIMIT = "STOP_LIMIT", e))(It || {}), Ct = /* @__PURE__ */ ((e) => (e.BUY = "BUY", e.SELL = "SELL", e))(Ct || {}), jt = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.FILLED = "FILLED", e.PARTIALLY_FILLED = "PARTIALLY_FILLED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e))(jt || {}), Tt = /* @__PURE__ */ ((e) => (e.GTC = "GTC", e.IOC = "IOC", e.FOK = "FOK", e.DAY = "DAY", e))(Tt || {});
const z = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  R_MULTIPLE: "r_multiple",
  DATE: "date",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  ACHIEVED_PL: "achieved_pl",
  PATTERN_QUALITY_RATING: "pattern_quality_rating"
};
class _t {
  constructor() {
    this.dbName = "adhd-trading-dashboard", this.version = 2, this.db = null, this.stores = {
      trades: "trades",
      fvg_details: "trade_fvg_details",
      setups: "trade_setups",
      analysis: "trade_analysis",
      sessions: "trading_sessions"
    };
  }
  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  async initDB() {
    return this.db ? this.db : new Promise((r, s) => {
      const n = indexedDB.open(this.dbName, this.version);
      n.onupgradeneeded = (a) => {
        var l;
        const p = a.target.result;
        if (!p.objectStoreNames.contains(this.stores.trades)) {
          const f = p.createObjectStore(this.stores.trades, {
            keyPath: "id",
            autoIncrement: !0
          });
          f.createIndex(z.DATE, z.DATE, {
            unique: !1
          }), f.createIndex(z.MODEL_TYPE, z.MODEL_TYPE, {
            unique: !1
          }), f.createIndex(z.SESSION, z.SESSION, {
            unique: !1
          }), f.createIndex(z.WIN_LOSS, z.WIN_LOSS, {
            unique: !1
          }), f.createIndex(z.R_MULTIPLE, z.R_MULTIPLE, {
            unique: !1
          });
        }
        if (p.objectStoreNames.contains(this.stores.fvg_details) || p.createObjectStore(this.stores.fvg_details, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), p.objectStoreNames.contains(this.stores.setups) || p.createObjectStore(this.stores.setups, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), p.objectStoreNames.contains(this.stores.analysis) || p.createObjectStore(this.stores.analysis, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), !p.objectStoreNames.contains(this.stores.sessions)) {
          p.createObjectStore(this.stores.sessions, {
            keyPath: "id",
            autoIncrement: !0
          }).createIndex("name", "name", {
            unique: !0
          });
          const d = [{
            name: "Pre-Market",
            start_time: "04:00:00",
            end_time: "09:30:00",
            description: "Pre-market trading hours"
          }, {
            name: "NY Open",
            start_time: "09:30:00",
            end_time: "10:30:00",
            description: "New York opening hour"
          }, {
            name: "10:50-11:10",
            start_time: "10:50:00",
            end_time: "11:10:00",
            description: "Mid-morning macro window"
          }, {
            name: "11:50-12:10",
            start_time: "11:50:00",
            end_time: "12:10:00",
            description: "Pre-lunch macro window"
          }, {
            name: "Lunch Macro",
            start_time: "12:00:00",
            end_time: "13:30:00",
            description: "Lunch time trading"
          }, {
            name: "13:50-14:10",
            start_time: "13:50:00",
            end_time: "14:10:00",
            description: "Post-lunch macro window"
          }, {
            name: "14:50-15:10",
            start_time: "14:50:00",
            end_time: "15:10:00",
            description: "Pre-close macro window"
          }, {
            name: "15:15-15:45",
            start_time: "15:15:00",
            end_time: "15:45:00",
            description: "Late afternoon window"
          }, {
            name: "MOC",
            start_time: "15:45:00",
            end_time: "16:00:00",
            description: "Market on close"
          }, {
            name: "Post MOC",
            start_time: "16:00:00",
            end_time: "20:00:00",
            description: "After hours trading"
          }];
          (l = n.transaction) == null || l.addEventListener("complete", () => {
            const c = p.transaction([this.stores.sessions], "readwrite").objectStore(this.stores.sessions);
            d.forEach((h) => c.add(h));
          });
        }
      }, n.onsuccess = (a) => {
        this.db = a.target.result, r(this.db);
      }, n.onerror = (a) => {
        console.error("Error opening IndexedDB:", a), s(new Error("Failed to open IndexedDB"));
      };
    });
  }
  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(r) {
    try {
      const s = await this.initDB();
      return new Promise((n, a) => {
        const p = s.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        p.onerror = (i) => {
          console.error("Transaction error:", i), a(new Error("Failed to save trade with details"));
        };
        const l = p.objectStore(this.stores.trades), f = {
          ...r.trade,
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, d = l.add(f);
        d.onsuccess = () => {
          const i = d.result, c = [];
          if (r.fvg_details) {
            const h = p.objectStore(this.stores.fvg_details), S = {
              ...r.fvg_details,
              trade_id: i
            };
            c.push(new Promise((I, v) => {
              const w = h.add(S);
              w.onsuccess = () => I(), w.onerror = () => v(new Error("Failed to save FVG details"));
            }));
          }
          if (r.setup) {
            const h = p.objectStore(this.stores.setups), S = {
              ...r.setup,
              trade_id: i
            };
            c.push(new Promise((I, v) => {
              const w = h.add(S);
              w.onsuccess = () => I(), w.onerror = () => v(new Error("Failed to save setup data"));
            }));
          }
          if (r.analysis) {
            const h = p.objectStore(this.stores.analysis), S = {
              ...r.analysis,
              trade_id: i
            };
            c.push(new Promise((I, v) => {
              const w = h.add(S);
              w.onsuccess = () => I(), w.onerror = () => v(new Error("Failed to save analysis data"));
            }));
          }
          p.oncomplete = () => {
            n(i);
          };
        }, d.onerror = (i) => {
          console.error("Error saving trade:", i), a(new Error("Failed to save trade"));
        };
      });
    } catch (s) {
      throw console.error("Error in saveTradeWithDetails:", s), new Error("Failed to save trade with details");
    }
  }
  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(r) {
    try {
      const s = await this.initDB();
      return new Promise((n, a) => {
        const p = s.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), f = p.objectStore(this.stores.trades).get(r);
        f.onsuccess = () => {
          const d = f.result;
          if (!d) {
            n(null);
            return;
          }
          const i = {
            trade: d
          }, S = p.objectStore(this.stores.fvg_details).index("trade_id").get(r);
          S.onsuccess = () => {
            S.result && (i.fvg_details = S.result);
            const w = p.objectStore(this.stores.setups).index("trade_id").get(r);
            w.onsuccess = () => {
              w.result && (i.setup = w.result);
              const C = p.objectStore(this.stores.analysis).index("trade_id").get(r);
              C.onsuccess = () => {
                C.result && (i.analysis = C.result), n(i);
              }, C.onerror = (L) => {
                console.error("Error getting analysis data:", L), n(i);
              };
            }, w.onerror = (_) => {
              console.error("Error getting setup data:", _), n(i);
            };
          }, S.onerror = (I) => {
            console.error("Error getting FVG details:", I), n(i);
          };
        }, f.onerror = (d) => {
          console.error("Error getting trade:", d), a(new Error("Failed to get trade"));
        };
      });
    } catch (s) {
      return console.error("Error in getTradeById:", s), null;
    }
  }
  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const r = await this.initDB();
      return new Promise((s, n) => {
        const l = r.transaction([this.stores.trades], "readonly").objectStore(this.stores.trades).getAll();
        l.onsuccess = () => {
          const f = l.result;
          if (f.length === 0) {
            s({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: "all",
              startDate: "",
              endDate: ""
            });
            return;
          }
          const d = f.length, i = f.filter((R) => R[z.WIN_LOSS] === "Win").length, c = f.filter((R) => R[z.WIN_LOSS] === "Loss").length, h = d > 0 ? i / d * 100 : 0, S = f.filter((R) => R.achieved_pl !== void 0).map((R) => R.achieved_pl), I = S.reduce((R, H) => R + H, 0), v = S.filter((R) => R > 0), w = S.filter((R) => R < 0), _ = v.length > 0 ? v.reduce((R, H) => R + H, 0) / v.length : 0, b = w.length > 0 ? Math.abs(w.reduce((R, H) => R + H, 0) / w.length) : 0, C = v.length > 0 ? Math.max(...v) : 0, L = w.length > 0 ? Math.abs(Math.min(...w)) : 0, q = v.reduce((R, H) => R + H, 0), M = Math.abs(w.reduce((R, H) => R + H, 0)), Y = M > 0 ? q / M : 0, $ = f.filter((R) => R[z.R_MULTIPLE] !== void 0).map((R) => R[z.R_MULTIPLE]), A = $.length > 0 ? $.reduce((R, H) => R + H, 0) / $.length : 0, k = A * (h / 100);
          let K = 0, U = 0, j = 0;
          for (const R of f)
            if (R.achieved_pl !== void 0) {
              K += R.achieved_pl, K > U && (U = K);
              const H = U - K;
              H > j && (j = H);
            }
          const W = U > 0 ? j / U * 100 : 0, J = $.length > 0 ? Math.sqrt($.length) * A / Math.sqrt($.reduce((R, H) => R + Math.pow(H - A, 2), 0) / $.length) : 0, X = f.map((R) => R.date).sort(), ae = X.length > 0 ? X[0] : "", Z = X.length > 0 ? X[X.length - 1] : "";
          s({
            totalTrades: d,
            winningTrades: i,
            losingTrades: c,
            winRate: h,
            profitFactor: Y,
            averageWin: _,
            averageLoss: b,
            largestWin: C,
            largestLoss: L,
            totalPnl: I,
            maxDrawdown: j,
            maxDrawdownPercent: W,
            sharpeRatio: 0,
            // Would need daily returns to calculate
            sortinoRatio: 0,
            // Would need daily returns to calculate
            calmarRatio: 0,
            // Would need daily returns to calculate
            averageRMultiple: A,
            expectancy: k,
            sqn: J,
            period: "all",
            startDate: ae,
            endDate: Z
          });
        }, l.onerror = (f) => {
          console.error("Error getting performance metrics:", f), n(new Error("Failed to get performance metrics"));
        };
      });
    } catch (r) {
      throw console.error("Error in getPerformanceMetrics:", r), new Error("Failed to get performance metrics");
    }
  }
  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(r) {
    try {
      const s = await this.initDB();
      return new Promise((n, a) => {
        const p = s.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), f = p.objectStore(this.stores.trades).getAll();
        f.onsuccess = async () => {
          let d = f.result;
          r.dateFrom && (d = d.filter((c) => c.date >= r.dateFrom)), r.dateTo && (d = d.filter((c) => c.date <= r.dateTo)), r.model_type && (d = d.filter((c) => c[z.MODEL_TYPE] === r.model_type)), r.session && (d = d.filter((c) => c[z.SESSION] === r.session)), r.direction && (d = d.filter((c) => c[z.DIRECTION] === r.direction)), r.win_loss && (d = d.filter((c) => c[z.WIN_LOSS] === r.win_loss)), r.market && (d = d.filter((c) => c[z.MARKET] === r.market)), r.min_r_multiple !== void 0 && (d = d.filter((c) => c[z.R_MULTIPLE] !== void 0 && c[z.R_MULTIPLE] >= r.min_r_multiple)), r.max_r_multiple !== void 0 && (d = d.filter((c) => c[z.R_MULTIPLE] !== void 0 && c[z.R_MULTIPLE] <= r.max_r_multiple)), r.min_pattern_quality !== void 0 && (d = d.filter((c) => c[z.PATTERN_QUALITY_RATING] !== void 0 && c[z.PATTERN_QUALITY_RATING] >= r.min_pattern_quality)), r.max_pattern_quality !== void 0 && (d = d.filter((c) => c[z.PATTERN_QUALITY_RATING] !== void 0 && c[z.PATTERN_QUALITY_RATING] <= r.max_pattern_quality));
          const i = [];
          for (const c of d) {
            const h = {
              trade: c
            }, v = p.objectStore(this.stores.fvg_details).index("trade_id").get(c.id);
            await new Promise((M) => {
              v.onsuccess = () => {
                v.result && (h.fvg_details = v.result), M();
              }, v.onerror = () => M();
            });
            const b = p.objectStore(this.stores.setups).index("trade_id").get(c.id);
            await new Promise((M) => {
              b.onsuccess = () => {
                b.result && (h.setup = b.result), M();
              }, b.onerror = () => M();
            });
            const q = p.objectStore(this.stores.analysis).index("trade_id").get(c.id);
            await new Promise((M) => {
              q.onsuccess = () => {
                q.result && (h.analysis = q.result), M();
              }, q.onerror = () => M();
            }), i.push(h);
          }
          n(i);
        }, f.onerror = (d) => {
          console.error("Error filtering trades:", d), a(new Error("Failed to filter trades"));
        };
      });
    } catch (s) {
      throw console.error("Error in filterTrades:", s), new Error("Failed to filter trades");
    }
  }
  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades() {
    try {
      return await this.filterTrades({});
    } catch (r) {
      return console.error("Error in getAllTrades:", r), [];
    }
  }
  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(r) {
    try {
      const s = await this.initDB();
      return new Promise((n, a) => {
        const p = s.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        p.onerror = (b) => {
          console.error("Transaction error:", b), a(new Error("Failed to delete trade"));
        };
        const d = p.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));
        d.onsuccess = (b) => {
          const C = b.target.result;
          C && (C.delete(), C.continue());
        };
        const h = p.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));
        h.onsuccess = (b) => {
          const C = b.target.result;
          C && (C.delete(), C.continue());
        };
        const v = p.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));
        v.onsuccess = (b) => {
          const C = b.target.result;
          C && (C.delete(), C.continue());
        };
        const _ = p.objectStore(this.stores.trades).delete(r);
        p.oncomplete = () => {
          n();
        }, _.onerror = (b) => {
          console.error("Error deleting trade:", b), a(new Error("Failed to delete trade"));
        };
      });
    } catch (s) {
      throw console.error("Error in deleteTrade:", s), new Error("Failed to delete trade");
    }
  }
  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(r, s) {
    try {
      const n = await this.initDB();
      return new Promise((a, p) => {
        const l = n.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        l.onerror = (c) => {
          console.error("Transaction error:", c), p(new Error("Failed to update trade"));
        };
        const f = l.objectStore(this.stores.trades), d = {
          ...s.trade,
          id: r,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, i = f.put(d);
        i.onsuccess = () => {
          if (s.fvg_details) {
            const c = l.objectStore(this.stores.fvg_details), h = {
              ...s.fvg_details,
              trade_id: r
            };
            c.put(h);
          }
          if (s.setup) {
            const c = l.objectStore(this.stores.setups), h = {
              ...s.setup,
              trade_id: r
            };
            c.put(h);
          }
          if (s.analysis) {
            const c = l.objectStore(this.stores.analysis), h = {
              ...s.analysis,
              trade_id: r
            };
            c.put(h);
          }
        }, l.oncomplete = () => {
          a();
        }, i.onerror = (c) => {
          console.error("Error updating trade:", c), p(new Error("Failed to update trade"));
        };
      });
    } catch (n) {
      throw console.error("Error in updateTradeWithDetails:", n), new Error("Failed to update trade");
    }
  }
}
const $r = new _t(), is = $r, cs = $r;
var t = {}, kt = {
  get exports() {
    return t;
  },
  set exports(e) {
    t = e;
  }
}, ye = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var br;
function Rt() {
  if (br)
    return ye;
  br = 1;
  var e = Ie, r = Symbol.for("react.element"), s = Symbol.for("react.fragment"), n = Object.prototype.hasOwnProperty, a = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, p = { key: !0, ref: !0, __self: !0, __source: !0 };
  function l(f, d, i) {
    var c, h = {}, S = null, I = null;
    i !== void 0 && (S = "" + i), d.key !== void 0 && (S = "" + d.key), d.ref !== void 0 && (I = d.ref);
    for (c in d)
      n.call(d, c) && !p.hasOwnProperty(c) && (h[c] = d[c]);
    if (f && f.defaultProps)
      for (c in d = f.defaultProps, d)
        h[c] === void 0 && (h[c] = d[c]);
    return { $$typeof: r, type: f, key: S, ref: I, props: h, _owner: a.current };
  }
  return ye.Fragment = s, ye.jsx = l, ye.jsxs = l, ye;
}
var ve = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var yr;
function Nt() {
  return yr || (yr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Ie, r = Symbol.for("react.element"), s = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), a = Symbol.for("react.strict_mode"), p = Symbol.for("react.profiler"), l = Symbol.for("react.provider"), f = Symbol.for("react.context"), d = Symbol.for("react.forward_ref"), i = Symbol.for("react.suspense"), c = Symbol.for("react.suspense_list"), h = Symbol.for("react.memo"), S = Symbol.for("react.lazy"), I = Symbol.for("react.offscreen"), v = Symbol.iterator, w = "@@iterator";
    function _(o) {
      if (o === null || typeof o != "object")
        return null;
      var m = v && o[v] || o[w];
      return typeof m == "function" ? m : null;
    }
    var b = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function C(o) {
      {
        for (var m = arguments.length, x = new Array(m > 1 ? m - 1 : 0), T = 1; T < m; T++)
          x[T - 1] = arguments[T];
        L("error", o, x);
      }
    }
    function L(o, m, x) {
      {
        var T = b.ReactDebugCurrentFrame, D = T.getStackAddendum();
        D !== "" && (m += "%s", x = x.concat([D]));
        var B = x.map(function(P) {
          return String(P);
        });
        B.unshift("Warning: " + m), Function.prototype.apply.call(console[o], console, B);
      }
    }
    var q = !1, M = !1, Y = !1, $ = !1, A = !1, k;
    k = Symbol.for("react.module.reference");
    function K(o) {
      return !!(typeof o == "string" || typeof o == "function" || o === n || o === p || A || o === a || o === i || o === c || $ || o === I || q || M || Y || typeof o == "object" && o !== null && (o.$$typeof === S || o.$$typeof === h || o.$$typeof === l || o.$$typeof === f || o.$$typeof === d || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      o.$$typeof === k || o.getModuleId !== void 0));
    }
    function U(o, m, x) {
      var T = o.displayName;
      if (T)
        return T;
      var D = m.displayName || m.name || "";
      return D !== "" ? x + "(" + D + ")" : x;
    }
    function j(o) {
      return o.displayName || "Context";
    }
    function W(o) {
      if (o == null)
        return null;
      if (typeof o.tag == "number" && C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof o == "function")
        return o.displayName || o.name || null;
      if (typeof o == "string")
        return o;
      switch (o) {
        case n:
          return "Fragment";
        case s:
          return "Portal";
        case p:
          return "Profiler";
        case a:
          return "StrictMode";
        case i:
          return "Suspense";
        case c:
          return "SuspenseList";
      }
      if (typeof o == "object")
        switch (o.$$typeof) {
          case f:
            var m = o;
            return j(m) + ".Consumer";
          case l:
            var x = o;
            return j(x._context) + ".Provider";
          case d:
            return U(o, o.render, "ForwardRef");
          case h:
            var T = o.displayName || null;
            return T !== null ? T : W(o.type) || "Memo";
          case S: {
            var D = o, B = D._payload, P = D._init;
            try {
              return W(P(B));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var J = Object.assign, X = 0, ae, Z, R, H, fe, O, ce;
    function or() {
    }
    or.__reactDisabledLog = !0;
    function Hr() {
      {
        if (X === 0) {
          ae = console.log, Z = console.info, R = console.warn, H = console.error, fe = console.group, O = console.groupCollapsed, ce = console.groupEnd;
          var o = {
            configurable: !0,
            enumerable: !0,
            value: or,
            writable: !0
          };
          Object.defineProperties(console, {
            info: o,
            log: o,
            warn: o,
            error: o,
            group: o,
            groupCollapsed: o,
            groupEnd: o
          });
        }
        X++;
      }
    }
    function Vr() {
      {
        if (X--, X === 0) {
          var o = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: J({}, o, {
              value: ae
            }),
            info: J({}, o, {
              value: Z
            }),
            warn: J({}, o, {
              value: R
            }),
            error: J({}, o, {
              value: H
            }),
            group: J({}, o, {
              value: fe
            }),
            groupCollapsed: J({}, o, {
              value: O
            }),
            groupEnd: J({}, o, {
              value: ce
            })
          });
        }
        X < 0 && C("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var De = b.ReactCurrentDispatcher, Me;
    function je(o, m, x) {
      {
        if (Me === void 0)
          try {
            throw Error();
          } catch (D) {
            var T = D.stack.trim().match(/\n( *(at )?)/);
            Me = T && T[1] || "";
          }
        return `
` + Me + o;
      }
    }
    var Ae = !1, Te;
    {
      var Gr = typeof WeakMap == "function" ? WeakMap : Map;
      Te = new Gr();
    }
    function nr(o, m) {
      if (!o || Ae)
        return "";
      {
        var x = Te.get(o);
        if (x !== void 0)
          return x;
      }
      var T;
      Ae = !0;
      var D = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var B;
      B = De.current, De.current = null, Hr();
      try {
        if (m) {
          var P = function() {
            throw Error();
          };
          if (Object.defineProperty(P.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(P, []);
            } catch (le) {
              T = le;
            }
            Reflect.construct(o, [], P);
          } else {
            try {
              P.call();
            } catch (le) {
              T = le;
            }
            o.call(P.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (le) {
            T = le;
          }
          o();
        }
      } catch (le) {
        if (le && T && typeof le.stack == "string") {
          for (var N = le.stack.split(`
`), ee = T.stack.split(`
`), V = N.length - 1, G = ee.length - 1; V >= 1 && G >= 0 && N[V] !== ee[G]; )
            G--;
          for (; V >= 1 && G >= 0; V--, G--)
            if (N[V] !== ee[G]) {
              if (V !== 1 || G !== 1)
                do
                  if (V--, G--, G < 0 || N[V] !== ee[G]) {
                    var ne = `
` + N[V].replace(" at new ", " at ");
                    return o.displayName && ne.includes("<anonymous>") && (ne = ne.replace("<anonymous>", o.displayName)), typeof o == "function" && Te.set(o, ne), ne;
                  }
                while (V >= 1 && G >= 0);
              break;
            }
        }
      } finally {
        Ae = !1, De.current = B, Vr(), Error.prepareStackTrace = D;
      }
      var me = o ? o.displayName || o.name : "", xr = me ? je(me) : "";
      return typeof o == "function" && Te.set(o, xr), xr;
    }
    function Qr(o, m, x) {
      return nr(o, !1);
    }
    function Jr(o) {
      var m = o.prototype;
      return !!(m && m.isReactComponent);
    }
    function _e(o, m, x) {
      if (o == null)
        return "";
      if (typeof o == "function")
        return nr(o, Jr(o));
      if (typeof o == "string")
        return je(o);
      switch (o) {
        case i:
          return je("Suspense");
        case c:
          return je("SuspenseList");
      }
      if (typeof o == "object")
        switch (o.$$typeof) {
          case d:
            return Qr(o.render);
          case h:
            return _e(o.type, m, x);
          case S: {
            var T = o, D = T._payload, B = T._init;
            try {
              return _e(B(D), m, x);
            } catch {
            }
          }
        }
      return "";
    }
    var ke = Object.prototype.hasOwnProperty, sr = {}, ar = b.ReactDebugCurrentFrame;
    function Re(o) {
      if (o) {
        var m = o._owner, x = _e(o.type, o._source, m ? m.type : null);
        ar.setExtraStackFrame(x);
      } else
        ar.setExtraStackFrame(null);
    }
    function Kr(o, m, x, T, D) {
      {
        var B = Function.call.bind(ke);
        for (var P in o)
          if (B(o, P)) {
            var N = void 0;
            try {
              if (typeof o[P] != "function") {
                var ee = Error((T || "React class") + ": " + x + " type `" + P + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof o[P] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ee.name = "Invariant Violation", ee;
              }
              N = o[P](m, P, T, x, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (V) {
              N = V;
            }
            N && !(N instanceof Error) && (Re(D), C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", T || "React class", x, P, typeof N), Re(null)), N instanceof Error && !(N.message in sr) && (sr[N.message] = !0, Re(D), C("Failed %s type: %s", x, N.message), Re(null));
          }
      }
    }
    var Xr = Array.isArray;
    function Oe(o) {
      return Xr(o);
    }
    function Zr(o) {
      {
        var m = typeof Symbol == "function" && Symbol.toStringTag, x = m && o[Symbol.toStringTag] || o.constructor.name || "Object";
        return x;
      }
    }
    function et(o) {
      try {
        return ir(o), !1;
      } catch {
        return !0;
      }
    }
    function ir(o) {
      return "" + o;
    }
    function cr(o) {
      if (et(o))
        return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Zr(o)), ir(o);
    }
    var be = b.ReactCurrentOwner, rt = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, lr, dr, $e;
    $e = {};
    function tt(o) {
      if (ke.call(o, "ref")) {
        var m = Object.getOwnPropertyDescriptor(o, "ref").get;
        if (m && m.isReactWarning)
          return !1;
      }
      return o.ref !== void 0;
    }
    function ot(o) {
      if (ke.call(o, "key")) {
        var m = Object.getOwnPropertyDescriptor(o, "key").get;
        if (m && m.isReactWarning)
          return !1;
      }
      return o.key !== void 0;
    }
    function nt(o, m) {
      if (typeof o.ref == "string" && be.current && m && be.current.stateNode !== m) {
        var x = W(be.current.type);
        $e[x] || (C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', W(be.current.type), o.ref), $e[x] = !0);
      }
    }
    function st(o, m) {
      {
        var x = function() {
          lr || (lr = !0, C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", m));
        };
        x.isReactWarning = !0, Object.defineProperty(o, "key", {
          get: x,
          configurable: !0
        });
      }
    }
    function at(o, m) {
      {
        var x = function() {
          dr || (dr = !0, C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", m));
        };
        x.isReactWarning = !0, Object.defineProperty(o, "ref", {
          get: x,
          configurable: !0
        });
      }
    }
    var it = function(o, m, x, T, D, B, P) {
      var N = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: r,
        // Built-in properties that belong on the element
        type: o,
        key: m,
        ref: x,
        props: P,
        // Record the component responsible for creating this element.
        _owner: B
      };
      return N._store = {}, Object.defineProperty(N._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(N, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: T
      }), Object.defineProperty(N, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: D
      }), Object.freeze && (Object.freeze(N.props), Object.freeze(N)), N;
    };
    function ct(o, m, x, T, D) {
      {
        var B, P = {}, N = null, ee = null;
        x !== void 0 && (cr(x), N = "" + x), ot(m) && (cr(m.key), N = "" + m.key), tt(m) && (ee = m.ref, nt(m, D));
        for (B in m)
          ke.call(m, B) && !rt.hasOwnProperty(B) && (P[B] = m[B]);
        if (o && o.defaultProps) {
          var V = o.defaultProps;
          for (B in V)
            P[B] === void 0 && (P[B] = V[B]);
        }
        if (N || ee) {
          var G = typeof o == "function" ? o.displayName || o.name || "Unknown" : o;
          N && st(P, G), ee && at(P, G);
        }
        return it(o, N, ee, D, T, be.current, P);
      }
    }
    var Fe = b.ReactCurrentOwner, ur = b.ReactDebugCurrentFrame;
    function ge(o) {
      if (o) {
        var m = o._owner, x = _e(o.type, o._source, m ? m.type : null);
        ur.setExtraStackFrame(x);
      } else
        ur.setExtraStackFrame(null);
    }
    var Be;
    Be = !1;
    function ze(o) {
      return typeof o == "object" && o !== null && o.$$typeof === r;
    }
    function pr() {
      {
        if (Fe.current) {
          var o = W(Fe.current.type);
          if (o)
            return `

Check the render method of \`` + o + "`.";
        }
        return "";
      }
    }
    function lt(o) {
      {
        if (o !== void 0) {
          var m = o.fileName.replace(/^.*[\\\/]/, ""), x = o.lineNumber;
          return `

Check your code at ` + m + ":" + x + ".";
        }
        return "";
      }
    }
    var fr = {};
    function dt(o) {
      {
        var m = pr();
        if (!m) {
          var x = typeof o == "string" ? o : o.displayName || o.name;
          x && (m = `

Check the top-level render call using <` + x + ">.");
        }
        return m;
      }
    }
    function gr(o, m) {
      {
        if (!o._store || o._store.validated || o.key != null)
          return;
        o._store.validated = !0;
        var x = dt(m);
        if (fr[x])
          return;
        fr[x] = !0;
        var T = "";
        o && o._owner && o._owner !== Fe.current && (T = " It was passed a child from " + W(o._owner.type) + "."), ge(o), C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', x, T), ge(null);
      }
    }
    function mr(o, m) {
      {
        if (typeof o != "object")
          return;
        if (Oe(o))
          for (var x = 0; x < o.length; x++) {
            var T = o[x];
            ze(T) && gr(T, m);
          }
        else if (ze(o))
          o._store && (o._store.validated = !0);
        else if (o) {
          var D = _(o);
          if (typeof D == "function" && D !== o.entries)
            for (var B = D.call(o), P; !(P = B.next()).done; )
              ze(P.value) && gr(P.value, m);
        }
      }
    }
    function ut(o) {
      {
        var m = o.type;
        if (m == null || typeof m == "string")
          return;
        var x;
        if (typeof m == "function")
          x = m.propTypes;
        else if (typeof m == "object" && (m.$$typeof === d || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        m.$$typeof === h))
          x = m.propTypes;
        else
          return;
        if (x) {
          var T = W(m);
          Kr(x, o.props, "prop", T, o);
        } else if (m.PropTypes !== void 0 && !Be) {
          Be = !0;
          var D = W(m);
          C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", D || "Unknown");
        }
        typeof m.getDefaultProps == "function" && !m.getDefaultProps.isReactClassApproved && C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function pt(o) {
      {
        for (var m = Object.keys(o.props), x = 0; x < m.length; x++) {
          var T = m[x];
          if (T !== "children" && T !== "key") {
            ge(o), C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", T), ge(null);
            break;
          }
        }
        o.ref !== null && (ge(o), C("Invalid attribute `ref` supplied to `React.Fragment`."), ge(null));
      }
    }
    function hr(o, m, x, T, D, B) {
      {
        var P = K(o);
        if (!P) {
          var N = "";
          (o === void 0 || typeof o == "object" && o !== null && Object.keys(o).length === 0) && (N += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ee = lt(D);
          ee ? N += ee : N += pr();
          var V;
          o === null ? V = "null" : Oe(o) ? V = "array" : o !== void 0 && o.$$typeof === r ? (V = "<" + (W(o.type) || "Unknown") + " />", N = " Did you accidentally export a JSX literal instead of a component?") : V = typeof o, C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", V, N);
        }
        var G = ct(o, m, x, D, B);
        if (G == null)
          return G;
        if (P) {
          var ne = m.children;
          if (ne !== void 0)
            if (T)
              if (Oe(ne)) {
                for (var me = 0; me < ne.length; me++)
                  mr(ne[me], o);
                Object.freeze && Object.freeze(ne);
              } else
                C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              mr(ne, o);
        }
        return o === n ? pt(G) : ut(G), G;
      }
    }
    function ft(o, m, x) {
      return hr(o, m, x, !0);
    }
    function gt(o, m, x) {
      return hr(o, m, x, !1);
    }
    var mt = gt, ht = ft;
    ve.Fragment = n, ve.jsx = mt, ve.jsxs = ht;
  }()), ve;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = Rt() : e.exports = Nt();
})(kt);
const Lt = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, Pt = (e, r, s = !1) => g(["", ""], ({
  theme: n
}) => {
  let a, p, l;
  switch (e) {
    case "primary":
      a = r ? n.colors.primary : `${n.colors.primary}20`, p = r ? n.colors.textInverse : n.colors.primary, l = n.colors.primary;
      break;
    case "secondary":
      a = r ? n.colors.secondary : `${n.colors.secondary}20`, p = r ? n.colors.textInverse : n.colors.secondary, l = n.colors.secondary;
      break;
    case "success":
      a = r ? n.colors.success : `${n.colors.success}20`, p = r ? n.colors.textInverse : n.colors.success, l = n.colors.success;
      break;
    case "warning":
      a = r ? n.colors.warning : `${n.colors.warning}20`, p = r ? n.colors.textInverse : n.colors.warning, l = n.colors.warning;
      break;
    case "error":
      a = r ? n.colors.error : `${n.colors.error}20`, p = r ? n.colors.textInverse : n.colors.error, l = n.colors.error;
      break;
    case "info":
      a = r ? n.colors.info : `${n.colors.info}20`, p = r ? n.colors.textInverse : n.colors.info, l = n.colors.info;
      break;
    case "neutral":
      a = r ? n.colors.textSecondary : `${n.colors.textSecondary}10`, p = r ? n.colors.textInverse : n.colors.textSecondary, l = n.colors.textSecondary;
      break;
    default:
      a = r ? n.colors.textSecondary : `${n.colors.textSecondary}20`, p = r ? n.colors.textInverse : n.colors.textSecondary, l = n.colors.textSecondary;
  }
  return s ? `
          background-color: transparent;
          color: ${l};
          border: 1px solid ${l};
        ` : `
        background-color: ${a};
        color: ${p};
        border: 1px solid transparent;
      `;
}), Fr = /* @__PURE__ */ u.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), Dt = /* @__PURE__ */ u(Fr).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), Mt = /* @__PURE__ */ u(Fr).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), At = /* @__PURE__ */ u.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: r,
  dot: s
}) => s ? "50%" : r ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => Lt[e], ({
  variant: e,
  solid: r,
  outlined: s
}) => Pt(e, r, s || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), Pe = ({
  children: e,
  variant: r = "default",
  size: s = "medium",
  solid: n = !1,
  className: a,
  onClick: p,
  rounded: l = !1,
  dot: f = !1,
  counter: d = !1,
  outlined: i = !1,
  startIcon: c,
  endIcon: h,
  max: S,
  inline: I = !0
}) => {
  let v = e;
  return d && typeof e == "number" && S !== void 0 && e > S && (v = `${S}+`), /* @__PURE__ */ t.jsx(At, { variant: r, size: s, solid: n, clickable: !!p, className: a, onClick: p, rounded: l, dot: f, counter: d, outlined: i, inline: I, children: !f && /* @__PURE__ */ t.jsxs(t.Fragment, { children: [
    c && /* @__PURE__ */ t.jsx(Dt, { children: c }),
    v,
    h && /* @__PURE__ */ t.jsx(Mt, { children: h })
  ] }) });
}, Ot = /* @__PURE__ */ Le(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), $t = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], Ot, ({
  theme: e
}) => e.spacing.xs), Ft = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, Bt = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, zt = /* @__PURE__ */ u.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => Ft[e], ({
  variant: e = "primary"
}) => Bt[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: r
}) => r.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: r
}) => r.spacing.xs)), qt = /* @__PURE__ */ u.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), oe = ({
  children: e,
  variant: r = "primary",
  disabled: s = !1,
  loading: n = !1,
  size: a = "medium",
  fullWidth: p = !1,
  startIcon: l,
  endIcon: f,
  onClick: d,
  className: i,
  type: c = "button",
  ...h
}) => /* @__PURE__ */ t.jsx(zt, { variant: r, disabled: s || n, size: a, fullWidth: p, onClick: d, className: i, type: c, $hasStartIcon: !!l && !n, $hasEndIcon: !!f && !n, ...h, children: /* @__PURE__ */ t.jsxs(qt, { children: [
  n && /* @__PURE__ */ t.jsx($t, {}),
  !n && l,
  e,
  !n && f
] }) }), Yt = /* @__PURE__ */ u.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Wt = /* @__PURE__ */ u.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Ut = /* @__PURE__ */ u.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: s,
  isFocused: n
}) => r ? e.colors.error : s ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: s,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], s ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), vr = /* @__PURE__ */ u.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Ht = /* @__PURE__ */ u.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Vt = /* @__PURE__ */ u.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Gt = /* @__PURE__ */ u.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: s
}) => r ? e.colors.error : s ? e.colors.success : e.colors.textSecondary), he = ({
  value: e,
  onChange: r,
  placeholder: s,
  disabled: n = !1,
  error: a,
  type: p = "text",
  name: l,
  id: f,
  className: d,
  required: i = !1,
  autoComplete: c,
  label: h,
  helperText: S,
  startIcon: I,
  endIcon: v,
  loading: w = !1,
  success: _ = !1,
  clearable: b = !1,
  onClear: C,
  maxLength: L,
  showCharCount: q = !1,
  size: M = "medium",
  fullWidth: Y = !1,
  ...$
}) => {
  const [A, k] = se(!1), K = Mr(null), U = () => {
    C ? C() : r(""), K.current && K.current.focus();
  }, j = (Z) => {
    k(!0), $.onFocus && $.onFocus(Z);
  }, W = (Z) => {
    k(!1), $.onBlur && $.onBlur(Z);
  }, J = b && e && !n, X = (e == null ? void 0 : e.length) || 0, ae = q || L !== void 0 && L > 0;
  return /* @__PURE__ */ t.jsxs(Yt, { className: d, fullWidth: Y, children: [
    h && /* @__PURE__ */ t.jsxs(Wt, { htmlFor: f, children: [
      h,
      i && " *"
    ] }),
    /* @__PURE__ */ t.jsxs(Ut, { hasError: !!a, hasSuccess: !!_, disabled: !!n, size: M, hasStartIcon: !!I, hasEndIcon: !!(v || J), isFocused: !!A, children: [
      I && /* @__PURE__ */ t.jsx(vr, { children: I }),
      /* @__PURE__ */ t.jsx(
        Ht,
        {
          ref: K,
          type: p,
          value: e,
          onChange: (Z) => r(Z.target.value),
          placeholder: s,
          disabled: !!(n || w),
          name: l,
          id: f,
          required: !!i,
          autoComplete: c,
          hasStartIcon: !!I,
          hasEndIcon: !!(v || J),
          size: M,
          maxLength: L,
          onFocus: j,
          onBlur: W,
          ...$
        }
      ),
      J && /* @__PURE__ */ t.jsx(Vt, { type: "button", onClick: U, tabIndex: -1, children: "✕" }),
      v && /* @__PURE__ */ t.jsx(vr, { children: v })
    ] }),
    (a || S || ae) && /* @__PURE__ */ t.jsxs(Gt, { hasError: !!a, hasSuccess: !!_, children: [
      /* @__PURE__ */ t.jsx("div", { children: a || S }),
      ae && /* @__PURE__ */ t.jsxs("div", { children: [
        X,
        L !== void 0 && `/${L}`
      ] })
    ] })
  ] });
}, wr = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, Qt = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, Jt = /* @__PURE__ */ Le(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Kt = /* @__PURE__ */ u.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: r,
  customWidth: s
}) => e === "custom" ? wr.custom({
  customHeight: r,
  customWidth: s
}) : wr[e], ({
  variant: e
}) => Qt[e]), Xt = /* @__PURE__ */ u.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, Jt, ({
  theme: e
}) => e.spacing.sm), Zt = /* @__PURE__ */ u.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), eo = ({
  variant: e = "default",
  size: r = "medium",
  height: s = "200px",
  width: n,
  text: a = "Loading...",
  showSpinner: p = !0,
  className: l
}) => /* @__PURE__ */ t.jsxs(Kt, { variant: e, size: r, customHeight: s, customWidth: n, className: l, children: [
  p && /* @__PURE__ */ t.jsx(Xt, {}),
  a && /* @__PURE__ */ t.jsx(Zt, { children: a })
] }), ro = /* @__PURE__ */ u.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), to = /* @__PURE__ */ u.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), oo = /* @__PURE__ */ u.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: s,
  isFocused: n
}) => r ? e.colors.error : s ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: s,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], s ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), no = /* @__PURE__ */ u.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), so = /* @__PURE__ */ u.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), ao = /* @__PURE__ */ u.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: s
}) => r ? e.colors.error : s ? e.colors.success : e.colors.textSecondary), io = /* @__PURE__ */ u.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), we = ({
  options: e,
  value: r,
  onChange: s,
  disabled: n = !1,
  error: a,
  name: p,
  id: l,
  className: f,
  required: d = !1,
  placeholder: i,
  label: c,
  helperText: h,
  size: S = "medium",
  fullWidth: I = !0,
  loading: v = !1,
  success: w = !1,
  startIcon: _,
  ...b
}) => {
  const [C, L] = se(!1), q = (k) => {
    L(!0), b.onFocus && b.onFocus(k);
  }, M = (k) => {
    L(!1), b.onBlur && b.onBlur(k);
  }, Y = {}, $ = [];
  e.forEach((k) => {
    k.group ? (Y[k.group] || (Y[k.group] = []), Y[k.group].push(k)) : $.push(k);
  });
  const A = Object.keys(Y).length > 0;
  return /* @__PURE__ */ t.jsxs(ro, { className: f, fullWidth: I, children: [
    c && /* @__PURE__ */ t.jsxs(to, { htmlFor: l, children: [
      c,
      d && " *"
    ] }),
    /* @__PURE__ */ t.jsxs(oo, { hasError: !!a, hasSuccess: !!w, disabled: !!(n || v), size: S, hasStartIcon: !!_, isFocused: !!C, children: [
      _ && /* @__PURE__ */ t.jsx(no, { children: _ }),
      /* @__PURE__ */ t.jsxs(
        so,
        {
          value: r,
          onChange: (k) => s(k.target.value),
          disabled: !!(n || v),
          name: p,
          id: l,
          required: !!d,
          hasStartIcon: !!_,
          size: S,
          onFocus: q,
          onBlur: M,
          ...b,
          children: [
            i && /* @__PURE__ */ t.jsx("option", { value: "", disabled: !0, children: i }),
            A ? /* @__PURE__ */ t.jsxs(t.Fragment, { children: [
              $.map((k) => /* @__PURE__ */ t.jsx("option", { value: k.value, disabled: k.disabled, children: k.label }, k.value)),
              Object.entries(Y).map(([k, K]) => /* @__PURE__ */ t.jsx(io, { label: k, children: K.map((U) => /* @__PURE__ */ t.jsx("option", { value: U.value, disabled: U.disabled, children: U.label }, U.value)) }, k))
            ] }) : (
              // Render all options without groups
              e.map((k) => /* @__PURE__ */ t.jsx("option", { value: k.value, disabled: k.disabled, children: k.label }, k.value))
            )
          ]
        }
      )
    ] }),
    (a || h) && /* @__PURE__ */ t.jsx(ao, { hasError: !!a, hasSuccess: !!w, children: /* @__PURE__ */ t.jsx("div", { children: a || h }) })
  ] });
}, Sr = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, co = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, lo = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), uo = /* @__PURE__ */ u.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), po = /* @__PURE__ */ u.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => Sr[e], ({
  size: e
}) => Sr[e], ({
  status: e,
  theme: r,
  pulse: s
}) => {
  let n, a;
  switch (e) {
    case "success":
      n = r.colors.success, a = "76, 175, 80";
      break;
    case "error":
      n = r.colors.error, a = "244, 67, 54";
      break;
    case "warning":
      n = r.colors.warning, a = "255, 152, 0";
      break;
    case "info":
      n = r.colors.info, a = "33, 150, 243";
      break;
    default:
      n = r.colors.textSecondary, a = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], n, s && g(["--pulse-color:", ";", ""], a, lo));
}), fo = /* @__PURE__ */ u.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => co[e], ({
  status: e,
  theme: r
}) => {
  let s;
  switch (e) {
    case "success":
      s = r.colors.success;
      break;
    case "error":
      s = r.colors.error;
      break;
    case "warning":
      s = r.colors.warning;
      break;
    case "info":
      s = r.colors.info;
      break;
    default:
      s = r.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], s, r.fontWeights.medium);
}), ls = ({
  status: e,
  size: r = "medium",
  pulse: s = !1,
  showLabel: n = !1,
  label: a,
  className: p
}) => {
  const l = a || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ t.jsxs(uo, { className: p, children: [
    /* @__PURE__ */ t.jsx(po, { status: e, size: r, pulse: s }),
    n && /* @__PURE__ */ t.jsx(fo, { status: e, size: r, children: l })
  ] });
}, go = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, mo = (e) => g(["", ""], ({
  theme: r
}) => {
  let s, n, a;
  switch (e) {
    case "primary":
      s = `${r.colors.primary}10`, n = r.colors.primary, a = `${r.colors.primary}30`;
      break;
    case "secondary":
      s = `${r.colors.secondary}10`, n = r.colors.secondary, a = `${r.colors.secondary}30`;
      break;
    case "success":
      s = `${r.colors.success}10`, n = r.colors.success, a = `${r.colors.success}30`;
      break;
    case "warning":
      s = `${r.colors.warning}10`, n = r.colors.warning, a = `${r.colors.warning}30`;
      break;
    case "error":
      s = `${r.colors.error}10`, n = r.colors.error, a = `${r.colors.error}30`;
      break;
    case "info":
      s = `${r.colors.info}10`, n = r.colors.info, a = `${r.colors.info}30`;
      break;
    default:
      s = `${r.colors.textSecondary}10`, n = r.colors.textSecondary, a = `${r.colors.textSecondary}30`;
  }
  return `
        background-color: ${s};
        color: ${n};
        border: 1px solid ${a};
      `;
}), ho = /* @__PURE__ */ u.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => go[e], ({
  variant: e
}) => mo(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), xo = /* @__PURE__ */ u.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: r
}) => {
  const s = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${s[e]};
      height: ${s[e]};
      font-size: ${r.fontSizes.xs};
    `;
}), ds = ({
  children: e,
  variant: r = "default",
  size: s = "medium",
  removable: n = !1,
  onRemove: a,
  className: p,
  onClick: l
}) => {
  const f = (d) => {
    d.stopPropagation(), a == null || a();
  };
  return /* @__PURE__ */ t.jsxs(ho, { variant: r, size: s, clickable: !!l, className: p, onClick: l, children: [
    e,
    n && /* @__PURE__ */ t.jsx(xo, { size: s, onClick: f, children: "×" })
  ] });
}, bo = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, yo = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, vo = /* @__PURE__ */ u.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";"], r.colors.border), ({
  padding: e
}) => bo[e], ({
  variant: e
}) => yo[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: r
}) => r.shadows.medium)), wo = /* @__PURE__ */ u.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), So = /* @__PURE__ */ u.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), Eo = /* @__PURE__ */ u.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Io = /* @__PURE__ */ u.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), Co = /* @__PURE__ */ u.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), jo = /* @__PURE__ */ u.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), To = /* @__PURE__ */ u.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), _o = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), ko = /* @__PURE__ */ u.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), Ro = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), No = ({
  children: e,
  title: r,
  subtitle: s,
  bordered: n = !0,
  variant: a = "default",
  padding: p = "medium",
  className: l,
  footer: f,
  actions: d,
  isLoading: i = !1,
  hasError: c = !1,
  errorMessage: h = "An error occurred",
  clickable: S = !1,
  onClick: I,
  ...v
}) => {
  const w = r || s || d;
  return /* @__PURE__ */ t.jsxs(vo, { bordered: n, variant: a, padding: p, clickable: S, className: l, onClick: S ? I : void 0, ...v, children: [
    i && /* @__PURE__ */ t.jsx(_o, { children: /* @__PURE__ */ t.jsx(Ro, {}) }),
    w && /* @__PURE__ */ t.jsxs(wo, { children: [
      /* @__PURE__ */ t.jsxs(So, { children: [
        r && /* @__PURE__ */ t.jsx(Eo, { children: r }),
        s && /* @__PURE__ */ t.jsx(Io, { children: s })
      ] }),
      d && /* @__PURE__ */ t.jsx(Co, { children: d })
    ] }),
    c && /* @__PURE__ */ t.jsx(ko, { children: /* @__PURE__ */ t.jsx("p", { children: h }) }),
    /* @__PURE__ */ t.jsx(jo, { children: e }),
    f && /* @__PURE__ */ t.jsx(To, { children: f })
  ] });
}, Lo = /* @__PURE__ */ u.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: r
}) => {
  const s = {
    small: r.fontSizes.md,
    medium: r.fontSizes.lg,
    large: r.fontSizes.xl
  };
  return g(["font-size:", ";"], s[e]);
}), Po = /* @__PURE__ */ u.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: r
}) => {
  const s = {
    small: r.fontSizes.sm,
    medium: r.fontSizes.md,
    large: r.fontSizes.lg
  };
  return g(["font-size:", ";"], s[e]);
}), Do = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, Mo = /* @__PURE__ */ u.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => Do[e], ({
  size: e,
  theme: r
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], r.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], r.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], r.spacing.lg);
  }
}), Ao = /* @__PURE__ */ u.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: r
}) => {
  const s = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], s[e], s[e], s[e], r.colors.textSecondary);
}), Oo = /* @__PURE__ */ u.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), $o = /* @__PURE__ */ u.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), Er = ({
  title: e,
  description: r,
  icon: s,
  actionText: n,
  onAction: a,
  variant: p = "default",
  size: l = "medium",
  className: f,
  children: d
}) => /* @__PURE__ */ t.jsxs(Mo, { variant: p, size: l, className: f, children: [
  s && /* @__PURE__ */ t.jsx(Ao, { size: l, children: s }),
  e && /* @__PURE__ */ t.jsx(Lo, { size: l, children: e }),
  r && /* @__PURE__ */ t.jsx(Po, { size: l, children: r }),
  n && a && /* @__PURE__ */ t.jsx(Oo, { children: /* @__PURE__ */ t.jsx(oe, { variant: "primary", size: l === "small" ? "small" : "medium", onClick: a, children: n }) }),
  d && /* @__PURE__ */ t.jsx($o, { children: d })
] }), Ir = /* @__PURE__ */ u.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), Fo = /* @__PURE__ */ u.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), Cr = /* @__PURE__ */ u.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Ne = /* @__PURE__ */ u.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), jr = /* @__PURE__ */ u.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), Tr = /* @__PURE__ */ u.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), Bo = /* @__PURE__ */ u.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), Br = /* @__PURE__ */ u.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), zo = /* @__PURE__ */ u.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), qo = /* @__PURE__ */ u(Br).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), Yo = ({
  error: e,
  resetError: r,
  isAppLevel: s,
  name: n,
  onSkip: a
}) => {
  const p = () => {
    window.location.reload();
  };
  return s ? /* @__PURE__ */ t.jsx(Ir, { isAppLevel: !0, children: /* @__PURE__ */ t.jsxs(Fo, { children: [
    /* @__PURE__ */ t.jsx(Cr, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ t.jsx(Ne, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ t.jsxs(jr, { children: [
      /* @__PURE__ */ t.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ t.jsx(Ne, { children: e.message }),
      e.stack && /* @__PURE__ */ t.jsx(Tr, { children: e.stack })
    ] }),
    /* @__PURE__ */ t.jsx(qo, { onClick: p, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ t.jsxs(Ir, { children: [
    /* @__PURE__ */ t.jsx(Cr, { children: n ? `Error in ${n}` : "Something went wrong" }),
    /* @__PURE__ */ t.jsx(Ne, { children: n ? `We encountered a problem while loading ${n}. You can try again${a ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ t.jsxs(jr, { children: [
      /* @__PURE__ */ t.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ t.jsx(Ne, { children: e.message }),
      e.stack && /* @__PURE__ */ t.jsx(Tr, { children: e.stack })
    ] }),
    /* @__PURE__ */ t.jsxs(Bo, { children: [
      /* @__PURE__ */ t.jsx(Br, { onClick: r, children: "Try Again" }),
      a && /* @__PURE__ */ t.jsx(zo, { onClick: a, children: "Skip This Feature" })
    ] })
  ] });
};
class Wo extends xt {
  constructor(r) {
    super(r), this.resetError = () => {
      this.setState({
        hasError: !1,
        error: null
      });
    }, this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(r) {
    return {
      hasError: !0,
      error: r
    };
  }
  componentDidCatch(r, s) {
    const {
      name: n
    } = this.props, a = n ? `ErrorBoundary(${n})` : "ErrorBoundary";
    console.error(`Error caught by ${a}:`, r, s), this.props.onError && this.props.onError(r, s);
  }
  componentDidUpdate(r) {
    this.state.hasError && this.props.resetOnPropsChange && r.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: r,
      error: s
    } = this.state, {
      children: n,
      fallback: a,
      name: p,
      isFeatureBoundary: l,
      onSkip: f
    } = this.props;
    return r && s ? typeof a == "function" ? a({
      error: s,
      resetError: this.resetError
    }) : a || /* @__PURE__ */ t.jsx(Yo, { error: s, resetError: this.resetError, isAppLevel: !l, name: p, onSkip: f }) : n;
  }
}
const zr = ({
  isAppLevel: e,
  isFeatureBoundary: r,
  ...s
}) => {
  const n = e ? "app" : r ? "feature" : "component", a = {
    resetOnPropsChange: n !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: n !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: n === "feature"
  };
  return /* @__PURE__ */ t.jsx(Wo, { ...a, ...s });
}, us = (e) => /* @__PURE__ */ t.jsx(zr, { isAppLevel: !0, ...e }), ps = ({
  featureName: e,
  ...r
}) => /* @__PURE__ */ t.jsx(zr, { isFeatureBoundary: !0, name: e, ...r }), Uo = /* @__PURE__ */ u.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), Ho = /* @__PURE__ */ u.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), Vo = /* @__PURE__ */ u.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), fs = ({
  children: e,
  label: r,
  helperText: s,
  required: n = !1,
  error: a,
  className: p,
  id: l,
  ...f
}) => {
  const d = l || `field-${Math.random().toString(36).substr(2, 9)}`, i = Ie.Children.map(e, (c) => Ie.isValidElement(c) ? Ie.cloneElement(c, {
    id: d,
    required: n,
    error: a,
    ...c.props
  }) : c);
  return /* @__PURE__ */ t.jsxs(Uo, { className: p, ...f, children: [
    /* @__PURE__ */ t.jsxs(Ho, { htmlFor: d, hasError: !!a, children: [
      r,
      n && /* @__PURE__ */ t.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    i,
    (s || a) && /* @__PURE__ */ t.jsx(Vo, { hasError: !!a, children: a || s })
  ] });
}, Go = /* @__PURE__ */ Le(["from{opacity:0;}to{opacity:1;}"]), Qo = /* @__PURE__ */ Le(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), Jo = /* @__PURE__ */ u.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, Go), Ko = /* @__PURE__ */ u.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, Qo, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), Xo = /* @__PURE__ */ u.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Zo = /* @__PURE__ */ u.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), en = /* @__PURE__ */ u.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), rn = /* @__PURE__ */ u.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), tn = /* @__PURE__ */ u.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), gs = ({
  isOpen: e,
  title: r,
  children: s,
  onClose: n,
  size: a = "medium",
  closeOnOutsideClick: p = !0,
  showCloseButton: l = !0,
  footer: f,
  hasFooter: d = !0,
  primaryActionText: i,
  onPrimaryAction: c,
  primaryActionDisabled: h = !1,
  primaryActionLoading: S = !1,
  secondaryActionText: I,
  onSecondaryAction: v,
  secondaryActionDisabled: w = !1,
  className: _,
  zIndex: b = 1e3,
  centered: C = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: L = !0
}) => {
  const q = Mr(null);
  xe(() => {
    const A = (k) => {
      k.key === "Escape" && e && p && n();
    };
    return document.addEventListener("keydown", A), () => {
      document.removeEventListener("keydown", A);
    };
  }, [e, n, p]);
  const M = (A) => {
    q.current && !q.current.contains(A.target) && p && n();
  };
  xe(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const Y = /* @__PURE__ */ t.jsxs(t.Fragment, { children: [
    I && /* @__PURE__ */ t.jsx(oe, { variant: "outline", onClick: v, disabled: w, children: I }),
    i && /* @__PURE__ */ t.jsx(oe, { onClick: c, disabled: h, loading: S, children: i })
  ] });
  return e ? wt(/* @__PURE__ */ t.jsx(Jo, { onClick: M, zIndex: b, children: /* @__PURE__ */ t.jsxs(Ko, { ref: q, size: a, className: _, centered: C, scrollable: L, onClick: (A) => A.stopPropagation(), children: [
    (r || l) && /* @__PURE__ */ t.jsxs(Xo, { children: [
      r && /* @__PURE__ */ t.jsx(Zo, { children: r }),
      l && /* @__PURE__ */ t.jsx(en, { onClick: n, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ t.jsx(rn, { scrollable: L, children: s }),
    d && (f || i || I) && /* @__PURE__ */ t.jsx(tn, { children: f || Y })
  ] }) }), document.body) : null;
}, on = /* @__PURE__ */ u.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), nn = /* @__PURE__ */ u.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";border-radius:", ";"], r.colors.border, r.borderRadius.sm), ({
  compact: e,
  theme: r
}) => e ? g(["th,td{padding:", " ", ";}"], r.spacing.xs, r.spacing.sm) : g(["th,td{padding:", " ", ";}"], r.spacing.sm, r.spacing.md)), sn = /* @__PURE__ */ u.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), an = /* @__PURE__ */ u.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), cn = /* @__PURE__ */ u.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => r.colors.background), ({
  isSorted: e,
  theme: r
}) => e && g(["color:", ";"], r.colors.primary)), ln = /* @__PURE__ */ u.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), dn = /* @__PURE__ */ u.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), un = /* @__PURE__ */ u.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: s
}) => e && !s && g(["&:nth-child(even){background-color:", "50;}"], r.colors.background), ({
  hoverable: e,
  theme: r,
  isSelected: s
}) => e && !s && g(["&:hover{background-color:", "aa;}"], r.colors.background), ({
  isSelected: e,
  theme: r
}) => e && g(["background-color:", "15;"], r.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), pn = /* @__PURE__ */ u.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), fn = /* @__PURE__ */ u.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), gn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), mn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), hn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), xn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), bn = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), yn = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function ms({
  columns: e,
  data: r,
  isLoading: s = !1,
  bordered: n = !0,
  striped: a = !0,
  hoverable: p = !0,
  compact: l = !1,
  stickyHeader: f = !1,
  height: d,
  onRowClick: i,
  isRowSelected: c,
  onSort: h,
  sortColumn: S,
  sortDirection: I,
  pagination: v = !1,
  currentPage: w = 1,
  pageSize: _ = 10,
  totalRows: b = 0,
  onPageChange: C,
  onPageSizeChange: L,
  className: q,
  emptyMessage: M = "No data available",
  scrollable: Y = !0
}) {
  const $ = te(() => e.filter((j) => !j.hidden), [e]), A = te(() => Math.ceil(b / _), [b, _]), k = te(() => {
    if (!v)
      return r;
    const j = (w - 1) * _, W = j + _;
    return b > 0 && r.length <= _ ? r : r.slice(j, W);
  }, [r, v, w, _, b]), K = (j) => {
    if (!h)
      return;
    h(j, S === j && I === "asc" ? "desc" : "asc");
  }, U = (j) => {
    j < 1 || j > A || !C || C(j);
  };
  return /* @__PURE__ */ t.jsxs("div", { style: {
    position: "relative"
  }, children: [
    s && /* @__PURE__ */ t.jsx(bn, { children: /* @__PURE__ */ t.jsx(yn, {}) }),
    /* @__PURE__ */ t.jsx(on, { height: d, scrollable: Y, children: /* @__PURE__ */ t.jsxs(nn, { bordered: n, striped: a, compact: l, className: q, children: [
      /* @__PURE__ */ t.jsx(sn, { stickyHeader: f, children: /* @__PURE__ */ t.jsx(an, { children: $.map((j) => /* @__PURE__ */ t.jsxs(cn, { sortable: j.sortable, isSorted: S === j.id, align: j.align, width: j.width, onClick: () => j.sortable && K(j.id), children: [
        j.header,
        j.sortable && /* @__PURE__ */ t.jsx(ln, { direction: S === j.id ? I : void 0 })
      ] }, j.id)) }) }),
      /* @__PURE__ */ t.jsx(dn, { children: k.length > 0 ? k.map((j, W) => /* @__PURE__ */ t.jsx(un, { hoverable: p, striped: a, isSelected: c ? c(j, W) : !1, isClickable: !!i, onClick: () => i && i(j, W), children: $.map((J) => /* @__PURE__ */ t.jsx(pn, { align: J.align, children: J.cell(j, W) }, J.id)) }, W)) : /* @__PURE__ */ t.jsx("tr", { children: /* @__PURE__ */ t.jsx("td", { colSpan: $.length, children: /* @__PURE__ */ t.jsx(fn, { children: M }) }) }) })
    ] }) }),
    v && A > 0 && /* @__PURE__ */ t.jsxs(gn, { children: [
      /* @__PURE__ */ t.jsxs(mn, { children: [
        "Showing ",
        Math.min((w - 1) * _ + 1, b),
        " to",
        " ",
        Math.min(w * _, b),
        " of ",
        b,
        " entries"
      ] }),
      /* @__PURE__ */ t.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        L && /* @__PURE__ */ t.jsxs(xn, { children: [
          /* @__PURE__ */ t.jsx("span", { children: "Show" }),
          /* @__PURE__ */ t.jsx("select", { value: _, onChange: (j) => L(Number(j.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((j) => /* @__PURE__ */ t.jsx("option", { value: j, children: j }, j)) }),
          /* @__PURE__ */ t.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ t.jsxs(hn, { children: [
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => U(1), disabled: w === 1, children: "First" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => U(w - 1), disabled: w === 1, children: "Prev" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => U(w + 1), disabled: w === A, children: "Next" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => U(A), disabled: w === A, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const y = {
  DATE: "date",
  SYMBOL: "symbol",
  DIRECTION: "direction",
  MODEL_TYPE: "model_type",
  SESSION: "session",
  ENTRY_PRICE: "entry_price",
  EXIT_PRICE: "exit_price",
  R_MULTIPLE: "r_multiple",
  ACHIEVED_PL: "achieved_pl",
  WIN_LOSS: "win_loss",
  PATTERN_QUALITY: "pattern_quality_rating",
  ENTRY_TIME: "entry_time",
  EXIT_TIME: "exit_time"
}, Ye = /* @__PURE__ */ u.span.withConfig({
  displayName: "ProfitLossCell",
  componentId: "sc-14bks31-0"
})(["color:", ";font-weight:", ";"], ({
  isProfit: e,
  theme: r
}) => e ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), qr = /* @__PURE__ */ u(Pe).withConfig({
  displayName: "DirectionBadge",
  componentId: "sc-14bks31-1"
})(["background-color:", ";color:white;"], ({
  direction: e,
  theme: r
}) => e === "Long" ? r.colors.success || "#10b981" : r.colors.error || "#ef4444"), Yr = /* @__PURE__ */ u.span.withConfig({
  displayName: "QualityRating",
  componentId: "sc-14bks31-2"
})(["color:", ";font-weight:", ";"], ({
  rating: e,
  theme: r
}) => e >= 4 ? r.colors.success || "#10b981" : e >= 3 ? r.colors.warning || "#f59e0b" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), We = /* @__PURE__ */ u.span.withConfig({
  displayName: "RMultipleCell",
  componentId: "sc-14bks31-3"
})(["color:", ";font-weight:", ";"], ({
  rMultiple: e,
  theme: r
}) => e > 0 ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), Ce = (e) => e == null ? "-" : new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2
}).format(e), Ue = (e) => {
  try {
    return new Date(e).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch {
    return e;
  }
}, _r = (e) => e || "-", vn = () => [{
  id: y.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => Ue(e.trade[y.DATE])
}, {
  id: y.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "80px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: y.DIRECTION,
  header: "Direction",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(qr, { direction: e.trade[y.DIRECTION], size: "small", children: e.trade[y.DIRECTION] })
}, {
  id: y.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[y.MODEL_TYPE] || "-"
}, {
  id: y.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[y.SESSION] || "-"
}, {
  id: y.ENTRY_PRICE,
  header: "Entry",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ce(e.trade[y.ENTRY_PRICE])
}, {
  id: y.EXIT_PRICE,
  header: "Exit",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ce(e.trade[y.EXIT_PRICE])
}, {
  id: y.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(We, { rMultiple: e.trade[y.R_MULTIPLE] || 0, children: e.trade[y.R_MULTIPLE] ? `${e.trade[y.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: y.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(Ye, { isProfit: (e.trade[y.ACHIEVED_PL] || 0) > 0, children: Ce(e.trade[y.ACHIEVED_PL]) })
}, {
  id: y.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(Pe, { variant: e.trade[y.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[y.WIN_LOSS] || "-" })
}, {
  id: y.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(Yr, { rating: e.trade[y.PATTERN_QUALITY] || 0, children: e.trade[y.PATTERN_QUALITY] ? `${e.trade[y.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: y.ENTRY_TIME,
  header: "Entry Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => _r(e.trade[y.ENTRY_TIME])
}, {
  id: y.EXIT_TIME,
  header: "Exit Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => _r(e.trade[y.EXIT_TIME])
}], wn = () => [{
  id: y.DATE,
  header: "Date",
  sortable: !0,
  width: "90px",
  cell: (e) => Ue(e.trade[y.DATE])
}, {
  id: y.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "60px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: y.DIRECTION,
  header: "Dir",
  sortable: !0,
  width: "50px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(qr, { direction: e.trade[y.DIRECTION], size: "small", children: e.trade[y.DIRECTION].charAt(0) })
}, {
  id: y.R_MULTIPLE,
  header: "R",
  sortable: !0,
  width: "60px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(We, { rMultiple: e.trade[y.R_MULTIPLE] || 0, children: e.trade[y.R_MULTIPLE] ? `${e.trade[y.R_MULTIPLE].toFixed(1)}R` : "-" })
}, {
  id: y.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "80px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(Ye, { isProfit: (e.trade[y.ACHIEVED_PL] || 0) > 0, children: Ce(e.trade[y.ACHIEVED_PL]) })
}, {
  id: y.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "60px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(Pe, { variant: e.trade[y.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[y.WIN_LOSS] === "Win" ? "W" : e.trade[y.WIN_LOSS] === "Loss" ? "L" : "-" })
}], Sn = () => [{
  id: y.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => Ue(e.trade[y.DATE])
}, {
  id: y.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[y.MODEL_TYPE] || "-"
}, {
  id: y.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[y.SESSION] || "-"
}, {
  id: y.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(We, { rMultiple: e.trade[y.R_MULTIPLE] || 0, children: e.trade[y.R_MULTIPLE] ? `${e.trade[y.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: y.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ t.jsx(Ye, { isProfit: (e.trade[y.ACHIEVED_PL] || 0) > 0, children: Ce(e.trade[y.ACHIEVED_PL]) })
}, {
  id: y.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(Yr, { rating: e.trade[y.PATTERN_QUALITY] || 0, children: e.trade[y.PATTERN_QUALITY] ? `${e.trade[y.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: y.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ t.jsx(Pe, { variant: e.trade[y.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[y.WIN_LOSS] || "-" })
}], En = /* @__PURE__ */ u.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-uyrnn-0"
})(["", " ", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: s
}) => {
  var n;
  return e && !s && g(["&:nth-child(even){background-color:", "50;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  hoverable: e,
  theme: r,
  isSelected: s
}) => {
  var n;
  return e && !s && g(["&:hover{background-color:", "aa;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  isSelected: e,
  theme: r
}) => {
  var s;
  return e && g(["background-color:", "15;"], ((s = r.colors) == null ? void 0 : s.primary) || "#3b82f6");
}, ({
  isClickable: e
}) => e && g(["cursor:pointer;"]), ({
  isExpanded: e,
  theme: r
}) => {
  var s;
  return e && g(["border-bottom:2px solid ", ";"], ((s = r.colors) == null ? void 0 : s.primary) || "#3b82f6");
}), kr = /* @__PURE__ */ u.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-uyrnn-1"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";padding:", " ", ";vertical-align:middle;"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), In = /* @__PURE__ */ u.tr.withConfig({
  displayName: "ExpandedRow",
  componentId: "sc-uyrnn-2"
})(["display:", ";"], ({
  isVisible: e
}) => e ? "table-row" : "none"), Cn = /* @__PURE__ */ u.td.withConfig({
  displayName: "ExpandedCell",
  componentId: "sc-uyrnn-3"
})(["padding:0;border-bottom:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), jn = /* @__PURE__ */ u.div.withConfig({
  displayName: "ExpandedContent",
  componentId: "sc-uyrnn-4"
})(["padding:", ";background-color:", "30;border-left:3px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Tn = /* @__PURE__ */ u.button.withConfig({
  displayName: "ExpandButton",
  componentId: "sc-uyrnn-5"
})(["background:none;border:none;cursor:pointer;padding:", ";color:", ";font-size:", ";display:flex;align-items:center;justify-content:center;border-radius:", ";transition:all 0.2s ease;&:hover{background-color:", ";color:", ";}&:focus{outline:2px solid ", ";outline-offset:2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), _n = /* @__PURE__ */ u.span.withConfig({
  displayName: "ExpandIcon",
  componentId: "sc-uyrnn-6"
})(["display:inline-block;transition:transform 0.2s ease;transform:", ";&::after{content:'▶';}"], ({
  isExpanded: e
}) => e ? "rotate(90deg)" : "rotate(0deg)"), kn = /* @__PURE__ */ u.div.withConfig({
  displayName: "TradeDetails",
  componentId: "sc-uyrnn-7"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), Se = /* @__PURE__ */ u.div.withConfig({
  displayName: "DetailGroup",
  componentId: "sc-uyrnn-8"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Ee = /* @__PURE__ */ u.span.withConfig({
  displayName: "DetailLabel",
  componentId: "sc-uyrnn-9"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), re = /* @__PURE__ */ u.span.withConfig({
  displayName: "DetailValue",
  componentId: "sc-uyrnn-10"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}), Rn = ({
  trade: e
}) => /* @__PURE__ */ t.jsxs(kn, { children: [
  e.fvg_details && /* @__PURE__ */ t.jsxs(Se, { children: [
    /* @__PURE__ */ t.jsx(Ee, { children: "FVG Details" }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Type: ",
      e.fvg_details.rd_type || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Entry Version: ",
      e.fvg_details.entry_version || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Draw on Liquidity: ",
      e.fvg_details.draw_on_liquidity || "-"
    ] })
  ] }),
  e.setup && /* @__PURE__ */ t.jsxs(Se, { children: [
    /* @__PURE__ */ t.jsx(Ee, { children: "Setup Classification" }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Primary: ",
      e.setup.primary_setup || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Secondary: ",
      e.setup.secondary_setup || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Liquidity: ",
      e.setup.liquidity_taken || "-"
    ] })
  ] }),
  e.analysis && /* @__PURE__ */ t.jsxs(Se, { children: [
    /* @__PURE__ */ t.jsx(Ee, { children: "Analysis" }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "DOL Target: ",
      e.analysis.dol_target_type || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Path Quality: ",
      e.analysis.path_quality || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Clustering: ",
      e.analysis.clustering || "-"
    ] })
  ] }),
  /* @__PURE__ */ t.jsxs(Se, { children: [
    /* @__PURE__ */ t.jsx(Ee, { children: "Timing" }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Entry: ",
      e.trade.entry_time || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "Exit: ",
      e.trade.exit_time || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "FVG: ",
      e.trade.fvg_time || "-"
    ] }),
    /* @__PURE__ */ t.jsxs(re, { children: [
      "RD: ",
      e.trade.rd_time || "-"
    ] })
  ] }),
  e.trade.notes && /* @__PURE__ */ t.jsxs(Se, { style: {
    gridColumn: "1 / -1"
  }, children: [
    /* @__PURE__ */ t.jsx(Ee, { children: "Notes" }),
    /* @__PURE__ */ t.jsx(re, { children: e.trade.notes })
  ] })
] }), Nn = ({
  trade: e,
  index: r,
  columns: s,
  isSelected: n = !1,
  hoverable: a = !0,
  striped: p = !0,
  expandable: l = !1,
  isExpanded: f = !1,
  onRowClick: d,
  onToggleExpand: i,
  expandedContent: c
}) => {
  const [h, S] = se(!1), I = f !== void 0 ? f : h, v = (b) => {
    b.target.closest("button") || d == null || d(e, r);
  }, w = (b) => {
    b.stopPropagation(), i ? i(e, r) : S(!h);
  }, _ = s.filter((b) => !b.hidden);
  return /* @__PURE__ */ t.jsxs(t.Fragment, { children: [
    /* @__PURE__ */ t.jsxs(En, { hoverable: a, striped: p, isSelected: n, isClickable: !!d, isExpanded: I, onClick: v, children: [
      l && /* @__PURE__ */ t.jsx(kr, { align: "center", style: {
        width: "40px",
        padding: "8px"
      }, children: /* @__PURE__ */ t.jsx(Tn, { onClick: w, children: /* @__PURE__ */ t.jsx(_n, { isExpanded: I }) }) }),
      _.map((b) => /* @__PURE__ */ t.jsx(kr, { align: b.align, children: b.cell(e, r) }, b.id))
    ] }),
    l && /* @__PURE__ */ t.jsx(In, { isVisible: I, children: /* @__PURE__ */ t.jsx(Cn, { colSpan: _.length + 1, children: /* @__PURE__ */ t.jsx(jn, { children: c || /* @__PURE__ */ t.jsx(Rn, { trade: e }) }) }) })
  ] });
}, ie = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  DATE_FROM: "dateFrom",
  DATE_TO: "dateTo",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  MIN_R_MULTIPLE: "min_r_multiple",
  MAX_R_MULTIPLE: "max_r_multiple",
  MIN_PATTERN_QUALITY: "min_pattern_quality",
  MAX_PATTERN_QUALITY: "max_pattern_quality"
}, Ln = /* @__PURE__ */ u.div.withConfig({
  displayName: "FiltersContainer",
  componentId: "sc-32k3gq-0"
})(["display:flex;flex-direction:column;gap:", ";padding:", ";background-color:", ";border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Rr = /* @__PURE__ */ u.div.withConfig({
  displayName: "FilterRow",
  componentId: "sc-32k3gq-1"
})(["display:flex;gap:", ";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), de = /* @__PURE__ */ u.div.withConfig({
  displayName: "FilterGroup",
  componentId: "sc-32k3gq-2"
})(["display:flex;flex-direction:column;gap:", ";min-width:120px;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), ue = /* @__PURE__ */ u.label.withConfig({
  displayName: "FilterLabel",
  componentId: "sc-32k3gq-3"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Pn = /* @__PURE__ */ u.div.withConfig({
  displayName: "FilterActions",
  componentId: "sc-32k3gq-4"
})(["display:flex;gap:", ";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), Dn = /* @__PURE__ */ u.div.withConfig({
  displayName: "AdvancedFilters",
  componentId: "sc-32k3gq-5"
})(["display:", ";flex-direction:column;gap:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  isVisible: e
}) => e ? "flex" : "none", ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Nr = /* @__PURE__ */ u.div.withConfig({
  displayName: "RangeInputGroup",
  componentId: "sc-32k3gq-6"
})(["display:flex;gap:", ";align-items:center;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Lr = /* @__PURE__ */ u.span.withConfig({
  displayName: "RangeLabel",
  componentId: "sc-32k3gq-7"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Mn = ({
  filters: e,
  onFiltersChange: r,
  onReset: s,
  isLoading: n = !1,
  showAdvanced: a = !1,
  onToggleAdvanced: p
}) => {
  const l = (i, c) => {
    r({
      ...e,
      [i]: c
    });
  }, f = () => {
    r({}), s == null || s();
  }, d = Object.values(e).some((i) => i !== void 0 && i !== "" && i !== null);
  return /* @__PURE__ */ t.jsxs(Ln, { children: [
    /* @__PURE__ */ t.jsxs(Rr, { children: [
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Date From" }),
        /* @__PURE__ */ t.jsx(he, { type: "date", value: e.dateFrom || "", onChange: (i) => l(ie.DATE_FROM, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Date To" }),
        /* @__PURE__ */ t.jsx(he, { type: "date", value: e.dateTo || "", onChange: (i) => l(ie.DATE_TO, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Model Type" }),
        /* @__PURE__ */ t.jsx(we, { options: [{
          value: "",
          label: "All Models"
        }, {
          value: "RD-Cont",
          label: "RD-Cont"
        }, {
          value: "FVG-RD",
          label: "FVG-RD"
        }, {
          value: "True-RD",
          label: "True-RD"
        }, {
          value: "IMM-RD",
          label: "IMM-RD"
        }, {
          value: "Dispersed-RD",
          label: "Dispersed-RD"
        }, {
          value: "Wide-Gap-RD",
          label: "Wide-Gap-RD"
        }], value: e.model_type || "", onChange: (i) => l(ie.MODEL_TYPE, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Session" }),
        /* @__PURE__ */ t.jsx(we, { options: [{
          value: "",
          label: "All Sessions"
        }, {
          value: "Pre-Market",
          label: "Pre-Market"
        }, {
          value: "NY Open",
          label: "NY Open"
        }, {
          value: "10:50-11:10",
          label: "10:50-11:10"
        }, {
          value: "11:50-12:10",
          label: "11:50-12:10"
        }, {
          value: "Lunch Macro",
          label: "Lunch Macro"
        }, {
          value: "13:50-14:10",
          label: "13:50-14:10"
        }, {
          value: "14:50-15:10",
          label: "14:50-15:10"
        }, {
          value: "15:15-15:45",
          label: "15:15-15:45"
        }, {
          value: "MOC",
          label: "MOC"
        }, {
          value: "Post MOC",
          label: "Post MOC"
        }], value: e.session || "", onChange: (i) => l(ie.SESSION, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Direction" }),
        /* @__PURE__ */ t.jsx(we, { options: [{
          value: "",
          label: "All Directions"
        }, {
          value: "Long",
          label: "Long"
        }, {
          value: "Short",
          label: "Short"
        }], value: e.direction || "", onChange: (i) => l(ie.DIRECTION, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Result" }),
        /* @__PURE__ */ t.jsx(we, { options: [{
          value: "",
          label: "All Results"
        }, {
          value: "Win",
          label: "Win"
        }, {
          value: "Loss",
          label: "Loss"
        }], value: e.win_loss || "", onChange: (i) => l(ie.WIN_LOSS, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(Pn, { children: [
        p && /* @__PURE__ */ t.jsxs(oe, { variant: "outline", size: "small", onClick: p, disabled: n, children: [
          a ? "Hide" : "Show",
          " Advanced"
        ] }),
        /* @__PURE__ */ t.jsx(oe, { variant: "outline", size: "small", onClick: f, disabled: n || !d, children: "Reset" })
      ] })
    ] }),
    /* @__PURE__ */ t.jsx(Dn, { isVisible: a, children: /* @__PURE__ */ t.jsxs(Rr, { children: [
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Market" }),
        /* @__PURE__ */ t.jsx(we, { options: [{
          value: "",
          label: "All Markets"
        }, {
          value: "MNQ",
          label: "MNQ"
        }, {
          value: "NQ",
          label: "NQ"
        }, {
          value: "ES",
          label: "ES"
        }, {
          value: "MES",
          label: "MES"
        }, {
          value: "YM",
          label: "YM"
        }, {
          value: "MYM",
          label: "MYM"
        }], value: e.market || "", onChange: (i) => l(ie.MARKET, i), disabled: n })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "R Multiple Range" }),
        /* @__PURE__ */ t.jsxs(Nr, { children: [
          /* @__PURE__ */ t.jsx(he, { type: "number", placeholder: "Min", step: "0.1", value: e.min_r_multiple || "", onChange: (i) => l(ie.MIN_R_MULTIPLE, i ? Number(i) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ t.jsx(Lr, { children: "to" }),
          /* @__PURE__ */ t.jsx(he, { type: "number", placeholder: "Max", step: "0.1", value: e.max_r_multiple || "", onChange: (i) => l(ie.MAX_R_MULTIPLE, i ? Number(i) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] }),
      /* @__PURE__ */ t.jsxs(de, { children: [
        /* @__PURE__ */ t.jsx(ue, { children: "Pattern Quality Range" }),
        /* @__PURE__ */ t.jsxs(Nr, { children: [
          /* @__PURE__ */ t.jsx(he, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: e.min_pattern_quality || "", onChange: (i) => l(ie.MIN_PATTERN_QUALITY, i ? Number(i) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ t.jsx(Lr, { children: "to" }),
          /* @__PURE__ */ t.jsx(he, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: e.max_pattern_quality || "", onChange: (i) => l(ie.MAX_PATTERN_QUALITY, i ? Number(i) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] })
    ] }) })
  ] });
}, An = /* @__PURE__ */ u.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-13oxwmo-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), On = /* @__PURE__ */ u.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-13oxwmo-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  bordered: e,
  theme: r
}) => {
  var s, n;
  return e && g(["border:1px solid ", ";border-radius:", ";"], ((s = r.colors) == null ? void 0 : s.border) || "#e5e7eb", ((n = r.borderRadius) == null ? void 0 : n.sm) || "4px");
}, ({
  compact: e,
  theme: r
}) => {
  var s, n, a, p;
  return e ? g(["th,td{padding:", " ", ";}"], ((s = r.spacing) == null ? void 0 : s.xs) || "8px", ((n = r.spacing) == null ? void 0 : n.sm) || "12px") : g(["th,td{padding:", " ", ";}"], ((a = r.spacing) == null ? void 0 : a.sm) || "12px", ((p = r.spacing) == null ? void 0 : p.md) || "16px");
}), $n = /* @__PURE__ */ u.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-13oxwmo-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), Fn = /* @__PURE__ */ u.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-13oxwmo-3"
})(["background-color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}), Pr = /* @__PURE__ */ u.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13oxwmo-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => {
  var s;
  return ((s = r.colors) == null ? void 0 : s.background) || "#f8f9fa";
}), ({
  isSorted: e,
  theme: r
}) => {
  var s;
  return e && g(["color:", ";"], ((s = r.colors) == null ? void 0 : s.primary) || "#3b82f6");
}), Bn = /* @__PURE__ */ u.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13oxwmo-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), zn = /* @__PURE__ */ u.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13oxwmo-6"
})([""]), qn = /* @__PURE__ */ u.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13oxwmo-7"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xl) || "32px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Yn = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-13oxwmo-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return `${((r = e.colors) == null ? void 0 : r.background) || "#ffffff"}80`;
}), Wn = /* @__PURE__ */ u.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-13oxwmo-9"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Un = /* @__PURE__ */ u.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-13oxwmo-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}), Hn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-13oxwmo-11"
})(["color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Vn = /* @__PURE__ */ u.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-13oxwmo-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), hs = ({
  data: e,
  isLoading: r = !1,
  bordered: s = !0,
  striped: n = !0,
  hoverable: a = !0,
  compact: p = !1,
  stickyHeader: l = !1,
  height: f,
  onRowClick: d,
  isRowSelected: i,
  onSort: c,
  sortColumn: h,
  sortDirection: S,
  pagination: I = !1,
  currentPage: v = 1,
  pageSize: w = 10,
  totalRows: _ = 0,
  onPageChange: b,
  onPageSizeChange: C,
  className: L,
  emptyMessage: q = "No trades available",
  scrollable: M = !0,
  showFilters: Y = !1,
  filters: $ = {},
  onFiltersChange: A,
  columnPreset: k = "default",
  customColumns: K,
  expandableRows: U = !1,
  renderExpandedContent: j
}) => {
  const [W, J] = se(!1), X = te(() => {
    if (K)
      return K;
    switch (k) {
      case "compact":
        return wn();
      case "performance":
        return Sn();
      default:
        return vn();
    }
  }, [K, k]), ae = te(() => X.filter((O) => !O.hidden), [X]), Z = te(() => Math.ceil(_ / w), [_, w]), R = te(() => {
    if (!I)
      return e;
    const O = (v - 1) * w, ce = O + w;
    return _ > 0 && e.length <= w ? e : e.slice(O, ce);
  }, [e, I, v, w, _]), H = (O) => {
    if (!c)
      return;
    c(O, h === O && S === "asc" ? "desc" : "asc");
  }, fe = (O) => {
    O < 1 || O > Z || !b || b(O);
  };
  return /* @__PURE__ */ t.jsxs("div", { children: [
    Y && A && /* @__PURE__ */ t.jsx(Mn, { filters: $, onFiltersChange: A, isLoading: r, showAdvanced: W, onToggleAdvanced: () => J(!W) }),
    /* @__PURE__ */ t.jsxs("div", { style: {
      position: "relative"
    }, children: [
      r && /* @__PURE__ */ t.jsx(Yn, { children: /* @__PURE__ */ t.jsx(Wn, {}) }),
      /* @__PURE__ */ t.jsx(An, { height: f, scrollable: M, children: /* @__PURE__ */ t.jsxs(On, { bordered: s, striped: n, compact: p, className: L, children: [
        /* @__PURE__ */ t.jsx($n, { stickyHeader: l, children: /* @__PURE__ */ t.jsxs(Fn, { children: [
          U && /* @__PURE__ */ t.jsx(Pr, { width: "40px", align: "center" }),
          ae.map((O) => /* @__PURE__ */ t.jsxs(Pr, { sortable: O.sortable, isSorted: h === O.id, align: O.align, width: O.width, onClick: () => O.sortable && H(O.id), children: [
            O.header,
            O.sortable && /* @__PURE__ */ t.jsx(Bn, { direction: h === O.id ? S : void 0 })
          ] }, O.id))
        ] }) }),
        /* @__PURE__ */ t.jsx(zn, { children: R.length > 0 ? R.map((O, ce) => /* @__PURE__ */ t.jsx(Nn, { trade: O, index: ce, columns: ae, isSelected: i ? i(O, ce) : !1, hoverable: a, striped: n, expandable: U, onRowClick: d, expandedContent: j == null ? void 0 : j(O) }, O.trade.id || ce)) : /* @__PURE__ */ t.jsx("tr", { children: /* @__PURE__ */ t.jsx("td", { colSpan: ae.length + (U ? 1 : 0), children: /* @__PURE__ */ t.jsx(qn, { children: q }) }) }) })
      ] }) }),
      I && Z > 0 && /* @__PURE__ */ t.jsxs(Un, { children: [
        /* @__PURE__ */ t.jsxs(Hn, { children: [
          "Showing ",
          Math.min((v - 1) * w + 1, _),
          " to",
          " ",
          Math.min(v * w, _),
          " of ",
          _,
          " entries"
        ] }),
        /* @__PURE__ */ t.jsxs(Vn, { children: [
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => fe(1), disabled: v === 1, children: "First" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => fe(v - 1), disabled: v === 1, children: "Prev" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => fe(v + 1), disabled: v === Z, children: "Next" }),
          /* @__PURE__ */ t.jsx(oe, { size: "small", variant: "outline", onClick: () => fe(Z), disabled: v === Z, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}, Gn = /* @__PURE__ */ u.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), xs = ({
  title: e,
  children: r,
  isLoading: s = !1,
  hasError: n = !1,
  errorMessage: a = "An error occurred while loading data",
  showRetry: p = !0,
  onRetry: l,
  isEmpty: f = !1,
  emptyMessage: d = "No data available",
  emptyActionText: i,
  onEmptyAction: c,
  actionButton: h,
  className: S,
  ...I
}) => {
  const v = /* @__PURE__ */ t.jsx(Gn, { children: h });
  let w;
  return s ? w = /* @__PURE__ */ t.jsx(eo, { variant: "card", text: "Loading data..." }) : n ? w = /* @__PURE__ */ t.jsx(Er, { title: "Error", description: a, variant: "compact", actionText: p ? "Retry" : void 0, onAction: p ? l : void 0 }) : f ? w = /* @__PURE__ */ t.jsx(Er, { title: "No Data", description: d, variant: "compact", actionText: i, onAction: c }) : w = r, /* @__PURE__ */ t.jsx(No, { title: e, actions: v, className: S, ...I, children: w });
}, Qn = /* @__PURE__ */ u.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), Jn = /* @__PURE__ */ u.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), Kn = /* @__PURE__ */ u.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), Xn = /* @__PURE__ */ u.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), bs = ({
  header: e,
  sidebar: r,
  children: s,
  sidebarCollapsed: n = !1,
  // toggleSidebar, // Unused prop
  className: a
}) => /* @__PURE__ */ t.jsxs(Qn, { sidebarCollapsed: n, className: a, children: [
  /* @__PURE__ */ t.jsx(Jn, { children: e }),
  /* @__PURE__ */ t.jsx(Kn, { collapsed: n, children: r }),
  /* @__PURE__ */ t.jsx(Xn, { children: s })
] });
function ys(e, r = {}) {
  const {
    fetchOnMount: s = !0,
    dependencies: n = []
  } = r, [a, p] = se({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), l = pe(async (...f) => {
    p((d) => ({
      ...d,
      isLoading: !0,
      error: null
    }));
    try {
      const d = await e(...f);
      return p({
        data: d,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), d;
    } catch (d) {
      const i = d instanceof Error ? d : new Error(String(d));
      throw p((c) => ({
        ...c,
        isLoading: !1,
        error: i,
        isInitialized: !0
      })), i;
    }
  }, [e]);
  return xe(() => {
    s && l();
  }, [s, l, ...n]), {
    ...a,
    fetchData: l,
    refetch: () => l()
  };
}
function vs(e, r) {
  const [s, n] = se(e);
  return xe(() => {
    const a = setTimeout(() => {
      n(e);
    }, r);
    return () => {
      clearTimeout(a);
    };
  }, [e, r]), s;
}
function ws(e = {}) {
  const {
    componentName: r,
    logToConsole: s = !0,
    reportToMonitoring: n = !0,
    onError: a
  } = e, [p, l] = se(null), [f, d] = se(!1), i = pe((S) => {
    if (l(S), d(!0), s) {
      const I = r ? `[${r}]` : "";
      console.error(`Error caught by useErrorHandler${I}:`, S);
    }
    a && a(S);
  }, [r, s, n, a]), c = pe(() => {
    l(null), d(!1);
  }, []), h = pe(async (S) => {
    try {
      return await S();
    } catch (I) {
      i(I);
      return;
    }
  }, [i]);
  return xe(() => () => {
    l(null), d(!1);
  }, []), {
    error: p,
    hasError: f,
    handleError: i,
    resetError: c,
    tryExecute: h
  };
}
function Dr(e, r) {
  const s = () => {
    if (typeof window > "u")
      return r;
    try {
      const l = window.localStorage.getItem(e);
      return l ? JSON.parse(l) : r;
    } catch (l) {
      return console.warn(`Error reading localStorage key "${e}":`, l), r;
    }
  }, [n, a] = se(s), p = (l) => {
    try {
      const f = l instanceof Function ? l(n) : l;
      a(f), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(f));
    } catch (f) {
      console.warn(`Error setting localStorage key "${e}":`, f);
    }
  };
  return xe(() => {
    const l = (f) => {
      f.key === e && f.newValue && a(JSON.parse(f.newValue));
    };
    return window.addEventListener("storage", l), () => window.removeEventListener("storage", l);
  }, [e]), [n, p];
}
function Ss(e) {
  const {
    totalItems: r,
    itemsPerPage: s = 10,
    initialPage: n = 1,
    persistKey: a
  } = e, [p, l] = a ? Dr(`${a}_page`, n) : se(n), [f, d] = a ? Dr(`${a}_itemsPerPage`, s) : se(s), i = te(() => Math.max(1, Math.ceil(r / f)), [r, f]), c = te(() => Math.min(Math.max(1, p), i), [p, i]);
  c !== p && l(c);
  const h = (c - 1) * f, S = Math.min(h + f - 1, r - 1), I = c > 1, v = c < i, w = te(() => {
    const M = [];
    if (i <= 5)
      for (let Y = 1; Y <= i; Y++)
        M.push(Y);
    else {
      let Y = Math.max(1, c - Math.floor(2.5));
      const $ = Math.min(i, Y + 5 - 1);
      $ === i && (Y = Math.max(1, $ - 5 + 1));
      for (let A = Y; A <= $; A++)
        M.push(A);
    }
    return M;
  }, [c, i]), _ = pe(() => {
    v && l(c + 1);
  }, [v, c, l]), b = pe(() => {
    I && l(c - 1);
  }, [I, c, l]), C = pe((q) => {
    const M = Math.min(Math.max(1, q), i);
    l(M);
  }, [i, l]), L = pe((q) => {
    d(q), l(1);
  }, [d, l]);
  return {
    currentPage: c,
    itemsPerPage: f,
    totalPages: i,
    hasPreviousPage: I,
    hasNextPage: v,
    startIndex: h,
    endIndex: S,
    pageRange: w,
    nextPage: _,
    previousPage: b,
    goToPage: C,
    setItemsPerPage: L
  };
}
const E = {
  // F1 colors
  f1Red: "#e10600",
  f1RedDark: "#c10500",
  f1RedLight: "#ff3b36",
  f1Blue: "#1e5bc6",
  f1BlueDark: "#1a4da8",
  f1BlueLight: "#4a7dd8",
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, F = {
  background: "#1a1f2c",
  surface: "#252a37",
  cardBackground: "#252a37",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: E.green,
  warning: E.yellow,
  error: E.red,
  info: E.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: E.f1Red,
  // Trading specific colors
  profit: E.green,
  loss: E.red,
  neutral: E.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, Q = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: E.green,
  warning: E.yellow,
  error: E.red,
  info: E.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: E.f1Red,
  // Trading specific colors
  profit: E.green,
  loss: E.red,
  neutral: E.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, He = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, Ve = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, Ge = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, Qe = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, Je = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, Ke = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, Xe = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, Ze = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, er = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, rr = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, Wr = {
  name: "f1",
  colors: {
    // Primary colors
    primary: E.f1Red,
    primaryDark: E.f1RedDark,
    primaryLight: E.f1RedLight,
    // Secondary colors
    secondary: E.f1Blue,
    secondaryDark: E.f1BlueDark,
    secondaryLight: E.f1BlueLight,
    // Accent colors
    accent: E.purple,
    accentDark: E.purpleDark,
    accentLight: E.purpleLight,
    // Status colors
    success: F.success,
    warning: F.warning,
    error: F.error,
    info: F.info,
    // Neutral colors
    background: F.background,
    surface: F.surface,
    cardBackground: F.surface,
    border: F.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: F.textPrimary,
    textSecondary: F.textSecondary,
    textDisabled: F.textDisabled,
    textInverse: F.textInverse,
    // Chart colors
    chartGrid: F.chartGrid,
    chartLine: F.chartLine,
    chartAxis: E.gray400,
    chartTooltip: F.tooltipBackground,
    // Trading specific colors
    profit: F.profit,
    loss: F.loss,
    neutral: F.neutral,
    // Tab colors
    tabActive: E.f1Red,
    tabInactive: E.gray600,
    // Component specific colors
    tooltipBackground: F.tooltipBackground,
    modalBackground: F.modalBackground,
    sidebarBackground: E.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)"
  },
  spacing: He,
  breakpoints: Ke,
  fontSizes: Ve,
  fontWeights: Ge,
  lineHeights: Qe,
  fontFamilies: Je,
  borderRadius: Xe,
  shadows: Ze,
  transitions: er,
  zIndex: rr
}, Zn = {
  name: "light",
  colors: {
    // Primary colors
    primary: E.f1Red,
    primaryDark: E.f1RedDark,
    primaryLight: E.f1RedLight,
    // Secondary colors
    secondary: E.f1Blue,
    secondaryDark: E.f1BlueDark,
    secondaryLight: E.f1BlueLight,
    // Accent colors
    accent: E.purple,
    accentDark: E.purpleDark,
    accentLight: E.purpleLight,
    // Status colors
    success: Q.success,
    warning: Q.warning,
    error: Q.error,
    info: Q.info,
    // Neutral colors
    background: Q.background,
    surface: Q.surface,
    cardBackground: Q.surface,
    border: Q.border,
    divider: E.blackTransparent10,
    // Text colors
    textPrimary: Q.textPrimary,
    textSecondary: Q.textSecondary,
    textDisabled: Q.textDisabled,
    textInverse: Q.textInverse,
    // Chart colors
    chartGrid: Q.chartGrid,
    chartLine: Q.chartLine,
    chartAxis: E.gray600,
    chartTooltip: Q.tooltipBackground,
    // Trading specific colors
    profit: Q.profit,
    loss: Q.loss,
    neutral: Q.neutral,
    // Tab colors
    tabActive: E.f1Red,
    tabInactive: E.gray400,
    // Component specific colors
    tooltipBackground: Q.tooltipBackground,
    modalBackground: Q.modalBackground,
    sidebarBackground: E.white,
    headerBackground: "rgba(0, 0, 0, 0.05)"
  },
  spacing: He,
  breakpoints: Ke,
  fontSizes: Ve,
  fontWeights: Ge,
  lineHeights: Qe,
  fontFamilies: Je,
  borderRadius: Xe,
  shadows: Ze,
  transitions: er,
  zIndex: rr
}, es = {
  name: "dark",
  colors: {
    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)
    primary: E.f1Blue,
    primaryDark: E.f1BlueDark,
    primaryLight: E.f1BlueLight,
    // Secondary colors
    secondary: E.f1Blue,
    secondaryDark: E.f1BlueDark,
    secondaryLight: E.f1BlueLight,
    // Accent colors
    accent: E.purple,
    accentDark: E.purpleDark,
    accentLight: E.purpleLight,
    // Status colors
    success: F.success,
    warning: F.warning,
    error: F.error,
    info: F.info,
    // Neutral colors
    background: E.gray900,
    // Slightly different from F1 theme
    surface: E.gray800,
    cardBackground: E.gray800,
    border: E.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: E.white,
    textSecondary: E.gray300,
    textDisabled: E.gray500,
    textInverse: E.gray900,
    // Chart colors
    chartGrid: F.chartGrid,
    chartLine: E.f1Blue,
    // Using blue instead of red
    chartAxis: E.gray400,
    chartTooltip: F.tooltipBackground,
    // Trading specific colors
    profit: F.profit,
    loss: F.loss,
    neutral: F.neutral,
    // Tab colors
    tabActive: E.f1Blue,
    // Using blue instead of red
    tabInactive: E.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: E.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)"
  },
  spacing: He,
  breakpoints: Ke,
  fontSizes: Ve,
  fontWeights: Ge,
  lineHeights: Qe,
  fontFamilies: Je,
  borderRadius: Xe,
  shadows: Ze,
  transitions: er,
  zIndex: rr
}, rs = /* @__PURE__ */ yt(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), ts = rs, os = {
  f1: Wr,
  light: Zn,
  dark: es
}, tr = Wr, qe = (e) => os[e] || tr, Ur = Ar({
  theme: tr,
  setTheme: () => {
  }
}), Es = () => Or(Ur), Is = ({
  initialTheme: e = tr,
  persistTheme: r = !0,
  storageKey: s = "adhd-dashboard-theme",
  children: n
}) => {
  const [a, p] = se(() => {
    if (r && typeof window < "u") {
      const i = window.localStorage.getItem(s);
      if (i)
        try {
          const c = qe(i);
          return c || JSON.parse(i);
        } catch (c) {
          console.error("Failed to parse stored theme:", c);
        }
    }
    return typeof e == "string" ? qe(e) : e;
  }), l = (d) => {
    const i = typeof d == "string" ? qe(d) : d;
    p(i), r && typeof window < "u" && window.localStorage.setItem(s, i.name || JSON.stringify(i));
  }, f = ({
    children: d
  }) => /* @__PURE__ */ t.jsxs(vt, { theme: a, children: [
    /* @__PURE__ */ t.jsx(ts, {}),
    d
  ] });
  return /* @__PURE__ */ t.jsx(Ur.Provider, { value: {
    theme: a,
    setTheme: l
  }, children: /* @__PURE__ */ t.jsx(f, { children: n }) });
};
function Cs(e, r, s = "StoreContext") {
  const n = Ar(void 0);
  n.displayName = s;
  const a = ({
    children: i,
    initialState: c
  }) => {
    const [h, S] = bt(e, c || r), I = te(() => ({
      state: h,
      dispatch: S
    }), [h]);
    return /* @__PURE__ */ t.jsx(n.Provider, { value: I, children: i });
  };
  function p() {
    const i = Or(n);
    if (i === void 0)
      throw new Error(`use${s} must be used within a ${s}Provider`);
    return i;
  }
  function l(i) {
    const {
      state: c
    } = p();
    return i(c);
  }
  function f(i) {
    const {
      dispatch: c
    } = p();
    return te(() => (...h) => {
      c(i(...h));
    }, [c, i]);
  }
  function d(i) {
    const {
      dispatch: c
    } = p();
    return te(() => {
      const h = {};
      for (const S in i)
        h[S] = (...I) => {
          c(i[S](...I));
        };
      return h;
    }, [c, i]);
  }
  return {
    Context: n,
    Provider: a,
    useStore: p,
    useSelector: l,
    useAction: f,
    useActions: d
  };
}
function js(...e) {
  const r = e.pop(), s = e;
  let n = null, a = null;
  return (p) => {
    const l = s.map((f) => f(p));
    return (n === null || l.length !== n.length || l.some((f, d) => f !== n[d])) && (a = r(...l), n = l), a;
  };
}
function Ts(e, r) {
  const {
    key: s,
    initialState: n,
    version: a = 1,
    migrate: p,
    serialize: l = JSON.stringify,
    deserialize: f = JSON.parse,
    filter: d = (b) => b,
    merge: i = (b, C) => ({
      ...C,
      ...b
    }),
    debug: c = !1
  } = r, h = () => {
    try {
      const b = localStorage.getItem(s);
      if (b === null)
        return null;
      const {
        state: C,
        version: L
      } = f(b);
      return L !== a && p ? (c && console.log(`Migrating state from version ${L} to ${a}`), p(C, L)) : C;
    } catch (b) {
      return c && console.error("Error loading state from local storage:", b), null;
    }
  }, S = (b) => {
    try {
      const C = d(b), L = l({
        state: C,
        version: a
      });
      localStorage.setItem(s, L);
    } catch (C) {
      c && console.error("Error saving state to local storage:", C);
    }
  }, I = () => {
    try {
      localStorage.removeItem(s);
    } catch (b) {
      c && console.error("Error clearing state from local storage:", b);
    }
  }, v = h(), w = v ? i(v, n) : n;
  return c && v && (console.log("Loaded persisted state:", v), console.log("Merged initial state:", w)), {
    reducer: (b, C) => {
      const L = e(b, C);
      return S(L), L;
    },
    initialState: w,
    clear: I
  };
}
function _s(e, r = "$") {
  return `${r}${e.toFixed(2)}`;
}
function ks(e, r = 1) {
  return `${(e * 100).toFixed(r)}%`;
}
function Rs(e, r = "short") {
  const s = typeof e == "string" ? new Date(e) : e;
  switch (r) {
    case "medium":
      return s.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return s.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return s.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function Ns(e, r = 50) {
  return e.length <= r ? e : `${e.substring(0, r - 3)}...`;
}
function Ls() {
  return Math.random().toString(36).substring(2, 9);
}
function Ps(e, r) {
  let s = null;
  return function(...n) {
    const a = () => {
      s = null, e(...n);
    };
    s && clearTimeout(s), s = setTimeout(a, r);
  };
}
function Ds(e, r) {
  let s = !1;
  return function(...n) {
    s || (e(...n), s = !0, setTimeout(() => {
      s = !1;
    }, r));
  };
}
function Ms(e = {}) {
  console.log("Monitoring service initialized", e);
}
function As(e, r) {
  console.error("Error captured by monitoring service:", e, r);
}
function Os(e) {
  console.log("User set for monitoring service:", e);
}
function $s(e, r) {
  const s = performance.now();
  return {
    name: e,
    startTime: s,
    finish: () => {
      const a = performance.now() - s;
      console.log(`Transaction "${e}" finished in ${a.toFixed(2)}ms`, r);
    }
  };
}
export {
  us as AppErrorBoundary,
  Pe as Badge,
  oe as Button,
  No as Card,
  bs as DashboardTemplate,
  xs as DataCard,
  Er as EmptyState,
  Wo as ErrorBoundary,
  ps as FeatureErrorBoundary,
  fs as FormField,
  he as Input,
  eo as LoadingPlaceholder,
  gs as Modal,
  Ct as OrderSide,
  jt as OrderStatus,
  It as OrderType,
  we as Select,
  ls as StatusIndicator,
  y as TRADE_COLUMN_IDS,
  ms as Table,
  ds as Tag,
  Ur as ThemeContext,
  Is as ThemeProvider,
  Tt as TimeInForce,
  St as TradeDirection,
  Et as TradeStatus,
  hs as TradeTable,
  Mn as TradeTableFilters,
  Nn as TradeTableRow,
  zr as UnifiedErrorBoundary,
  E as baseColors,
  Xe as borderRadius,
  Ke as breakpoints,
  As as captureError,
  js as createSelector,
  Cs as createStoreContext,
  F as darkModeColors,
  es as darkTheme,
  Ps as debounce,
  Wr as f1Theme,
  Je as fontFamilies,
  Ve as fontSizes,
  Ge as fontWeights,
  _s as formatCurrency,
  Rs as formatDate,
  ks as formatPercentage,
  _r as formatTime,
  Ls as generateId,
  wn as getCompactTradeTableColumns,
  Sn as getPerformanceTradeTableColumns,
  vn as getTradeTableColumns,
  Ms as initMonitoring,
  Q as lightModeColors,
  Zn as lightTheme,
  Qe as lineHeights,
  Ts as persistState,
  Os as setUser,
  Ze as shadows,
  He as spacing,
  $s as startTransaction,
  Ds as throttle,
  cs as tradeStorage,
  is as tradeStorageService,
  er as transitions,
  Ns as truncateText,
  ys as useAsyncData,
  vs as useDebounce,
  ws as useErrorHandler,
  Dr as useLocalStorage,
  Ss as usePagination,
  Es as useTheme,
  rr as zIndex
};
