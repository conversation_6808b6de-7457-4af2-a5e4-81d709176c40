(function(m,T){typeof exports=="object"&&typeof module<"u"?T(exports,require("react"),require("styled-components"),require("react-dom")):typeof define=="function"&&define.amd?define(["exports","react","styled-components","react-dom"],T):(m=typeof globalThis<"u"?globalThis:m||self,T(m.ADHDTradingDashboardShared={},m.React,m.styled,m.ReactDOM))})(this,function(m,T,t,eo){"use strict";var nr=(e=>(e.LONG="LONG",e.SHORT="SHORT",e))(nr||{}),sr=(e=>(e.OPEN="OPEN",e.CLOSED="CLOSED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e.PENDING="PENDING",e))(sr||{}),ar=(e=>(e.MARKET="MARKET",e.LIMIT="LIMIT",e.STOP="STOP",e.STOP_LIMIT="STOP_LIMIT",e))(ar||{}),ir=(e=>(e.BUY="BUY",e.SELL="SELL",e))(ir||{}),cr=(e=>(e.PENDING="PENDING",e.FILLED="FILLED",e.PARTIALLY_FILLED="PARTIALLY_FILLED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e))(cr||{}),lr=(e=>(e.GTC="GTC",e.IOC="IOC",e.FOK="FOK",e.DAY="DAY",e))(lr||{});const z={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class ro{constructor(){this.dbName="adhd-trading-dashboard",this.version=2,this.db=null,this.stores={trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"}}async initDB(){return this.db?this.db:new Promise((r,a)=>{const s=indexedDB.open(this.dbName,this.version);s.onupgradeneeded=i=>{var d;const f=i.target.result;if(!f.objectStoreNames.contains(this.stores.trades)){const p=f.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});p.createIndex(z.DATE,z.DATE,{unique:!1}),p.createIndex(z.MODEL_TYPE,z.MODEL_TYPE,{unique:!1}),p.createIndex(z.SESSION,z.SESSION,{unique:!1}),p.createIndex(z.WIN_LOSS,z.WIN_LOSS,{unique:!1}),p.createIndex(z.R_MULTIPLE,z.R_MULTIPLE,{unique:!1})}if(f.objectStoreNames.contains(this.stores.fvg_details)||f.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),f.objectStoreNames.contains(this.stores.setups)||f.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),f.objectStoreNames.contains(this.stores.analysis)||f.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!f.objectStoreNames.contains(this.stores.sessions)){f.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const u=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(d=s.transaction)==null||d.addEventListener("complete",()=>{const l=f.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);u.forEach(h=>l.add(h))})}},s.onsuccess=i=>{this.db=i.target.result,r(this.db)},s.onerror=i=>{console.error("Error opening IndexedDB:",i),a(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(r){try{const a=await this.initDB();return new Promise((s,i)=>{const f=a.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");f.onerror=c=>{console.error("Transaction error:",c),i(new Error("Failed to save trade with details"))};const d=f.objectStore(this.stores.trades),p={...r.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},u=d.add(p);u.onsuccess=()=>{const c=u.result,l=[];if(r.fvg_details){const h=f.objectStore(this.stores.fvg_details),E={...r.fvg_details,trade_id:c};l.push(new Promise((C,w)=>{const S=h.add(E);S.onsuccess=()=>C(),S.onerror=()=>w(new Error("Failed to save FVG details"))}))}if(r.setup){const h=f.objectStore(this.stores.setups),E={...r.setup,trade_id:c};l.push(new Promise((C,w)=>{const S=h.add(E);S.onsuccess=()=>C(),S.onerror=()=>w(new Error("Failed to save setup data"))}))}if(r.analysis){const h=f.objectStore(this.stores.analysis),E={...r.analysis,trade_id:c};l.push(new Promise((C,w)=>{const S=h.add(E);S.onsuccess=()=>C(),S.onerror=()=>w(new Error("Failed to save analysis data"))}))}f.oncomplete=()=>{s(c)}},u.onerror=c=>{console.error("Error saving trade:",c),i(new Error("Failed to save trade"))}})}catch(a){throw console.error("Error in saveTradeWithDetails:",a),new Error("Failed to save trade with details")}}async getTradeById(r){try{const a=await this.initDB();return new Promise((s,i)=>{const f=a.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=f.objectStore(this.stores.trades).get(r);p.onsuccess=()=>{const u=p.result;if(!u){s(null);return}const c={trade:u},E=f.objectStore(this.stores.fvg_details).index("trade_id").get(r);E.onsuccess=()=>{E.result&&(c.fvg_details=E.result);const S=f.objectStore(this.stores.setups).index("trade_id").get(r);S.onsuccess=()=>{S.result&&(c.setup=S.result);const I=f.objectStore(this.stores.analysis).index("trade_id").get(r);I.onsuccess=()=>{I.result&&(c.analysis=I.result),s(c)},I.onerror=D=>{console.error("Error getting analysis data:",D),s(c)}},S.onerror=k=>{console.error("Error getting setup data:",k),s(c)}},E.onerror=C=>{console.error("Error getting FVG details:",C),s(c)}},p.onerror=u=>{console.error("Error getting trade:",u),i(new Error("Failed to get trade"))}})}catch(a){return console.error("Error in getTradeById:",a),null}}async getPerformanceMetrics(){try{const r=await this.initDB();return new Promise((a,s)=>{const d=r.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();d.onsuccess=()=>{const p=d.result;if(p.length===0){a({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const u=p.length,c=p.filter(L=>L[z.WIN_LOSS]==="Win").length,l=p.filter(L=>L[z.WIN_LOSS]==="Loss").length,h=u>0?c/u*100:0,E=p.filter(L=>L.achieved_pl!==void 0).map(L=>L.achieved_pl),C=E.reduce((L,G)=>L+G,0),w=E.filter(L=>L>0),S=E.filter(L=>L<0),k=w.length>0?w.reduce((L,G)=>L+G,0)/w.length:0,x=S.length>0?Math.abs(S.reduce((L,G)=>L+G,0)/S.length):0,I=w.length>0?Math.max(...w):0,D=S.length>0?Math.abs(Math.min(...S)):0,Y=w.reduce((L,G)=>L+G,0),A=Math.abs(S.reduce((L,G)=>L+G,0)),W=A>0?Y/A:0,$=p.filter(L=>L[z.R_MULTIPLE]!==void 0).map(L=>L[z.R_MULTIPLE]),F=$.length>0?$.reduce((L,G)=>L+G,0)/$.length:0,N=F*(h/100);let X=0,H=0,j=0;for(const L of p)if(L.achieved_pl!==void 0){X+=L.achieved_pl,X>H&&(H=X);const G=H-X;G>j&&(j=G)}const U=H>0?j/H*100:0,K=$.length>0?Math.sqrt($.length)*F/Math.sqrt($.reduce((L,G)=>L+Math.pow(G-F,2),0)/$.length):0,Z=p.map(L=>L.date).sort(),ae=Z.length>0?Z[0]:"",ee=Z.length>0?Z[Z.length-1]:"";a({totalTrades:u,winningTrades:c,losingTrades:l,winRate:h,profitFactor:W,averageWin:k,averageLoss:x,largestWin:I,largestLoss:D,totalPnl:C,maxDrawdown:j,maxDrawdownPercent:U,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:F,expectancy:N,sqn:K,period:"all",startDate:ae,endDate:ee})},d.onerror=p=>{console.error("Error getting performance metrics:",p),s(new Error("Failed to get performance metrics"))}})}catch(r){throw console.error("Error in getPerformanceMetrics:",r),new Error("Failed to get performance metrics")}}async filterTrades(r){try{const a=await this.initDB();return new Promise((s,i)=>{const f=a.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=f.objectStore(this.stores.trades).getAll();p.onsuccess=async()=>{let u=p.result;r.dateFrom&&(u=u.filter(l=>l.date>=r.dateFrom)),r.dateTo&&(u=u.filter(l=>l.date<=r.dateTo)),r.model_type&&(u=u.filter(l=>l[z.MODEL_TYPE]===r.model_type)),r.session&&(u=u.filter(l=>l[z.SESSION]===r.session)),r.direction&&(u=u.filter(l=>l[z.DIRECTION]===r.direction)),r.win_loss&&(u=u.filter(l=>l[z.WIN_LOSS]===r.win_loss)),r.market&&(u=u.filter(l=>l[z.MARKET]===r.market)),r.min_r_multiple!==void 0&&(u=u.filter(l=>l[z.R_MULTIPLE]!==void 0&&l[z.R_MULTIPLE]>=r.min_r_multiple)),r.max_r_multiple!==void 0&&(u=u.filter(l=>l[z.R_MULTIPLE]!==void 0&&l[z.R_MULTIPLE]<=r.max_r_multiple)),r.min_pattern_quality!==void 0&&(u=u.filter(l=>l[z.PATTERN_QUALITY_RATING]!==void 0&&l[z.PATTERN_QUALITY_RATING]>=r.min_pattern_quality)),r.max_pattern_quality!==void 0&&(u=u.filter(l=>l[z.PATTERN_QUALITY_RATING]!==void 0&&l[z.PATTERN_QUALITY_RATING]<=r.max_pattern_quality));const c=[];for(const l of u){const h={trade:l},w=f.objectStore(this.stores.fvg_details).index("trade_id").get(l.id);await new Promise(A=>{w.onsuccess=()=>{w.result&&(h.fvg_details=w.result),A()},w.onerror=()=>A()});const x=f.objectStore(this.stores.setups).index("trade_id").get(l.id);await new Promise(A=>{x.onsuccess=()=>{x.result&&(h.setup=x.result),A()},x.onerror=()=>A()});const Y=f.objectStore(this.stores.analysis).index("trade_id").get(l.id);await new Promise(A=>{Y.onsuccess=()=>{Y.result&&(h.analysis=Y.result),A()},Y.onerror=()=>A()}),c.push(h)}s(c)},p.onerror=u=>{console.error("Error filtering trades:",u),i(new Error("Failed to filter trades"))}})}catch(a){throw console.error("Error in filterTrades:",a),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(r){return console.error("Error in getAllTrades:",r),[]}}async deleteTrade(r){try{const a=await this.initDB();return new Promise((s,i)=>{const f=a.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");f.onerror=x=>{console.error("Transaction error:",x),i(new Error("Failed to delete trade"))};const u=f.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));u.onsuccess=x=>{const I=x.target.result;I&&(I.delete(),I.continue())};const h=f.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));h.onsuccess=x=>{const I=x.target.result;I&&(I.delete(),I.continue())};const w=f.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));w.onsuccess=x=>{const I=x.target.result;I&&(I.delete(),I.continue())};const k=f.objectStore(this.stores.trades).delete(r);f.oncomplete=()=>{s()},k.onerror=x=>{console.error("Error deleting trade:",x),i(new Error("Failed to delete trade"))}})}catch(a){throw console.error("Error in deleteTrade:",a),new Error("Failed to delete trade")}}async updateTradeWithDetails(r,a){try{const s=await this.initDB();return new Promise((i,f)=>{const d=s.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");d.onerror=l=>{console.error("Transaction error:",l),f(new Error("Failed to update trade"))};const p=d.objectStore(this.stores.trades),u={...a.trade,id:r,updated_at:new Date().toISOString()},c=p.put(u);c.onsuccess=()=>{if(a.fvg_details){const l=d.objectStore(this.stores.fvg_details),h={...a.fvg_details,trade_id:r};l.put(h)}if(a.setup){const l=d.objectStore(this.stores.setups),h={...a.setup,trade_id:r};l.put(h)}if(a.analysis){const l=d.objectStore(this.stores.analysis),h={...a.analysis,trade_id:r};l.put(h)}},d.oncomplete=()=>{i()},c.onerror=l=>{console.error("Error updating trade:",l),f(new Error("Failed to update trade"))}})}catch(s){throw console.error("Error in updateTradeWithDetails:",s),new Error("Failed to update trade")}}}const dr=new ro,oo=dr,to=dr;var o={},no={get exports(){return o},set exports(e){o=e}},he={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur;function so(){if(ur)return he;ur=1;var e=T,r=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function d(p,u,c){var l,h={},E=null,C=null;c!==void 0&&(E=""+c),u.key!==void 0&&(E=""+u.key),u.ref!==void 0&&(C=u.ref);for(l in u)s.call(u,l)&&!f.hasOwnProperty(l)&&(h[l]=u[l]);if(p&&p.defaultProps)for(l in u=p.defaultProps,u)h[l]===void 0&&(h[l]=u[l]);return{$$typeof:r,type:p,key:E,ref:C,props:h,_owner:i.current}}return he.Fragment=a,he.jsx=d,he.jsxs=d,he}var be={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fr;function ao(){return fr||(fr=1,process.env.NODE_ENV!=="production"&&function(){var e=T,r=Symbol.for("react.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),p=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),l=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),C=Symbol.for("react.offscreen"),w=Symbol.iterator,S="@@iterator";function k(n){if(n===null||typeof n!="object")return null;var g=w&&n[w]||n[S];return typeof g=="function"?g:null}var x=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function I(n){{for(var g=arguments.length,b=new Array(g>1?g-1:0),_=1;_<g;_++)b[_-1]=arguments[_];D("error",n,b)}}function D(n,g,b){{var _=x.ReactDebugCurrentFrame,M=_.getStackAddendum();M!==""&&(g+="%s",b=b.concat([M]));var q=b.map(function(R){return String(R)});q.unshift("Warning: "+g),Function.prototype.apply.call(console[n],console,q)}}var Y=!1,A=!1,W=!1,$=!1,F=!1,N;N=Symbol.for("react.module.reference");function X(n){return!!(typeof n=="string"||typeof n=="function"||n===s||n===f||F||n===i||n===c||n===l||$||n===C||Y||A||W||typeof n=="object"&&n!==null&&(n.$$typeof===E||n.$$typeof===h||n.$$typeof===d||n.$$typeof===p||n.$$typeof===u||n.$$typeof===N||n.getModuleId!==void 0))}function H(n,g,b){var _=n.displayName;if(_)return _;var M=g.displayName||g.name||"";return M!==""?b+"("+M+")":b}function j(n){return n.displayName||"Context"}function U(n){if(n==null)return null;if(typeof n.tag=="number"&&I("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;switch(n){case s:return"Fragment";case a:return"Portal";case f:return"Profiler";case i:return"StrictMode";case c:return"Suspense";case l:return"SuspenseList"}if(typeof n=="object")switch(n.$$typeof){case p:var g=n;return j(g)+".Consumer";case d:var b=n;return j(b._context)+".Provider";case u:return H(n,n.render,"ForwardRef");case h:var _=n.displayName||null;return _!==null?_:U(n.type)||"Memo";case E:{var M=n,q=M._payload,R=M._init;try{return U(R(q))}catch{return null}}}return null}var K=Object.assign,Z=0,ae,ee,L,G,fe,B,le;function Fr(){}Fr.__reactDisabledLog=!0;function Xn(){{if(Z===0){ae=console.log,ee=console.info,L=console.warn,G=console.error,fe=console.group,B=console.groupCollapsed,le=console.groupEnd;var n={configurable:!0,enumerable:!0,value:Fr,writable:!0};Object.defineProperties(console,{info:n,log:n,warn:n,error:n,group:n,groupCollapsed:n,groupEnd:n})}Z++}}function Zn(){{if(Z--,Z===0){var n={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:K({},n,{value:ae}),info:K({},n,{value:ee}),warn:K({},n,{value:L}),error:K({},n,{value:G}),group:K({},n,{value:fe}),groupCollapsed:K({},n,{value:B}),groupEnd:K({},n,{value:le})})}Z<0&&I("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Je=x.ReactCurrentDispatcher,Ke;function Re(n,g,b){{if(Ke===void 0)try{throw Error()}catch(M){var _=M.stack.trim().match(/\n( *(at )?)/);Ke=_&&_[1]||""}return`
`+Ke+n}}var Xe=!1,Me;{var es=typeof WeakMap=="function"?WeakMap:Map;Me=new es}function Br(n,g){if(!n||Xe)return"";{var b=Me.get(n);if(b!==void 0)return b}var _;Xe=!0;var M=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var q;q=Je.current,Je.current=null,Xn();try{if(g){var R=function(){throw Error()};if(Object.defineProperty(R.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(R,[])}catch(de){_=de}Reflect.construct(n,[],R)}else{try{R.call()}catch(de){_=de}n.call(R.prototype)}}else{try{throw Error()}catch(de){_=de}n()}}catch(de){if(de&&_&&typeof de.stack=="string"){for(var P=de.stack.split(`
`),re=_.stack.split(`
`),Q=P.length-1,J=re.length-1;Q>=1&&J>=0&&P[Q]!==re[J];)J--;for(;Q>=1&&J>=0;Q--,J--)if(P[Q]!==re[J]){if(Q!==1||J!==1)do if(Q--,J--,J<0||P[Q]!==re[J]){var ne=`
`+P[Q].replace(" at new "," at ");return n.displayName&&ne.includes("<anonymous>")&&(ne=ne.replace("<anonymous>",n.displayName)),typeof n=="function"&&Me.set(n,ne),ne}while(Q>=1&&J>=0);break}}}finally{Xe=!1,Je.current=q,Zn(),Error.prepareStackTrace=M}var me=n?n.displayName||n.name:"",Zr=me?Re(me):"";return typeof n=="function"&&Me.set(n,Zr),Zr}function rs(n,g,b){return Br(n,!1)}function os(n){var g=n.prototype;return!!(g&&g.isReactComponent)}function Oe(n,g,b){if(n==null)return"";if(typeof n=="function")return Br(n,os(n));if(typeof n=="string")return Re(n);switch(n){case c:return Re("Suspense");case l:return Re("SuspenseList")}if(typeof n=="object")switch(n.$$typeof){case u:return rs(n.render);case h:return Oe(n.type,g,b);case E:{var _=n,M=_._payload,q=_._init;try{return Oe(q(M),g,b)}catch{}}}return""}var Ae=Object.prototype.hasOwnProperty,zr={},qr=x.ReactDebugCurrentFrame;function $e(n){if(n){var g=n._owner,b=Oe(n.type,n._source,g?g.type:null);qr.setExtraStackFrame(b)}else qr.setExtraStackFrame(null)}function ts(n,g,b,_,M){{var q=Function.call.bind(Ae);for(var R in n)if(q(n,R)){var P=void 0;try{if(typeof n[R]!="function"){var re=Error((_||"React class")+": "+b+" type `"+R+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof n[R]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw re.name="Invariant Violation",re}P=n[R](g,R,_,b,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Q){P=Q}P&&!(P instanceof Error)&&($e(M),I("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",_||"React class",b,R,typeof P),$e(null)),P instanceof Error&&!(P.message in zr)&&(zr[P.message]=!0,$e(M),I("Failed %s type: %s",b,P.message),$e(null))}}}var ns=Array.isArray;function Ze(n){return ns(n)}function ss(n){{var g=typeof Symbol=="function"&&Symbol.toStringTag,b=g&&n[Symbol.toStringTag]||n.constructor.name||"Object";return b}}function as(n){try{return Yr(n),!1}catch{return!0}}function Yr(n){return""+n}function Wr(n){if(as(n))return I("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",ss(n)),Yr(n)}var Se=x.ReactCurrentOwner,is={key:!0,ref:!0,__self:!0,__source:!0},Ur,Hr,er;er={};function cs(n){if(Ae.call(n,"ref")){var g=Object.getOwnPropertyDescriptor(n,"ref").get;if(g&&g.isReactWarning)return!1}return n.ref!==void 0}function ls(n){if(Ae.call(n,"key")){var g=Object.getOwnPropertyDescriptor(n,"key").get;if(g&&g.isReactWarning)return!1}return n.key!==void 0}function ds(n,g){if(typeof n.ref=="string"&&Se.current&&g&&Se.current.stateNode!==g){var b=U(Se.current.type);er[b]||(I('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',U(Se.current.type),n.ref),er[b]=!0)}}function us(n,g){{var b=function(){Ur||(Ur=!0,I("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",g))};b.isReactWarning=!0,Object.defineProperty(n,"key",{get:b,configurable:!0})}}function fs(n,g){{var b=function(){Hr||(Hr=!0,I("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",g))};b.isReactWarning=!0,Object.defineProperty(n,"ref",{get:b,configurable:!0})}}var ps=function(n,g,b,_,M,q,R){var P={$$typeof:r,type:n,key:g,ref:b,props:R,_owner:q};return P._store={},Object.defineProperty(P._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(P,"_self",{configurable:!1,enumerable:!1,writable:!1,value:_}),Object.defineProperty(P,"_source",{configurable:!1,enumerable:!1,writable:!1,value:M}),Object.freeze&&(Object.freeze(P.props),Object.freeze(P)),P};function gs(n,g,b,_,M){{var q,R={},P=null,re=null;b!==void 0&&(Wr(b),P=""+b),ls(g)&&(Wr(g.key),P=""+g.key),cs(g)&&(re=g.ref,ds(g,M));for(q in g)Ae.call(g,q)&&!is.hasOwnProperty(q)&&(R[q]=g[q]);if(n&&n.defaultProps){var Q=n.defaultProps;for(q in Q)R[q]===void 0&&(R[q]=Q[q])}if(P||re){var J=typeof n=="function"?n.displayName||n.name||"Unknown":n;P&&us(R,J),re&&fs(R,J)}return ps(n,P,re,M,_,Se.current,R)}}var rr=x.ReactCurrentOwner,Vr=x.ReactDebugCurrentFrame;function ge(n){if(n){var g=n._owner,b=Oe(n.type,n._source,g?g.type:null);Vr.setExtraStackFrame(b)}else Vr.setExtraStackFrame(null)}var or;or=!1;function tr(n){return typeof n=="object"&&n!==null&&n.$$typeof===r}function Gr(){{if(rr.current){var n=U(rr.current.type);if(n)return`

Check the render method of \``+n+"`."}return""}}function ms(n){{if(n!==void 0){var g=n.fileName.replace(/^.*[\\\/]/,""),b=n.lineNumber;return`

Check your code at `+g+":"+b+"."}return""}}var Qr={};function hs(n){{var g=Gr();if(!g){var b=typeof n=="string"?n:n.displayName||n.name;b&&(g=`

Check the top-level render call using <`+b+">.")}return g}}function Jr(n,g){{if(!n._store||n._store.validated||n.key!=null)return;n._store.validated=!0;var b=hs(g);if(Qr[b])return;Qr[b]=!0;var _="";n&&n._owner&&n._owner!==rr.current&&(_=" It was passed a child from "+U(n._owner.type)+"."),ge(n),I('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',b,_),ge(null)}}function Kr(n,g){{if(typeof n!="object")return;if(Ze(n))for(var b=0;b<n.length;b++){var _=n[b];tr(_)&&Jr(_,g)}else if(tr(n))n._store&&(n._store.validated=!0);else if(n){var M=k(n);if(typeof M=="function"&&M!==n.entries)for(var q=M.call(n),R;!(R=q.next()).done;)tr(R.value)&&Jr(R.value,g)}}}function bs(n){{var g=n.type;if(g==null||typeof g=="string")return;var b;if(typeof g=="function")b=g.propTypes;else if(typeof g=="object"&&(g.$$typeof===u||g.$$typeof===h))b=g.propTypes;else return;if(b){var _=U(g);ts(b,n.props,"prop",_,n)}else if(g.PropTypes!==void 0&&!or){or=!0;var M=U(g);I("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",M||"Unknown")}typeof g.getDefaultProps=="function"&&!g.getDefaultProps.isReactClassApproved&&I("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function xs(n){{for(var g=Object.keys(n.props),b=0;b<g.length;b++){var _=g[b];if(_!=="children"&&_!=="key"){ge(n),I("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",_),ge(null);break}}n.ref!==null&&(ge(n),I("Invalid attribute `ref` supplied to `React.Fragment`."),ge(null))}}function Xr(n,g,b,_,M,q){{var R=X(n);if(!R){var P="";(n===void 0||typeof n=="object"&&n!==null&&Object.keys(n).length===0)&&(P+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var re=ms(M);re?P+=re:P+=Gr();var Q;n===null?Q="null":Ze(n)?Q="array":n!==void 0&&n.$$typeof===r?(Q="<"+(U(n.type)||"Unknown")+" />",P=" Did you accidentally export a JSX literal instead of a component?"):Q=typeof n,I("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Q,P)}var J=gs(n,g,b,M,q);if(J==null)return J;if(R){var ne=g.children;if(ne!==void 0)if(_)if(Ze(ne)){for(var me=0;me<ne.length;me++)Kr(ne[me],n);Object.freeze&&Object.freeze(ne)}else I("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Kr(ne,n)}return n===s?xs(J):bs(J),J}}function vs(n,g,b){return Xr(n,g,b,!0)}function ys(n,g,b){return Xr(n,g,b,!1)}var ws=ys,Ss=vs;be.Fragment=s,be.jsx=ws,be.jsxs=Ss}()),be}(function(e){process.env.NODE_ENV==="production"?e.exports=so():e.exports=ao()})(no);const io={small:t.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:t.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:t.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},co=(e,r,a=!1)=>t.css(["",""],({theme:s})=>{let i,f,d;switch(e){case"primary":i=r?s.colors.primary:`${s.colors.primary}20`,f=r?s.colors.textInverse:s.colors.primary,d=s.colors.primary;break;case"secondary":i=r?s.colors.secondary:`${s.colors.secondary}20`,f=r?s.colors.textInverse:s.colors.secondary,d=s.colors.secondary;break;case"success":i=r?s.colors.success:`${s.colors.success}20`,f=r?s.colors.textInverse:s.colors.success,d=s.colors.success;break;case"warning":i=r?s.colors.warning:`${s.colors.warning}20`,f=r?s.colors.textInverse:s.colors.warning,d=s.colors.warning;break;case"error":i=r?s.colors.error:`${s.colors.error}20`,f=r?s.colors.textInverse:s.colors.error,d=s.colors.error;break;case"info":i=r?s.colors.info:`${s.colors.info}20`,f=r?s.colors.textInverse:s.colors.info,d=s.colors.info;break;case"neutral":i=r?s.colors.textSecondary:`${s.colors.textSecondary}10`,f=r?s.colors.textInverse:s.colors.textSecondary,d=s.colors.textSecondary;break;default:i=r?s.colors.textSecondary:`${s.colors.textSecondary}20`,f=r?s.colors.textInverse:s.colors.textSecondary,d=s.colors.textSecondary}return a?`
          background-color: transparent;
          color: ${d};
          border: 1px solid ${d};
        `:`
        background-color: ${i};
        color: ${f};
        border: 1px solid transparent;
      `}),pr=t.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),lo=t(pr).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),uo=t(pr).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),fo=t.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:r,dot:a})=>a?"50%":r?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>io[e],({variant:e,solid:r,outlined:a})=>co(e,r,a||!1),({dot:e})=>e&&t.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&t.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&t.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),xe=({children:e,variant:r="default",size:a="medium",solid:s=!1,className:i,onClick:f,rounded:d=!1,dot:p=!1,counter:u=!1,outlined:c=!1,startIcon:l,endIcon:h,max:E,inline:C=!0})=>{let w=e;return u&&typeof e=="number"&&E!==void 0&&e>E&&(w=`${E}+`),o.jsx(fo,{variant:r,size:a,solid:s,clickable:!!f,className:i,onClick:f,rounded:d,dot:p,counter:u,outlined:c,inline:C,children:!p&&o.jsxs(o.Fragment,{children:[l&&o.jsx(lo,{children:l}),w,h&&o.jsx(uo,{children:h})]})})},po=t.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),go=t.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],po,({theme:e})=>e.spacing.xs),mo={small:t.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:t.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:t.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},ho={primary:t.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:t.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:t.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:t.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),success:t.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:t.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},bo=t.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({size:e="medium"})=>mo[e],({variant:e="primary"})=>ho[e],({fullWidth:e})=>e&&t.css(["width:100%;"]),({$hasStartIcon:e})=>e&&t.css(["& > *:first-child{margin-right:",";}"],({theme:r})=>r.spacing.xs),({$hasEndIcon:e})=>e&&t.css(["& > *:last-child{margin-left:",";}"],({theme:r})=>r.spacing.xs)),xo=t.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),oe=({children:e,variant:r="primary",disabled:a=!1,loading:s=!1,size:i="medium",fullWidth:f=!1,startIcon:d,endIcon:p,onClick:u,className:c,type:l="button",...h})=>o.jsx(bo,{variant:r,disabled:a||s,size:i,fullWidth:f,onClick:u,className:c,type:l,$hasStartIcon:!!d&&!s,$hasEndIcon:!!p&&!s,...h,children:o.jsxs(xo,{children:[s&&o.jsx(go,{}),!s&&d,e,!s&&p]})}),vo=t.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),yo=t.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),wo=t.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:a,isFocused:s})=>r?e.colors.error:a?e.colors.success:s?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&t.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:a,hasSuccess:s})=>e&&t.css(["box-shadow:0 0 0 2px ",";"],a?`${r.colors.error}33`:s?`${r.colors.success}33`:`${r.colors.primary}33`),({size:e})=>{switch(e){case"small":return t.css(["height:32px;"]);case"large":return t.css(["height:48px;"]);default:return t.css(["height:40px;"])}}),gr=t.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),So=t.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&t.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&t.css(["padding-right:0;"]),({size:e,theme:r})=>e==="small"?t.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?t.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):t.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),Eo=t.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Co=t.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:a})=>r?e.colors.error:a?e.colors.success:e.colors.textSecondary),ue=({value:e,onChange:r,placeholder:a,disabled:s=!1,error:i,type:f="text",name:d,id:p,className:u,required:c=!1,autoComplete:l,label:h,helperText:E,startIcon:C,endIcon:w,loading:S=!1,success:k=!1,clearable:x=!1,onClear:I,maxLength:D,showCharCount:Y=!1,size:A="medium",fullWidth:W=!1,...$})=>{const[F,N]=T.useState(!1),X=T.useRef(null),H=()=>{I?I():r(""),X.current&&X.current.focus()},j=ee=>{N(!0),$.onFocus&&$.onFocus(ee)},U=ee=>{N(!1),$.onBlur&&$.onBlur(ee)},K=x&&e&&!s,Z=(e==null?void 0:e.length)||0,ae=Y||D!==void 0&&D>0;return o.jsxs(vo,{className:u,fullWidth:W,children:[h&&o.jsxs(yo,{htmlFor:p,children:[h,c&&" *"]}),o.jsxs(wo,{hasError:!!i,hasSuccess:!!k,disabled:!!s,size:A,hasStartIcon:!!C,hasEndIcon:!!(w||K),isFocused:!!F,children:[C&&o.jsx(gr,{children:C}),o.jsx(So,{ref:X,type:f,value:e,onChange:ee=>r(ee.target.value),placeholder:a,disabled:!!(s||S),name:d,id:p,required:!!c,autoComplete:l,hasStartIcon:!!C,hasEndIcon:!!(w||K),size:A,maxLength:D,onFocus:j,onBlur:U,...$}),K&&o.jsx(Eo,{type:"button",onClick:H,tabIndex:-1,children:"✕"}),w&&o.jsx(gr,{children:w})]}),(i||E||ae)&&o.jsxs(Co,{hasError:!!i,hasSuccess:!!k,children:[o.jsx("div",{children:i||E}),ae&&o.jsxs("div",{children:[Z,D!==void 0&&`/${D}`]})]})]})},mr={small:t.css(["height:100px;"]),medium:t.css(["height:200px;"]),large:t.css(["height:300px;"]),custom:e=>t.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Io={default:t.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:t.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:t.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:t.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},To=t.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),jo=t.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:r,customWidth:a})=>e==="custom"?mr.custom({customHeight:r,customWidth:a}):mr[e],({variant:e})=>Io[e]),_o=t.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,To,({theme:e})=>e.spacing.sm),ko=t.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),hr=({variant:e="default",size:r="medium",height:a="200px",width:s,text:i="Loading...",showSpinner:f=!0,className:d})=>o.jsxs(jo,{variant:e,size:r,customHeight:a,customWidth:s,className:d,children:[f&&o.jsx(_o,{}),i&&o.jsx(ko,{children:i})]}),No=t.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Lo=t.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),Po=t.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:a,isFocused:s})=>r?e.colors.error:a?e.colors.success:s?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&t.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:a,hasSuccess:s})=>e&&t.css(["box-shadow:0 0 0 2px ",";"],a?`${r.colors.error}33`:s?`${r.colors.success}33`:`${r.colors.primary}33`),({size:e})=>{switch(e){case"small":return t.css(["height:32px;"]);case"large":return t.css(["height:48px;"]);default:return t.css(["height:40px;"])}}),Do=t.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Ro=t.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&t.css(["padding-left:0;"]),({size:e,theme:r})=>e==="small"?t.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?t.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):t.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),Mo=t.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:a})=>r?e.colors.error:a?e.colors.success:e.colors.textSecondary),Oo=t.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>e.colors.textPrimary),pe=({options:e,value:r,onChange:a,disabled:s=!1,error:i,name:f,id:d,className:p,required:u=!1,placeholder:c,label:l,helperText:h,size:E="medium",fullWidth:C=!0,loading:w=!1,success:S=!1,startIcon:k,...x})=>{const[I,D]=T.useState(!1),Y=N=>{D(!0),x.onFocus&&x.onFocus(N)},A=N=>{D(!1),x.onBlur&&x.onBlur(N)},W={},$=[];e.forEach(N=>{N.group?(W[N.group]||(W[N.group]=[]),W[N.group].push(N)):$.push(N)});const F=Object.keys(W).length>0;return o.jsxs(No,{className:p,fullWidth:C,children:[l&&o.jsxs(Lo,{htmlFor:d,children:[l,u&&" *"]}),o.jsxs(Po,{hasError:!!i,hasSuccess:!!S,disabled:!!(s||w),size:E,hasStartIcon:!!k,isFocused:!!I,children:[k&&o.jsx(Do,{children:k}),o.jsxs(Ro,{value:r,onChange:N=>a(N.target.value),disabled:!!(s||w),name:f,id:d,required:!!u,hasStartIcon:!!k,size:E,onFocus:Y,onBlur:A,...x,children:[c&&o.jsx("option",{value:"",disabled:!0,children:c}),F?o.jsxs(o.Fragment,{children:[$.map(N=>o.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value)),Object.entries(W).map(([N,X])=>o.jsx(Oo,{label:N,children:X.map(H=>o.jsx("option",{value:H.value,disabled:H.disabled,children:H.label},H.value))},N))]}):e.map(N=>o.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value))]})]}),(i||h)&&o.jsx(Mo,{hasError:!!i,hasSuccess:!!S,children:o.jsx("div",{children:i||h})})]})},br={small:"8px",medium:"12px",large:"16px"},Ao={small:t.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:t.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:t.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},$o=t.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),Fo=t.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),Bo=t.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>br[e],({size:e})=>br[e],({status:e,theme:r,pulse:a})=>{let s,i;switch(e){case"success":s=r.colors.success,i="76, 175, 80";break;case"error":s=r.colors.error,i="244, 67, 54";break;case"warning":s=r.colors.warning,i="255, 152, 0";break;case"info":s=r.colors.info,i="33, 150, 243";break;default:s=r.colors.textSecondary,i="158, 158, 158"}return t.css(["background-color:",";",""],s,a&&t.css(["--pulse-color:",";",""],i,$o))}),zo=t.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>Ao[e],({status:e,theme:r})=>{let a;switch(e){case"success":a=r.colors.success;break;case"error":a=r.colors.error;break;case"warning":a=r.colors.warning;break;case"info":a=r.colors.info;break;default:a=r.colors.textSecondary}return t.css(["color:",";font-weight:",";"],a,r.fontWeights.medium)}),qo=({status:e,size:r="medium",pulse:a=!1,showLabel:s=!1,label:i,className:f})=>{const d=i||e.charAt(0).toUpperCase()+e.slice(1);return o.jsxs(Fo,{className:f,children:[o.jsx(Bo,{status:e,size:r,pulse:a}),s&&o.jsx(zo,{status:e,size:r,children:d})]})},Yo={small:t.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:t.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:t.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},Wo=e=>t.css(["",""],({theme:r})=>{let a,s,i;switch(e){case"primary":a=`${r.colors.primary}10`,s=r.colors.primary,i=`${r.colors.primary}30`;break;case"secondary":a=`${r.colors.secondary}10`,s=r.colors.secondary,i=`${r.colors.secondary}30`;break;case"success":a=`${r.colors.success}10`,s=r.colors.success,i=`${r.colors.success}30`;break;case"warning":a=`${r.colors.warning}10`,s=r.colors.warning,i=`${r.colors.warning}30`;break;case"error":a=`${r.colors.error}10`,s=r.colors.error,i=`${r.colors.error}30`;break;case"info":a=`${r.colors.info}10`,s=r.colors.info,i=`${r.colors.info}30`;break;default:a=`${r.colors.textSecondary}10`,s=r.colors.textSecondary,i=`${r.colors.textSecondary}30`}return`
        background-color: ${a};
        color: ${s};
        border: 1px solid ${i};
      `}),Uo=t.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>Yo[e],({variant:e})=>Wo(e),({clickable:e})=>e&&t.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),Ho=t.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:r})=>{const a={small:"12px",medium:"14px",large:"16px"};return`
      width: ${a[e]};
      height: ${a[e]};
      font-size: ${r.fontSizes.xs};
    `}),Vo=({children:e,variant:r="default",size:a="medium",removable:s=!1,onRemove:i,className:f,onClick:d})=>{const p=u=>{u.stopPropagation(),i==null||i()};return o.jsxs(Uo,{variant:r,size:a,clickable:!!d,className:f,onClick:d,children:[e,s&&o.jsx(Ho,{size:a,onClick:p,children:"×"})]})},Go={none:t.css(["padding:0;"]),small:t.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:t.css(["padding:",";"],({theme:e})=>e.spacing.md),large:t.css(["padding:",";"],({theme:e})=>e.spacing.lg)},Qo={default:t.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:t.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:t.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:t.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:t.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},Jo=t.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:r})=>e&&t.css(["border:1px solid ",";"],r.colors.border),({padding:e})=>Go[e],({variant:e})=>Qo[e],({clickable:e})=>e&&t.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:r})=>r.shadows.medium)),Ko=t.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),Xo=t.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),Zo=t.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),et=t.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),rt=t.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),ot=t.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),tt=t.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),nt=t.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),st=t.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),at=t.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),xr=({children:e,title:r,subtitle:a,bordered:s=!0,variant:i="default",padding:f="medium",className:d,footer:p,actions:u,isLoading:c=!1,hasError:l=!1,errorMessage:h="An error occurred",clickable:E=!1,onClick:C,...w})=>{const S=r||a||u;return o.jsxs(Jo,{bordered:s,variant:i,padding:f,clickable:E,className:d,onClick:E?C:void 0,...w,children:[c&&o.jsx(nt,{children:o.jsx(at,{})}),S&&o.jsxs(Ko,{children:[o.jsxs(Xo,{children:[r&&o.jsx(Zo,{children:r}),a&&o.jsx(et,{children:a})]}),u&&o.jsx(rt,{children:u})]}),l&&o.jsx(st,{children:o.jsx("p",{children:h})}),o.jsx(ot,{children:e}),p&&o.jsx(tt,{children:p})]})},it=t.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:r})=>{const a={small:r.fontSizes.md,medium:r.fontSizes.lg,large:r.fontSizes.xl};return t.css(["font-size:",";"],a[e])}),ct=t.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:r})=>{const a={small:r.fontSizes.sm,medium:r.fontSizes.md,large:r.fontSizes.lg};return t.css(["font-size:",";"],a[e])}),lt={default:t.css(["background-color:transparent;"]),compact:t.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:t.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},dt=t.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>lt[e],({size:e,theme:r})=>{switch(e){case"small":return t.css(["padding:",";min-height:120px;"],r.spacing.md);case"large":return t.css(["padding:",";min-height:300px;"],r.spacing.xl);default:return t.css(["padding:",";min-height:200px;"],r.spacing.lg)}}),ut=t.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:r})=>{const a={small:"32px",medium:"48px",large:"64px"};return t.css(["font-size:",";svg{width:",";height:",";color:",";}"],a[e],a[e],a[e],r.colors.textSecondary)}),ft=t.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),pt=t.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),Fe=({title:e,description:r,icon:a,actionText:s,onAction:i,variant:f="default",size:d="medium",className:p,children:u})=>o.jsxs(dt,{variant:f,size:d,className:p,children:[a&&o.jsx(ut,{size:d,children:a}),e&&o.jsx(it,{size:d,children:e}),r&&o.jsx(ct,{size:d,children:r}),s&&i&&o.jsx(ft,{children:o.jsx(oe,{variant:"primary",size:d==="small"?"small":"medium",onClick:i,children:s})}),u&&o.jsx(pt,{children:u})]}),vr=t.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),gt=t.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),yr=t.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),Ee=t.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),wr=t.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),Sr=t.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),mt=t.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),Er=t.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),ht=t.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),bt=t(Er).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),xt=({error:e,resetError:r,isAppLevel:a,name:s,onSkip:i})=>{const f=()=>{window.location.reload()};return a?o.jsx(vr,{isAppLevel:!0,children:o.jsxs(gt,{children:[o.jsx(yr,{isAppLevel:!0,children:"Something went wrong"}),o.jsx(Ee,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),o.jsxs(wr,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Ee,{children:e.message}),e.stack&&o.jsx(Sr,{children:e.stack})]}),o.jsx(bt,{onClick:f,children:"Reload Application"})]})}):o.jsxs(vr,{children:[o.jsx(yr,{children:s?`Error in ${s}`:"Something went wrong"}),o.jsx(Ee,{children:s?`We encountered a problem while loading ${s}. You can try again${i?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),o.jsxs(wr,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Ee,{children:e.message}),e.stack&&o.jsx(Sr,{children:e.stack})]}),o.jsxs(mt,{children:[o.jsx(Er,{onClick:r,children:"Try Again"}),i&&o.jsx(ht,{onClick:i,children:"Skip This Feature"})]})]})};class Cr extends T.Component{constructor(r){super(r),this.resetError=()=>{this.setState({hasError:!1,error:null})},this.state={hasError:!1,error:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,a){const{name:s}=this.props,i=s?`ErrorBoundary(${s})`:"ErrorBoundary";console.error(`Error caught by ${i}:`,r,a),this.props.onError&&this.props.onError(r,a)}componentDidUpdate(r){this.state.hasError&&this.props.resetOnPropsChange&&r.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:r,error:a}=this.state,{children:s,fallback:i,name:f,isFeatureBoundary:d,onSkip:p}=this.props;return r&&a?typeof i=="function"?i({error:a,resetError:this.resetError}):i||o.jsx(xt,{error:a,resetError:this.resetError,isAppLevel:!d,name:f,onSkip:p}):s}}const Be=({isAppLevel:e,isFeatureBoundary:r,...a})=>{const s=e?"app":r?"feature":"component",i={resetOnPropsChange:s!=="app",resetOnUnmount:s!=="app",isFeatureBoundary:s==="feature"};return o.jsx(Cr,{...i,...a})},vt=e=>o.jsx(Be,{isAppLevel:!0,...e}),yt=({featureName:e,...r})=>o.jsx(Be,{isFeatureBoundary:!0,name:e,...r}),wt=t.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),St=t.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),Et=t.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),Ct=({children:e,label:r,helperText:a,required:s=!1,error:i,className:f,id:d,...p})=>{const u=d||`field-${Math.random().toString(36).substr(2,9)}`,c=T.Children.map(e,l=>T.isValidElement(l)?T.cloneElement(l,{id:u,required:s,error:i,...l.props}):l);return o.jsxs(wt,{className:f,...p,children:[o.jsxs(St,{htmlFor:u,hasError:!!i,children:[r,s&&o.jsx("span",{className:"required-indicator",children:"*"})]}),c,(a||i)&&o.jsx(Et,{hasError:!!i,children:i||a})]})},It=t.keyframes(["from{opacity:0;}to{opacity:1;}"]),Tt=t.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),jt=t.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,It),_t=t.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},Tt,({size:e})=>e==="fullscreen"&&t.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&t.css(["margin:auto;"])),kt=t.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Nt=t.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Lt=t.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Pt=t.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&t.css(["overflow-y:auto;flex:1;"])),Dt=t.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Rt=({isOpen:e,title:r,children:a,onClose:s,size:i="medium",closeOnOutsideClick:f=!0,showCloseButton:d=!0,footer:p,hasFooter:u=!0,primaryActionText:c,onPrimaryAction:l,primaryActionDisabled:h=!1,primaryActionLoading:E=!1,secondaryActionText:C,onSecondaryAction:w,secondaryActionDisabled:S=!1,className:k,zIndex:x=1e3,centered:I=!0,scrollable:D=!0})=>{const Y=T.useRef(null);T.useEffect(()=>{const F=N=>{N.key==="Escape"&&e&&f&&s()};return document.addEventListener("keydown",F),()=>{document.removeEventListener("keydown",F)}},[e,s,f]);const A=F=>{Y.current&&!Y.current.contains(F.target)&&f&&s()};T.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const W=o.jsxs(o.Fragment,{children:[C&&o.jsx(oe,{variant:"outline",onClick:w,disabled:S,children:C}),c&&o.jsx(oe,{onClick:l,disabled:h,loading:E,children:c})]});if(!e)return null;const $=o.jsx(jt,{onClick:A,zIndex:x,children:o.jsxs(_t,{ref:Y,size:i,className:k,centered:I,scrollable:D,onClick:F=>F.stopPropagation(),children:[(r||d)&&o.jsxs(kt,{children:[r&&o.jsx(Nt,{children:r}),d&&o.jsx(Lt,{onClick:s,"aria-label":"Close",children:"×"})]}),o.jsx(Pt,{scrollable:D,children:a}),u&&(p||c||C)&&o.jsx(Dt,{children:p||W})]})});return eo.createPortal($,document.body)},Mt=t.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),Ot=t.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:r})=>e&&t.css(["border:1px solid ",";border-radius:",";"],r.colors.border,r.borderRadius.sm),({compact:e,theme:r})=>e?t.css(["th,td{padding:"," ",";}"],r.spacing.xs,r.spacing.sm):t.css(["th,td{padding:"," ",";}"],r.spacing.sm,r.spacing.md)),At=t.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&t.css(["position:sticky;top:0;z-index:1;"])),$t=t.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),Ft=t.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&t.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>r.colors.background),({isSorted:e,theme:r})=>e&&t.css(["color:",";"],r.colors.primary)),Bt=t.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),zt=t.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),qt=t.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:r,isSelected:a})=>e&&!a&&t.css(["&:nth-child(even){background-color:","50;}"],r.colors.background),({hoverable:e,theme:r,isSelected:a})=>e&&!a&&t.css(["&:hover{background-color:","aa;}"],r.colors.background),({isSelected:e,theme:r})=>e&&t.css(["background-color:","15;"],r.colors.primary),({isClickable:e})=>e&&t.css(["cursor:pointer;"])),Yt=t.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),Wt=t.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),Ut=t.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),Ht=t.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),Vt=t.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),Gt=t.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),Qt=t.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),Jt=t.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function Kt({columns:e,data:r,isLoading:a=!1,bordered:s=!0,striped:i=!0,hoverable:f=!0,compact:d=!1,stickyHeader:p=!1,height:u,onRowClick:c,isRowSelected:l,onSort:h,sortColumn:E,sortDirection:C,pagination:w=!1,currentPage:S=1,pageSize:k=10,totalRows:x=0,onPageChange:I,onPageSizeChange:D,className:Y,emptyMessage:A="No data available",scrollable:W=!0}){const $=T.useMemo(()=>e.filter(j=>!j.hidden),[e]),F=T.useMemo(()=>Math.ceil(x/k),[x,k]),N=T.useMemo(()=>{if(!w)return r;const j=(S-1)*k,U=j+k;return x>0&&r.length<=k?r:r.slice(j,U)},[r,w,S,k,x]),X=j=>{if(!h)return;h(j,E===j&&C==="asc"?"desc":"asc")},H=j=>{j<1||j>F||!I||I(j)};return o.jsxs("div",{style:{position:"relative"},children:[a&&o.jsx(Qt,{children:o.jsx(Jt,{})}),o.jsx(Mt,{height:u,scrollable:W,children:o.jsxs(Ot,{bordered:s,striped:i,compact:d,className:Y,children:[o.jsx(At,{stickyHeader:p,children:o.jsx($t,{children:$.map(j=>o.jsxs(Ft,{sortable:j.sortable,isSorted:E===j.id,align:j.align,width:j.width,onClick:()=>j.sortable&&X(j.id),children:[j.header,j.sortable&&o.jsx(Bt,{direction:E===j.id?C:void 0})]},j.id))})}),o.jsx(zt,{children:N.length>0?N.map((j,U)=>o.jsx(qt,{hoverable:f,striped:i,isSelected:l?l(j,U):!1,isClickable:!!c,onClick:()=>c&&c(j,U),children:$.map(K=>o.jsx(Yt,{align:K.align,children:K.cell(j,U)},K.id))},U)):o.jsx("tr",{children:o.jsx("td",{colSpan:$.length,children:o.jsx(Wt,{children:A})})})})]})}),w&&F>0&&o.jsxs(Ut,{children:[o.jsxs(Ht,{children:["Showing ",Math.min((S-1)*k+1,x)," to"," ",Math.min(S*k,x)," of ",x," entries"]}),o.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[D&&o.jsxs(Gt,{children:[o.jsx("span",{children:"Show"}),o.jsx("select",{value:k,onChange:j=>D(Number(j.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(j=>o.jsx("option",{value:j,children:j},j))}),o.jsx("span",{children:"entries"})]}),o.jsxs(Vt,{children:[o.jsx(oe,{size:"small",variant:"outline",onClick:()=>H(1),disabled:S===1,children:"First"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>H(S-1),disabled:S===1,children:"Prev"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>H(S+1),disabled:S===F,children:"Next"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>H(F),disabled:S===F,children:"Last"})]})]})]})]})}const v={DATE:"date",SYMBOL:"symbol",DIRECTION:"direction",MODEL_TYPE:"model_type",SESSION:"session",ENTRY_PRICE:"entry_price",EXIT_PRICE:"exit_price",R_MULTIPLE:"r_multiple",ACHIEVED_PL:"achieved_pl",WIN_LOSS:"win_loss",PATTERN_QUALITY:"pattern_quality_rating",ENTRY_TIME:"entry_time",EXIT_TIME:"exit_time"},ze=t.span.withConfig({displayName:"ProfitLossCell",componentId:"sc-14bks31-0"})(["color:",";font-weight:",";"],({isProfit:e,theme:r})=>e?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),Ir=t(xe).withConfig({displayName:"DirectionBadge",componentId:"sc-14bks31-1"})(["background-color:",";color:white;"],({direction:e,theme:r})=>e==="Long"?r.colors.success||"#10b981":r.colors.error||"#ef4444"),Tr=t.span.withConfig({displayName:"QualityRating",componentId:"sc-14bks31-2"})(["color:",";font-weight:",";"],({rating:e,theme:r})=>e>=4?r.colors.success||"#10b981":e>=3?r.colors.warning||"#f59e0b":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),qe=t.span.withConfig({displayName:"RMultipleCell",componentId:"sc-14bks31-3"})(["color:",";font-weight:",";"],({rMultiple:e,theme:r})=>e>0?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),ve=e=>e==null?"-":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e),Ye=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return e}},We=e=>e||"-",jr=()=>[{id:v.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ye(e.trade[v.DATE])},{id:v.SYMBOL,header:"Symbol",sortable:!0,width:"80px",cell:e=>e.trade.market||"MNQ"},{id:v.DIRECTION,header:"Direction",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Ir,{direction:e.trade[v.DIRECTION],size:"small",children:e.trade[v.DIRECTION]})},{id:v.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[v.MODEL_TYPE]||"-"},{id:v.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[v.SESSION]||"-"},{id:v.ENTRY_PRICE,header:"Entry",sortable:!0,width:"100px",align:"right",cell:e=>ve(e.trade[v.ENTRY_PRICE])},{id:v.EXIT_PRICE,header:"Exit",sortable:!0,width:"100px",align:"right",cell:e=>ve(e.trade[v.EXIT_PRICE])},{id:v.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(qe,{rMultiple:e.trade[v.R_MULTIPLE]||0,children:e.trade[v.R_MULTIPLE]?`${e.trade[v.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:v.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(ze,{isProfit:(e.trade[v.ACHIEVED_PL]||0)>0,children:ve(e.trade[v.ACHIEVED_PL])})},{id:v.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(xe,{variant:e.trade[v.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[v.WIN_LOSS]||"-"})},{id:v.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Tr,{rating:e.trade[v.PATTERN_QUALITY]||0,children:e.trade[v.PATTERN_QUALITY]?`${e.trade[v.PATTERN_QUALITY]}/5`:"-"})},{id:v.ENTRY_TIME,header:"Entry Time",sortable:!0,width:"100px",align:"center",cell:e=>We(e.trade[v.ENTRY_TIME])},{id:v.EXIT_TIME,header:"Exit Time",sortable:!0,width:"100px",align:"center",cell:e=>We(e.trade[v.EXIT_TIME])}],_r=()=>[{id:v.DATE,header:"Date",sortable:!0,width:"90px",cell:e=>Ye(e.trade[v.DATE])},{id:v.SYMBOL,header:"Symbol",sortable:!0,width:"60px",cell:e=>e.trade.market||"MNQ"},{id:v.DIRECTION,header:"Dir",sortable:!0,width:"50px",align:"center",cell:e=>o.jsx(Ir,{direction:e.trade[v.DIRECTION],size:"small",children:e.trade[v.DIRECTION].charAt(0)})},{id:v.R_MULTIPLE,header:"R",sortable:!0,width:"60px",align:"right",cell:e=>o.jsx(qe,{rMultiple:e.trade[v.R_MULTIPLE]||0,children:e.trade[v.R_MULTIPLE]?`${e.trade[v.R_MULTIPLE].toFixed(1)}R`:"-"})},{id:v.ACHIEVED_PL,header:"P&L",sortable:!0,width:"80px",align:"right",cell:e=>o.jsx(ze,{isProfit:(e.trade[v.ACHIEVED_PL]||0)>0,children:ve(e.trade[v.ACHIEVED_PL])})},{id:v.WIN_LOSS,header:"Result",sortable:!0,width:"60px",align:"center",cell:e=>o.jsx(xe,{variant:e.trade[v.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[v.WIN_LOSS]==="Win"?"W":e.trade[v.WIN_LOSS]==="Loss"?"L":"-"})}],kr=()=>[{id:v.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ye(e.trade[v.DATE])},{id:v.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[v.MODEL_TYPE]||"-"},{id:v.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[v.SESSION]||"-"},{id:v.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(qe,{rMultiple:e.trade[v.R_MULTIPLE]||0,children:e.trade[v.R_MULTIPLE]?`${e.trade[v.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:v.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(ze,{isProfit:(e.trade[v.ACHIEVED_PL]||0)>0,children:ve(e.trade[v.ACHIEVED_PL])})},{id:v.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Tr,{rating:e.trade[v.PATTERN_QUALITY]||0,children:e.trade[v.PATTERN_QUALITY]?`${e.trade[v.PATTERN_QUALITY]}/5`:"-"})},{id:v.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(xe,{variant:e.trade[v.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[v.WIN_LOSS]||"-"})}],Xt=t.tr.withConfig({displayName:"TableRow",componentId:"sc-uyrnn-0"})([""," "," "," "," ",""],({striped:e,theme:r,isSelected:a})=>{var s;return e&&!a&&t.css(["&:nth-child(even){background-color:","50;}"],((s=r.colors)==null?void 0:s.background)||"#f8f9fa")},({hoverable:e,theme:r,isSelected:a})=>{var s;return e&&!a&&t.css(["&:hover{background-color:","aa;}"],((s=r.colors)==null?void 0:s.background)||"#f8f9fa")},({isSelected:e,theme:r})=>{var a;return e&&t.css(["background-color:","15;"],((a=r.colors)==null?void 0:a.primary)||"#3b82f6")},({isClickable:e})=>e&&t.css(["cursor:pointer;"]),({isExpanded:e,theme:r})=>{var a;return e&&t.css(["border-bottom:2px solid ",";"],((a=r.colors)==null?void 0:a.primary)||"#3b82f6")}),Nr=t.td.withConfig({displayName:"TableCell",componentId:"sc-uyrnn-1"})(["text-align:",";border-bottom:1px solid ",";color:",";padding:"," ",";vertical-align:middle;"],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),Zt=t.tr.withConfig({displayName:"ExpandedRow",componentId:"sc-uyrnn-2"})(["display:",";"],({isVisible:e})=>e?"table-row":"none"),en=t.td.withConfig({displayName:"ExpandedCell",componentId:"sc-uyrnn-3"})(["padding:0;border-bottom:1px solid ",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),rn=t.div.withConfig({displayName:"ExpandedContent",componentId:"sc-uyrnn-4"})(["padding:",";background-color:","30;border-left:3px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),on=t.button.withConfig({displayName:"ExpandButton",componentId:"sc-uyrnn-5"})(["background:none;border:none;cursor:pointer;padding:",";color:",";font-size:",";display:flex;align-items:center;justify-content:center;border-radius:",";transition:all 0.2s ease;&:hover{background-color:",";color:",";}&:focus{outline:2px solid ",";outline-offset:2px;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),tn=t.span.withConfig({displayName:"ExpandIcon",componentId:"sc-uyrnn-6"})(["display:inline-block;transition:transform 0.2s ease;transform:",";&::after{content:'▶';}"],({isExpanded:e})=>e?"rotate(90deg)":"rotate(0deg)"),nn=t.div.withConfig({displayName:"TradeDetails",componentId:"sc-uyrnn-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),ye=t.div.withConfig({displayName:"DetailGroup",componentId:"sc-uyrnn-8"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),we=t.span.withConfig({displayName:"DetailLabel",componentId:"sc-uyrnn-9"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),te=t.span.withConfig({displayName:"DetailValue",componentId:"sc-uyrnn-10"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"}),sn=({trade:e})=>o.jsxs(nn,{children:[e.fvg_details&&o.jsxs(ye,{children:[o.jsx(we,{children:"FVG Details"}),o.jsxs(te,{children:["Type: ",e.fvg_details.rd_type||"-"]}),o.jsxs(te,{children:["Entry Version: ",e.fvg_details.entry_version||"-"]}),o.jsxs(te,{children:["Draw on Liquidity: ",e.fvg_details.draw_on_liquidity||"-"]})]}),e.setup&&o.jsxs(ye,{children:[o.jsx(we,{children:"Setup Classification"}),o.jsxs(te,{children:["Primary: ",e.setup.primary_setup||"-"]}),o.jsxs(te,{children:["Secondary: ",e.setup.secondary_setup||"-"]}),o.jsxs(te,{children:["Liquidity: ",e.setup.liquidity_taken||"-"]})]}),e.analysis&&o.jsxs(ye,{children:[o.jsx(we,{children:"Analysis"}),o.jsxs(te,{children:["DOL Target: ",e.analysis.dol_target_type||"-"]}),o.jsxs(te,{children:["Path Quality: ",e.analysis.path_quality||"-"]}),o.jsxs(te,{children:["Clustering: ",e.analysis.clustering||"-"]})]}),o.jsxs(ye,{children:[o.jsx(we,{children:"Timing"}),o.jsxs(te,{children:["Entry: ",e.trade.entry_time||"-"]}),o.jsxs(te,{children:["Exit: ",e.trade.exit_time||"-"]}),o.jsxs(te,{children:["FVG: ",e.trade.fvg_time||"-"]}),o.jsxs(te,{children:["RD: ",e.trade.rd_time||"-"]})]}),e.trade.notes&&o.jsxs(ye,{style:{gridColumn:"1 / -1"},children:[o.jsx(we,{children:"Notes"}),o.jsx(te,{children:e.trade.notes})]})]}),Lr=({trade:e,index:r,columns:a,isSelected:s=!1,hoverable:i=!0,striped:f=!0,expandable:d=!1,isExpanded:p=!1,onRowClick:u,onToggleExpand:c,expandedContent:l})=>{const[h,E]=T.useState(!1),C=p!==void 0?p:h,w=x=>{x.target.closest("button")||u==null||u(e,r)},S=x=>{x.stopPropagation(),c?c(e,r):E(!h)},k=a.filter(x=>!x.hidden);return o.jsxs(o.Fragment,{children:[o.jsxs(Xt,{hoverable:i,striped:f,isSelected:s,isClickable:!!u,isExpanded:C,onClick:w,children:[d&&o.jsx(Nr,{align:"center",style:{width:"40px",padding:"8px"},children:o.jsx(on,{onClick:S,children:o.jsx(tn,{isExpanded:C})})}),k.map(x=>o.jsx(Nr,{align:x.align,children:x.cell(e,r)},x.id))]}),d&&o.jsx(Zt,{isVisible:C,children:o.jsx(en,{colSpan:k.length+1,children:o.jsx(rn,{children:l||o.jsx(sn,{trade:e})})})})]})},se={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",DATE_FROM:"dateFrom",DATE_TO:"dateTo",SESSION:"session",DIRECTION:"direction",MARKET:"market",MIN_R_MULTIPLE:"min_r_multiple",MAX_R_MULTIPLE:"max_r_multiple",MIN_PATTERN_QUALITY:"min_pattern_quality",MAX_PATTERN_QUALITY:"max_pattern_quality"},an=t.div.withConfig({displayName:"FiltersContainer",componentId:"sc-32k3gq-0"})(["display:flex;flex-direction:column;gap:",";padding:",";background-color:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),Pr=t.div.withConfig({displayName:"FilterRow",componentId:"sc-32k3gq-1"})(["display:flex;gap:",";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),ie=t.div.withConfig({displayName:"FilterGroup",componentId:"sc-32k3gq-2"})(["display:flex;flex-direction:column;gap:",";min-width:120px;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),ce=t.label.withConfig({displayName:"FilterLabel",componentId:"sc-32k3gq-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),cn=t.div.withConfig({displayName:"FilterActions",componentId:"sc-32k3gq-4"})(["display:flex;gap:",";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),ln=t.div.withConfig({displayName:"AdvancedFilters",componentId:"sc-32k3gq-5"})(["display:",";flex-direction:column;gap:",";padding-top:",";border-top:1px solid ",";"],({isVisible:e})=>e?"flex":"none",({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),Dr=t.div.withConfig({displayName:"RangeInputGroup",componentId:"sc-32k3gq-6"})(["display:flex;gap:",";align-items:center;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),Rr=t.span.withConfig({displayName:"RangeLabel",componentId:"sc-32k3gq-7"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),Mr=({filters:e,onFiltersChange:r,onReset:a,isLoading:s=!1,showAdvanced:i=!1,onToggleAdvanced:f})=>{const d=(c,l)=>{r({...e,[c]:l})},p=()=>{r({}),a==null||a()},u=Object.values(e).some(c=>c!==void 0&&c!==""&&c!==null);return o.jsxs(an,{children:[o.jsxs(Pr,{children:[o.jsxs(ie,{children:[o.jsx(ce,{children:"Date From"}),o.jsx(ue,{type:"date",value:e.dateFrom||"",onChange:c=>d(se.DATE_FROM,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Date To"}),o.jsx(ue,{type:"date",value:e.dateTo||"",onChange:c=>d(se.DATE_TO,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Model Type"}),o.jsx(pe,{options:[{value:"",label:"All Models"},{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"True-RD",label:"True-RD"},{value:"IMM-RD",label:"IMM-RD"},{value:"Dispersed-RD",label:"Dispersed-RD"},{value:"Wide-Gap-RD",label:"Wide-Gap-RD"}],value:e.model_type||"",onChange:c=>d(se.MODEL_TYPE,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Session"}),o.jsx(pe,{options:[{value:"",label:"All Sessions"},{value:"Pre-Market",label:"Pre-Market"},{value:"NY Open",label:"NY Open"},{value:"10:50-11:10",label:"10:50-11:10"},{value:"11:50-12:10",label:"11:50-12:10"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"13:50-14:10",label:"13:50-14:10"},{value:"14:50-15:10",label:"14:50-15:10"},{value:"15:15-15:45",label:"15:15-15:45"},{value:"MOC",label:"MOC"},{value:"Post MOC",label:"Post MOC"}],value:e.session||"",onChange:c=>d(se.SESSION,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Direction"}),o.jsx(pe,{options:[{value:"",label:"All Directions"},{value:"Long",label:"Long"},{value:"Short",label:"Short"}],value:e.direction||"",onChange:c=>d(se.DIRECTION,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Result"}),o.jsx(pe,{options:[{value:"",label:"All Results"},{value:"Win",label:"Win"},{value:"Loss",label:"Loss"}],value:e.win_loss||"",onChange:c=>d(se.WIN_LOSS,c),disabled:s})]}),o.jsxs(cn,{children:[f&&o.jsxs(oe,{variant:"outline",size:"small",onClick:f,disabled:s,children:[i?"Hide":"Show"," Advanced"]}),o.jsx(oe,{variant:"outline",size:"small",onClick:p,disabled:s||!u,children:"Reset"})]})]}),o.jsx(ln,{isVisible:i,children:o.jsxs(Pr,{children:[o.jsxs(ie,{children:[o.jsx(ce,{children:"Market"}),o.jsx(pe,{options:[{value:"",label:"All Markets"},{value:"MNQ",label:"MNQ"},{value:"NQ",label:"NQ"},{value:"ES",label:"ES"},{value:"MES",label:"MES"},{value:"YM",label:"YM"},{value:"MYM",label:"MYM"}],value:e.market||"",onChange:c=>d(se.MARKET,c),disabled:s})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"R Multiple Range"}),o.jsxs(Dr,{children:[o.jsx(ue,{type:"number",placeholder:"Min",step:"0.1",value:e.min_r_multiple||"",onChange:c=>d(se.MIN_R_MULTIPLE,c?Number(c):void 0),disabled:s,style:{width:"80px"}}),o.jsx(Rr,{children:"to"}),o.jsx(ue,{type:"number",placeholder:"Max",step:"0.1",value:e.max_r_multiple||"",onChange:c=>d(se.MAX_R_MULTIPLE,c?Number(c):void 0),disabled:s,style:{width:"80px"}})]})]}),o.jsxs(ie,{children:[o.jsx(ce,{children:"Pattern Quality Range"}),o.jsxs(Dr,{children:[o.jsx(ue,{type:"number",placeholder:"Min",min:"1",max:"5",step:"0.1",value:e.min_pattern_quality||"",onChange:c=>d(se.MIN_PATTERN_QUALITY,c?Number(c):void 0),disabled:s,style:{width:"80px"}}),o.jsx(Rr,{children:"to"}),o.jsx(ue,{type:"number",placeholder:"Max",min:"1",max:"5",step:"0.1",value:e.max_pattern_quality||"",onChange:c=>d(se.MAX_PATTERN_QUALITY,c?Number(c):void 0),disabled:s,style:{width:"80px"}})]})]})]})})]})},dn=t.div.withConfig({displayName:"TableContainer",componentId:"sc-13oxwmo-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),un=t.table.withConfig({displayName:"StyledTable",componentId:"sc-13oxwmo-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({bordered:e,theme:r})=>{var a,s;return e&&t.css(["border:1px solid ",";border-radius:",";"],((a=r.colors)==null?void 0:a.border)||"#e5e7eb",((s=r.borderRadius)==null?void 0:s.sm)||"4px")},({compact:e,theme:r})=>{var a,s,i,f;return e?t.css(["th,td{padding:"," ",";}"],((a=r.spacing)==null?void 0:a.xs)||"8px",((s=r.spacing)==null?void 0:s.sm)||"12px"):t.css(["th,td{padding:"," ",";}"],((i=r.spacing)==null?void 0:i.sm)||"12px",((f=r.spacing)==null?void 0:f.md)||"16px")}),fn=t.thead.withConfig({displayName:"TableHeader",componentId:"sc-13oxwmo-2"})(["",""],({stickyHeader:e})=>e&&t.css(["position:sticky;top:0;z-index:1;"])),pn=t.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-13oxwmo-3"})(["background-color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"}),Or=t.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13oxwmo-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&t.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>{var a;return((a=r.colors)==null?void 0:a.background)||"#f8f9fa"}),({isSorted:e,theme:r})=>{var a;return e&&t.css(["color:",";"],((a=r.colors)==null?void 0:a.primary)||"#3b82f6")}),gn=t.span.withConfig({displayName:"SortIcon",componentId:"sc-13oxwmo-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),mn=t.tbody.withConfig({displayName:"TableBody",componentId:"sc-13oxwmo-6"})([""]),hn=t.div.withConfig({displayName:"EmptyState",componentId:"sc-13oxwmo-7"})(["padding:",";text-align:center;color:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),bn=t.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-13oxwmo-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>{var r;return`${((r=e.colors)==null?void 0:r.background)||"#ffffff"}80`}),xn=t.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-13oxwmo-9"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),vn=t.div.withConfig({displayName:"PaginationContainer",componentId:"sc-13oxwmo-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"}),yn=t.div.withConfig({displayName:"PageInfo",componentId:"sc-13oxwmo-11"})(["color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),wn=t.div.withConfig({displayName:"PaginationControls",componentId:"sc-13oxwmo-12"})(["display:flex;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),Sn=({data:e,isLoading:r=!1,bordered:a=!0,striped:s=!0,hoverable:i=!0,compact:f=!1,stickyHeader:d=!1,height:p,onRowClick:u,isRowSelected:c,onSort:l,sortColumn:h,sortDirection:E,pagination:C=!1,currentPage:w=1,pageSize:S=10,totalRows:k=0,onPageChange:x,onPageSizeChange:I,className:D,emptyMessage:Y="No trades available",scrollable:A=!0,showFilters:W=!1,filters:$={},onFiltersChange:F,columnPreset:N="default",customColumns:X,expandableRows:H=!1,renderExpandedContent:j})=>{const[U,K]=T.useState(!1),Z=T.useMemo(()=>{if(X)return X;switch(N){case"compact":return _r();case"performance":return kr();default:return jr()}},[X,N]),ae=T.useMemo(()=>Z.filter(B=>!B.hidden),[Z]),ee=T.useMemo(()=>Math.ceil(k/S),[k,S]),L=T.useMemo(()=>{if(!C)return e;const B=(w-1)*S,le=B+S;return k>0&&e.length<=S?e:e.slice(B,le)},[e,C,w,S,k]),G=B=>{if(!l)return;l(B,h===B&&E==="asc"?"desc":"asc")},fe=B=>{B<1||B>ee||!x||x(B)};return o.jsxs("div",{children:[W&&F&&o.jsx(Mr,{filters:$,onFiltersChange:F,isLoading:r,showAdvanced:U,onToggleAdvanced:()=>K(!U)}),o.jsxs("div",{style:{position:"relative"},children:[r&&o.jsx(bn,{children:o.jsx(xn,{})}),o.jsx(dn,{height:p,scrollable:A,children:o.jsxs(un,{bordered:a,striped:s,compact:f,className:D,children:[o.jsx(fn,{stickyHeader:d,children:o.jsxs(pn,{children:[H&&o.jsx(Or,{width:"40px",align:"center"}),ae.map(B=>o.jsxs(Or,{sortable:B.sortable,isSorted:h===B.id,align:B.align,width:B.width,onClick:()=>B.sortable&&G(B.id),children:[B.header,B.sortable&&o.jsx(gn,{direction:h===B.id?E:void 0})]},B.id))]})}),o.jsx(mn,{children:L.length>0?L.map((B,le)=>o.jsx(Lr,{trade:B,index:le,columns:ae,isSelected:c?c(B,le):!1,hoverable:i,striped:s,expandable:H,onRowClick:u,expandedContent:j==null?void 0:j(B)},B.trade.id||le)):o.jsx("tr",{children:o.jsx("td",{colSpan:ae.length+(H?1:0),children:o.jsx(hn,{children:Y})})})})]})}),C&&ee>0&&o.jsxs(vn,{children:[o.jsxs(yn,{children:["Showing ",Math.min((w-1)*S+1,k)," to"," ",Math.min(w*S,k)," of ",k," entries"]}),o.jsxs(wn,{children:[o.jsx(oe,{size:"small",variant:"outline",onClick:()=>fe(1),disabled:w===1,children:"First"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>fe(w-1),disabled:w===1,children:"Prev"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>fe(w+1),disabled:w===ee,children:"Next"}),o.jsx(oe,{size:"small",variant:"outline",onClick:()=>fe(ee),disabled:w===ee,children:"Last"})]})]})]})]})},En=t.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Cn=({title:e,children:r,isLoading:a=!1,hasError:s=!1,errorMessage:i="An error occurred while loading data",showRetry:f=!0,onRetry:d,isEmpty:p=!1,emptyMessage:u="No data available",emptyActionText:c,onEmptyAction:l,actionButton:h,className:E,...C})=>{const w=o.jsx(En,{children:h});let S;return a?S=o.jsx(hr,{variant:"card",text:"Loading data..."}):s?S=o.jsx(Fe,{title:"Error",description:i,variant:"compact",actionText:f?"Retry":void 0,onAction:f?d:void 0}):p?S=o.jsx(Fe,{title:"No Data",description:u,variant:"compact",actionText:c,onAction:l}):S=r,o.jsx(xr,{title:e,actions:w,className:E,...C,children:S})},In=t.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),Tn=t.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),jn=t.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),_n=t.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),kn=({header:e,sidebar:r,children:a,sidebarCollapsed:s=!1,className:i})=>o.jsxs(In,{sidebarCollapsed:s,className:i,children:[o.jsx(Tn,{children:e}),o.jsx(jn,{collapsed:s,children:r}),o.jsx(_n,{children:a})]});function Nn(e,r={}){const{fetchOnMount:a=!0,dependencies:s=[]}=r,[i,f]=T.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),d=T.useCallback(async(...p)=>{f(u=>({...u,isLoading:!0,error:null}));try{const u=await e(...p);return f({data:u,isLoading:!1,error:null,isInitialized:!0}),u}catch(u){const c=u instanceof Error?u:new Error(String(u));throw f(l=>({...l,isLoading:!1,error:c,isInitialized:!0})),c}},[e]);return T.useEffect(()=>{a&&d()},[a,d,...s]),{...i,fetchData:d,refetch:()=>d()}}function Ln(e,r){const[a,s]=T.useState(e);return T.useEffect(()=>{const i=setTimeout(()=>{s(e)},r);return()=>{clearTimeout(i)}},[e,r]),a}function Pn(e={}){const{componentName:r,logToConsole:a=!0,reportToMonitoring:s=!0,onError:i}=e,[f,d]=T.useState(null),[p,u]=T.useState(!1),c=T.useCallback(E=>{if(d(E),u(!0),a){const C=r?`[${r}]`:"";console.error(`Error caught by useErrorHandler${C}:`,E)}i&&i(E)},[r,a,s,i]),l=T.useCallback(()=>{d(null),u(!1)},[]),h=T.useCallback(async E=>{try{return await E()}catch(C){c(C);return}},[c]);return T.useEffect(()=>()=>{d(null),u(!1)},[]),{error:f,hasError:p,handleError:c,resetError:l,tryExecute:h}}function Ue(e,r){const a=()=>{if(typeof window>"u")return r;try{const d=window.localStorage.getItem(e);return d?JSON.parse(d):r}catch(d){return console.warn(`Error reading localStorage key "${e}":`,d),r}},[s,i]=T.useState(a),f=d=>{try{const p=d instanceof Function?d(s):d;i(p),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(p))}catch(p){console.warn(`Error setting localStorage key "${e}":`,p)}};return T.useEffect(()=>{const d=p=>{p.key===e&&p.newValue&&i(JSON.parse(p.newValue))};return window.addEventListener("storage",d),()=>window.removeEventListener("storage",d)},[e]),[s,f]}function Dn(e){const{totalItems:r,itemsPerPage:a=10,initialPage:s=1,persistKey:i}=e,[f,d]=i?Ue(`${i}_page`,s):T.useState(s),[p,u]=i?Ue(`${i}_itemsPerPage`,a):T.useState(a),c=T.useMemo(()=>Math.max(1,Math.ceil(r/p)),[r,p]),l=T.useMemo(()=>Math.min(Math.max(1,f),c),[f,c]);l!==f&&d(l);const h=(l-1)*p,E=Math.min(h+p-1,r-1),C=l>1,w=l<c,S=T.useMemo(()=>{const A=[];if(c<=5)for(let W=1;W<=c;W++)A.push(W);else{let W=Math.max(1,l-Math.floor(2.5));const $=Math.min(c,W+5-1);$===c&&(W=Math.max(1,$-5+1));for(let F=W;F<=$;F++)A.push(F)}return A},[l,c]),k=T.useCallback(()=>{w&&d(l+1)},[w,l,d]),x=T.useCallback(()=>{C&&d(l-1)},[C,l,d]),I=T.useCallback(Y=>{const A=Math.min(Math.max(1,Y),c);d(A)},[c,d]),D=T.useCallback(Y=>{u(Y),d(1)},[u,d]);return{currentPage:l,itemsPerPage:p,totalPages:c,hasPreviousPage:C,hasNextPage:w,startIndex:h,endIndex:E,pageRange:S,nextPage:k,previousPage:x,goToPage:I,setItemsPerPage:D}}const y={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#1e5bc6",f1BlueDark:"#1a4da8",f1BlueLight:"#4a7dd8",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},O={background:"#1a1f2c",surface:"#252a37",cardBackground:"#252a37",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:y.green,warning:y.yellow,error:y.red,info:y.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:y.f1Red,profit:y.green,loss:y.red,neutral:y.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},V={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:y.green,warning:y.yellow,error:y.red,info:y.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:y.f1Red,profit:y.green,loss:y.red,neutral:y.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},Ce={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},Ie={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},Te={light:300,regular:400,medium:500,semibold:600,bold:700},je={tight:1.25,normal:1.5,relaxed:1.75},_e={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},ke={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},Ne={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},Le={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},Pe={fast:"0.1s",normal:"0.3s",slow:"0.5s"},De={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},He={name:"f1",colors:{primary:y.f1Red,primaryDark:y.f1RedDark,primaryLight:y.f1RedLight,secondary:y.f1Blue,secondaryDark:y.f1BlueDark,secondaryLight:y.f1BlueLight,accent:y.purple,accentDark:y.purpleDark,accentLight:y.purpleLight,success:O.success,warning:O.warning,error:O.error,info:O.info,background:O.background,surface:O.surface,cardBackground:O.surface,border:O.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:O.textPrimary,textSecondary:O.textSecondary,textDisabled:O.textDisabled,textInverse:O.textInverse,chartGrid:O.chartGrid,chartLine:O.chartLine,chartAxis:y.gray400,chartTooltip:O.tooltipBackground,profit:O.profit,loss:O.loss,neutral:O.neutral,tabActive:y.f1Red,tabInactive:y.gray600,tooltipBackground:O.tooltipBackground,modalBackground:O.modalBackground,sidebarBackground:y.gray800,headerBackground:"rgba(0, 0, 0, 0.2)"},spacing:Ce,breakpoints:ke,fontSizes:Ie,fontWeights:Te,lineHeights:je,fontFamilies:_e,borderRadius:Ne,shadows:Le,transitions:Pe,zIndex:De},Ar={name:"light",colors:{primary:y.f1Red,primaryDark:y.f1RedDark,primaryLight:y.f1RedLight,secondary:y.f1Blue,secondaryDark:y.f1BlueDark,secondaryLight:y.f1BlueLight,accent:y.purple,accentDark:y.purpleDark,accentLight:y.purpleLight,success:V.success,warning:V.warning,error:V.error,info:V.info,background:V.background,surface:V.surface,cardBackground:V.surface,border:V.border,divider:y.blackTransparent10,textPrimary:V.textPrimary,textSecondary:V.textSecondary,textDisabled:V.textDisabled,textInverse:V.textInverse,chartGrid:V.chartGrid,chartLine:V.chartLine,chartAxis:y.gray600,chartTooltip:V.tooltipBackground,profit:V.profit,loss:V.loss,neutral:V.neutral,tabActive:y.f1Red,tabInactive:y.gray400,tooltipBackground:V.tooltipBackground,modalBackground:V.modalBackground,sidebarBackground:y.white,headerBackground:"rgba(0, 0, 0, 0.05)"},spacing:Ce,breakpoints:ke,fontSizes:Ie,fontWeights:Te,lineHeights:je,fontFamilies:_e,borderRadius:Ne,shadows:Le,transitions:Pe,zIndex:De},$r={name:"dark",colors:{primary:y.f1Blue,primaryDark:y.f1BlueDark,primaryLight:y.f1BlueLight,secondary:y.f1Blue,secondaryDark:y.f1BlueDark,secondaryLight:y.f1BlueLight,accent:y.purple,accentDark:y.purpleDark,accentLight:y.purpleLight,success:O.success,warning:O.warning,error:O.error,info:O.info,background:y.gray900,surface:y.gray800,cardBackground:y.gray800,border:y.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:y.white,textSecondary:y.gray300,textDisabled:y.gray500,textInverse:y.gray900,chartGrid:O.chartGrid,chartLine:y.f1Blue,chartAxis:y.gray400,chartTooltip:O.tooltipBackground,profit:O.profit,loss:O.loss,neutral:O.neutral,tabActive:y.f1Blue,tabInactive:y.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:y.gray900,headerBackground:"rgba(0, 0, 0, 0.3)"},spacing:Ce,breakpoints:ke,fontSizes:Ie,fontWeights:Te,lineHeights:je,fontFamilies:_e,borderRadius:Ne,shadows:Le,transitions:Pe,zIndex:De},Rn=t.createGlobalStyle(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),Mn={f1:He,light:Ar,dark:$r},Ve=He,Ge=e=>Mn[e]||Ve,Qe=T.createContext({theme:Ve,setTheme:()=>{}}),On=()=>T.useContext(Qe),An=({initialTheme:e=Ve,persistTheme:r=!0,storageKey:a="adhd-dashboard-theme",children:s})=>{const[i,f]=T.useState(()=>{if(r&&typeof window<"u"){const c=window.localStorage.getItem(a);if(c)try{const l=Ge(c);return l||JSON.parse(c)}catch(l){console.error("Failed to parse stored theme:",l)}}return typeof e=="string"?Ge(e):e}),d=u=>{const c=typeof u=="string"?Ge(u):u;f(c),r&&typeof window<"u"&&window.localStorage.setItem(a,c.name||JSON.stringify(c))},p=({children:u})=>o.jsxs(t.ThemeProvider,{theme:i,children:[o.jsx(Rn,{}),u]});return o.jsx(Qe.Provider,{value:{theme:i,setTheme:d},children:o.jsx(p,{children:s})})};function $n(e,r,a="StoreContext"){const s=T.createContext(void 0);s.displayName=a;const i=({children:c,initialState:l})=>{const[h,E]=T.useReducer(e,l||r),C=T.useMemo(()=>({state:h,dispatch:E}),[h]);return o.jsx(s.Provider,{value:C,children:c})};function f(){const c=T.useContext(s);if(c===void 0)throw new Error(`use${a} must be used within a ${a}Provider`);return c}function d(c){const{state:l}=f();return c(l)}function p(c){const{dispatch:l}=f();return T.useMemo(()=>(...h)=>{l(c(...h))},[l,c])}function u(c){const{dispatch:l}=f();return T.useMemo(()=>{const h={};for(const E in c)h[E]=(...C)=>{l(c[E](...C))};return h},[l,c])}return{Context:s,Provider:i,useStore:f,useSelector:d,useAction:p,useActions:u}}function Fn(...e){const r=e.pop(),a=e;let s=null,i=null;return f=>{const d=a.map(p=>p(f));return(s===null||d.length!==s.length||d.some((p,u)=>p!==s[u]))&&(i=r(...d),s=d),i}}function Bn(e,r){const{key:a,initialState:s,version:i=1,migrate:f,serialize:d=JSON.stringify,deserialize:p=JSON.parse,filter:u=x=>x,merge:c=(x,I)=>({...I,...x}),debug:l=!1}=r,h=()=>{try{const x=localStorage.getItem(a);if(x===null)return null;const{state:I,version:D}=p(x);return D!==i&&f?(l&&console.log(`Migrating state from version ${D} to ${i}`),f(I,D)):I}catch(x){return l&&console.error("Error loading state from local storage:",x),null}},E=x=>{try{const I=u(x),D=d({state:I,version:i});localStorage.setItem(a,D)}catch(I){l&&console.error("Error saving state to local storage:",I)}},C=()=>{try{localStorage.removeItem(a)}catch(x){l&&console.error("Error clearing state from local storage:",x)}},w=h(),S=w?c(w,s):s;return l&&w&&(console.log("Loaded persisted state:",w),console.log("Merged initial state:",S)),{reducer:(x,I)=>{const D=e(x,I);return E(D),D},initialState:S,clear:C}}function zn(e,r="$"){return`${r}${e.toFixed(2)}`}function qn(e,r=1){return`${(e*100).toFixed(r)}%`}function Yn(e,r="short"){const a=typeof e=="string"?new Date(e):e;switch(r){case"medium":return a.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return a.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function Wn(e,r=50){return e.length<=r?e:`${e.substring(0,r-3)}...`}function Un(){return Math.random().toString(36).substring(2,9)}function Hn(e,r){let a=null;return function(...s){const i=()=>{a=null,e(...s)};a&&clearTimeout(a),a=setTimeout(i,r)}}function Vn(e,r){let a=!1;return function(...s){a||(e(...s),a=!0,setTimeout(()=>{a=!1},r))}}function Gn(e={}){console.log("Monitoring service initialized",e)}function Qn(e,r){console.error("Error captured by monitoring service:",e,r)}function Jn(e){console.log("User set for monitoring service:",e)}function Kn(e,r){const a=performance.now();return{name:e,startTime:a,finish:()=>{const i=performance.now()-a;console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`,r)}}}m.AppErrorBoundary=vt,m.Badge=xe,m.Button=oe,m.Card=xr,m.DashboardTemplate=kn,m.DataCard=Cn,m.EmptyState=Fe,m.ErrorBoundary=Cr,m.FeatureErrorBoundary=yt,m.FormField=Ct,m.Input=ue,m.LoadingPlaceholder=hr,m.Modal=Rt,m.OrderSide=ir,m.OrderStatus=cr,m.OrderType=ar,m.Select=pe,m.StatusIndicator=qo,m.TRADE_COLUMN_IDS=v,m.Table=Kt,m.Tag=Vo,m.ThemeContext=Qe,m.ThemeProvider=An,m.TimeInForce=lr,m.TradeDirection=nr,m.TradeStatus=sr,m.TradeTable=Sn,m.TradeTableFilters=Mr,m.TradeTableRow=Lr,m.UnifiedErrorBoundary=Be,m.baseColors=y,m.borderRadius=Ne,m.breakpoints=ke,m.captureError=Qn,m.createSelector=Fn,m.createStoreContext=$n,m.darkModeColors=O,m.darkTheme=$r,m.debounce=Hn,m.f1Theme=He,m.fontFamilies=_e,m.fontSizes=Ie,m.fontWeights=Te,m.formatCurrency=zn,m.formatDate=Yn,m.formatPercentage=qn,m.formatTime=We,m.generateId=Un,m.getCompactTradeTableColumns=_r,m.getPerformanceTradeTableColumns=kr,m.getTradeTableColumns=jr,m.initMonitoring=Gn,m.lightModeColors=V,m.lightTheme=Ar,m.lineHeights=je,m.persistState=Bn,m.setUser=Jn,m.shadows=Le,m.spacing=Ce,m.startTransaction=Kn,m.throttle=Vn,m.tradeStorage=to,m.tradeStorageService=oo,m.transitions=Pe,m.truncateText=Wn,m.useAsyncData=Nn,m.useDebounce=Ln,m.useErrorHandler=Pn,m.useLocalStorage=Ue,m.usePagination=Dn,m.useTheme=On,m.zIndex=De,Object.defineProperty(m,Symbol.toStringTag,{value:"Module"})});
