{"name": "@adhd-trading-dashboard/shared", "version": "1.0.0", "description": "Shared components and utilities for ADHD Trading Dashboard", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"build": "tsc --emitDeclarationOnly --skipLibCheck && vite build", "build:dev": "tsc --emitDeclarationOnly --skipLibCheck && vite build --mode development", "build:prod": "tsc --emitDeclarationOnly --skipLibCheck && vite build --mode production", "dev": "tsc --emitDeclarationOnly --skipLibCheck --watch & vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@anthropic-ai/sdk": "0.52.0", "@babel/parser": "7.27.2", "@babel/traverse": "7.27.1"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"canvas": "3.1.0", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "4.9.4"}, "keywords": ["react", "components", "ui", "design-system", "trading", "dashboard"], "author": "", "license": "MIT"}