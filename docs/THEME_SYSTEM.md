# Theme System

> **🎨 Complete design system and theming guide for the ADHD Trading Dashboard**

The ADHD Trading Dashboard features a Formula 1-inspired design system that emphasizes performance, precision, and visual clarity. This guide covers all aspects of the theme system, from design tokens to component styling.

## 🏎️ Design Philosophy

### Formula 1 Inspiration

The theme draws inspiration from Formula 1 racing, emphasizing:

- **Speed** - Fast, responsive interactions
- **Precision** - Exact measurements and clean layouts
- **Performance** - Optimized for high-frequency data display
- **Technology** - Modern, technical aesthetic
- **Focus** - Minimal distractions, maximum clarity

### Design Principles

1. **Consistency** - Unified visual language across all components
2. **Accessibility** - WCAG 2.1 AA compliance for all users
3. **Scalability** - Design tokens that work at any scale
4. **Maintainability** - Centralized theme management
5. **Performance** - Optimized for fast rendering

## 🎨 Color System

### Primary Palette

The core colors inspired by Formula 1 racing:

```typescript
const baseColors = {
  // F1 Red - Primary brand color
  primary: '#e10600',
  
  // Racing Black - Deep, technical black
  black: '#000000',
  
  // Pure White - Clean, high contrast
  white: '#ffffff',
  
  // Carbon Fiber - Dark gray with technical feel
  carbon: '#1a1f2c',
  
  // Silver - Metallic accent
  silver: '#c0c0c0'
};
```

### Semantic Colors

Colors with specific meanings and use cases:

```typescript
const semanticColors = {
  // Success states (profit, wins)
  success: '#4caf50',
  successLight: '#81c784',
  successDark: '#388e3c',
  
  // Danger states (loss, errors)
  danger: '#f44336',
  dangerLight: '#e57373',
  dangerDark: '#d32f2f',
  
  // Warning states (alerts, cautions)
  warning: '#ff9800',
  warningLight: '#ffb74d',
  warningDark: '#f57c00',
  
  // Info states (neutral information)
  info: '#2196f3',
  infoLight: '#64b5f6',
  infoDark: '#1976d2',
  
  // Neutral states (breakeven, inactive)
  neutral: '#9e9e9e',
  neutralLight: '#bdbdbd',
  neutralDark: '#616161'
};
```

### Theme Variants

#### F1 Theme (Default)

The primary dark theme with F1 racing aesthetics:

```typescript
const f1Theme = {
  colors: {
    primary: '#e10600',
    background: '#1a1f2c',
    surface: '#252a37',
    text: '#ffffff',
    textSecondary: '#aaaaaa',
    border: '#333333',
    
    // Interactive states
    hover: '#e10600',
    active: '#b30500',
    focus: '#e10600',
    disabled: '#555555',
    
    // Chart colors
    chartGrid: '#333333',
    chartLine: '#e10600',
    chartArea: 'rgba(225, 6, 0, 0.1)',
    
    // Status colors
    profit: '#4caf50',
    loss: '#f44336',
    breakeven: '#9e9e9e'
  }
};
```

#### Light Theme

Alternative light theme for different preferences:

```typescript
const lightTheme = {
  colors: {
    primary: '#e10600',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#333333',
    textSecondary: '#666666',
    border: '#e0e0e0',
    
    // Interactive states
    hover: '#e10600',
    active: '#b30500',
    focus: '#e10600',
    disabled: '#cccccc'
  }
};
```

## 📏 Spacing System

### Spacing Scale

Consistent spacing based on 8px grid system:

```typescript
const spacing = {
  xs: '0.25rem',    // 4px  - Minimal spacing
  sm: '0.5rem',     // 8px  - Small spacing
  md: '1rem',       // 16px - Medium spacing (base)
  lg: '1.5rem',     // 24px - Large spacing
  xl: '2rem',       // 32px - Extra large spacing
  xxl: '3rem',      // 48px - Maximum spacing
  
  // Component-specific spacing
  cardPadding: '1.5rem',
  buttonPadding: '0.75rem 1.5rem',
  inputPadding: '0.75rem',
  sectionGap: '2rem'
};
```

### Usage Guidelines

- **xs (4px)** - Icon spacing, fine adjustments
- **sm (8px)** - Element spacing within components
- **md (16px)** - Default spacing between related elements
- **lg (24px)** - Spacing between component groups
- **xl (32px)** - Section spacing
- **xxl (48px)** - Page-level spacing

## 🔤 Typography System

### Font Families

Technical, readable fonts that support the F1 aesthetic:

```typescript
const fontFamilies = {
  primary: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
  mono: '"JetBrains Mono", "Fira Code", "Consolas", monospace',
  display: '"Orbitron", "Inter", sans-serif'
};
```

### Font Scale

Harmonious scale for different text hierarchies:

```typescript
const fontSizes = {
  xs: '0.75rem',    // 12px - Small labels, captions
  sm: '0.875rem',   // 14px - Body text, secondary info
  md: '1rem',       // 16px - Primary body text
  lg: '1.125rem',   // 18px - Subheadings
  xl: '1.25rem',    // 20px - Headings
  xxl: '1.5rem',    // 24px - Large headings
  xxxl: '2rem',     // 32px - Display text
  display: '3rem'   // 48px - Hero text
};

const fontWeights = {
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700
};

const lineHeights = {
  tight: 1.2,
  normal: 1.5,
  relaxed: 1.75
};
```

## 🎯 Component Styling

### Button Variants

```typescript
// Primary button - main actions
const PrimaryButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.white};
  border: none;
  padding: ${({ theme }) => theme.spacing.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.hover};
  }
`;

// Secondary button - secondary actions
const SecondaryButton = styled.button`
  background-color: transparent;
  color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.white};
  }
`;
```

### Card Components

```typescript
const Card = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.card};
`;

const CardHeader = styled.div`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
```

### Form Elements

```typescript
const Input = styled.input`
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text};
  padding: ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  
  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}20;
  }
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.textSecondary};
  }
`;
```

## 📊 Data Visualization

### Chart Colors

Consistent colors for trading data visualization:

```typescript
const chartColors = {
  // Price movements
  bullish: '#4caf50',      // Green for upward movement
  bearish: '#f44336',      // Red for downward movement
  
  // Volume and indicators
  volume: '#2196f3',       // Blue for volume
  ma50: '#ff9800',         // Orange for 50-day MA
  ma200: '#9c27b0',        // Purple for 200-day MA
  
  // Support and resistance
  support: '#4caf50',      // Green for support levels
  resistance: '#f44336',   // Red for resistance levels
  
  // Grid and axes
  gridLines: '#333333',    // Subtle grid lines
  axisLabels: '#aaaaaa'    // Muted axis labels
};
```

### Status Indicators

Visual indicators for trade outcomes:

```typescript
const StatusIndicator = styled.div<{ status: 'profit' | 'loss' | 'breakeven' }>`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  ${({ status, theme }) => {
    switch (status) {
      case 'profit':
        return `
          background-color: ${theme.colors.success}20;
          color: ${theme.colors.success};
        `;
      case 'loss':
        return `
          background-color: ${theme.colors.danger}20;
          color: ${theme.colors.danger};
        `;
      case 'breakeven':
        return `
          background-color: ${theme.colors.neutral}20;
          color: ${theme.colors.neutral};
        `;
    }
  }}
`;
```

## 🎛️ Theme Usage

### ThemeProvider Setup

```typescript
import { ThemeProvider } from 'styled-components';
import { f1Theme } from '@adhd-trading-dashboard/shared';

function App() {
  return (
    <ThemeProvider theme={f1Theme}>
      <Dashboard />
    </ThemeProvider>
  );
}
```

### Using Theme in Components

```typescript
import styled from 'styled-components';
import { useTheme } from '@adhd-trading-dashboard/shared';

// With styled-components
const StyledComponent = styled.div`
  color: ${({ theme }) => theme.colors.text};
  background: ${({ theme }) => theme.colors.surface};
  padding: ${({ theme }) => theme.spacing.lg};
`;

// With useTheme hook
const Component = () => {
  const theme = useTheme();
  
  return (
    <div style={{
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.lg
    }}>
      Content
    </div>
  );
};
```

### Theme Switching

```typescript
const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  
  const toggleTheme = () => {
    setTheme(theme.name === 'f1' ? 'light' : 'f1');
  };
  
  return (
    <Button onClick={toggleTheme}>
      Switch to {theme.name === 'f1' ? 'Light' : 'F1'} Theme
    </Button>
  );
};
```

## 🎨 Design Tokens

### Border Radius

```typescript
const borderRadius = {
  none: '0',
  sm: '0.25rem',    // 4px
  md: '0.5rem',     // 8px
  lg: '0.75rem',    // 12px
  xl: '1rem',       // 16px
  full: '9999px'    // Fully rounded
};
```

### Shadows

```typescript
const shadows = {
  none: 'none',
  sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px rgba(0, 0, 0, 0.1)',
  lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px rgba(0, 0, 0, 0.15)',
  
  // Component-specific shadows
  card: '0 4px 6px rgba(0, 0, 0, 0.1)',
  modal: '0 20px 25px rgba(0, 0, 0, 0.15)',
  dropdown: '0 10px 15px rgba(0, 0, 0, 0.1)'
};
```

### Transitions

```typescript
const transitions = {
  fast: '150ms ease-in-out',
  normal: '250ms ease-in-out',
  slow: '350ms ease-in-out',
  
  // Property-specific transitions
  color: 'color 150ms ease-in-out',
  background: 'background-color 150ms ease-in-out',
  transform: 'transform 250ms ease-in-out',
  opacity: 'opacity 150ms ease-in-out'
};
```

## 🔧 Customization

### Extending the Theme

```typescript
import { f1Theme } from '@adhd-trading-dashboard/shared';

const customTheme = {
  ...f1Theme,
  colors: {
    ...f1Theme.colors,
    // Override specific colors
    primary: '#ff6b35',
    // Add custom colors
    brand: '#1a365d'
  },
  // Add custom properties
  custom: {
    headerHeight: '64px',
    sidebarWidth: '280px'
  }
};
```

### Creating New Themes

```typescript
import { Theme } from '@adhd-trading-dashboard/shared';

const myCustomTheme: Theme = {
  name: 'custom',
  colors: {
    // Define all required colors
  },
  spacing: {
    // Define spacing scale
  },
  // ... other theme properties
};
```

---

**Related Documentation:**
- [Development Guide](./DEVELOPMENT.md) - Component development with themes
- [API Reference](./API_REFERENCE.md) - Theme API documentation
- [Architecture Guide](./ARCHITECTURE.md) - Theme system architecture
