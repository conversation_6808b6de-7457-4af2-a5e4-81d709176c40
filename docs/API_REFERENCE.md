# API Reference

> **📡 Complete API documentation for the ADHD Trading Dashboard**

This document provides comprehensive documentation for all APIs, services, and data interfaces used in the ADHD Trading Dashboard.

## 🏗️ API Architecture

### Overview

The ADHD Trading Dashboard uses a simplified data architecture:

- **Service Layer** - Business logic and data processing
- **Storage Layer** - IndexedDB for local data persistence
- **Mock Layer** - Development and testing data

### Data Flow

```
Components → TradeStorageService → IndexedDB
     ↓
Mock Data (Development)
```

## 📊 Core Data Types

### Trade Interface

The primary data structure for trading information:

```typescript
interface Trade {
  id: string | number;
  date: string; // ISO date string
  symbol: string; // Trading symbol (e.g., "AAPL")
  side: 'long' | 'short'; // Position direction
  entryPrice: number; // Entry price
  exitPrice?: number; // Exit price (if closed)
  quantity: number; // Number of shares/contracts
  result?: 'win' | 'loss' | 'breakeven'; // Trade outcome
  profit?: number; // Profit/loss amount
  profitPercent?: number; // Profit/loss percentage
  setup?: string; // Trading setup description
  notes?: string; // Additional notes
  tags?: string[]; // Trade tags
  status: 'open' | 'closed'; // Trade status
  createdAt: string; // Creation timestamp
  updatedAt: string; // Last update timestamp
}
```

### Trade Analysis Types

```typescript
interface TradeAnalysis {
  id: string;
  tradeId: string;
  patternQuality: 1 | 2 | 3 | 4 | 5; // Pattern quality rating
  executionQuality: 1 | 2 | 3 | 4 | 5; // Execution quality rating
  emotionalState: string; // Emotional state during trade
  marketConditions: string; // Market conditions
  lessonsLearned: string; // Key lessons
  improvements: string; // Areas for improvement
}

interface TradeSetup {
  id: string;
  tradeId: string;
  setupType: string; // Type of setup
  timeframe: string; // Trading timeframe
  confluence: string[]; // Confluence factors
  riskReward: number; // Risk/reward ratio
  stopLoss: number; // Stop loss level
  takeProfit: number; // Take profit level
}

interface TradeFVGDetails {
  id: string;
  tradeId: string;
  fvgPresent: boolean; // Fair Value Gap present
  fvgType?: 'bullish' | 'bearish'; // FVG type
  fvgTimeframe?: string; // FVG timeframe
  fvgFilled: boolean; // Whether FVG was filled
  dolPresent: boolean; // Draw on Liquidity present
  dolType?: 'buy' | 'sell'; // DOL type
  dolLevel?: number; // DOL price level
}
```

### Performance Metrics

```typescript
interface PerformanceMetrics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number; // Percentage
  totalProfit: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  currentStreak: number;
  longestWinStreak: number;
  longestLossStreak: number;
}

interface PerformanceDataPoint {
  date: string;
  value: number;
  cumulativeProfit: number;
  tradeCount: number;
}
```

## 🗄️ Storage Services

### TradeStorageService

The main service for managing trade data with IndexedDB.

#### Methods

**saveTradeWithDetails(tradeData: CompleteTradeData): Promise<string>**

Saves a complete trade with all related details.

```typescript
const tradeData: CompleteTradeData = {
  trade: {
    symbol: 'AAPL',
    side: 'long',
    entryPrice: 150.0,
    quantity: 100,
    // ... other trade fields
  },
  analysis: {
    patternQuality: 4,
    executionQuality: 3,
    emotionalState: 'confident',
    // ... other analysis fields
  },
  setup: {
    setupType: 'breakout',
    timeframe: '1h',
    riskReward: 2.5,
    // ... other setup fields
  },
  fvgDetails: {
    fvgPresent: true,
    fvgType: 'bullish',
    dolPresent: false,
    // ... other FVG fields
  },
};

const tradeId = await tradeStorageService.saveTradeWithDetails(tradeData);
```

**getTradeById(id: string): Promise<CompleteTradeData | null>**

Retrieves a complete trade with all related details.

```typescript
const trade = await tradeStorageService.getTradeById('trade-123');
if (trade) {
  console.log('Trade:', trade.trade);
  console.log('Analysis:', trade.analysis);
  console.log('Setup:', trade.setup);
  console.log('FVG Details:', trade.fvgDetails);
}
```

**getAllTrades(): Promise<Trade[]>**

Retrieves all trades.

```typescript
const trades = await tradeStorageService.getAllTrades();
console.log(`Found ${trades.length} trades`);
```

**filterTrades(filters: TradeFilters): Promise<Trade[]>**

Filters trades based on criteria.

```typescript
interface TradeFilters {
  symbol?: string;
  side?: 'long' | 'short';
  result?: 'win' | 'loss' | 'breakeven';
  status?: 'open' | 'closed';
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
}

const filters: TradeFilters = {
  symbol: 'AAPL',
  result: 'win',
  dateFrom: '2024-01-01',
  dateTo: '2024-12-31',
};

const filteredTrades = await tradeStorageService.filterTrades(filters);
```

**updateTrade(id: string, updates: Partial<Trade>): Promise<void>**

Updates an existing trade.

```typescript
await tradeStorageService.updateTrade('trade-123', {
  exitPrice: 155.0,
  status: 'closed',
  result: 'win',
  profit: 500.0,
});
```

**deleteTrade(id: string): Promise<void>**

Deletes a trade and all related data.

```typescript
await tradeStorageService.deleteTrade('trade-123');
```

**calculateMetrics(): Promise<PerformanceMetrics>**

Calculates performance metrics from all trades.

```typescript
const metrics = await tradeStorageService.calculateMetrics();
console.log(`Win Rate: ${metrics.winRate}%`);
console.log(`Profit Factor: ${metrics.profitFactor}`);
```

## 🎣 Custom Hooks

### Trading Data Hooks

**useTradingData()**

Hook for managing trading data with loading states.

```typescript
const { trades, loading, error, addTrade, updateTrade, deleteTrade, refreshTrades } =
  useTradingData();

// Usage in component
if (loading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;

return (
  <div>
    {trades.map((trade) => (
      <TradeCard key={trade.id} trade={trade} />
    ))}
  </div>
);
```

**usePerformanceMetrics()**

Hook for performance metrics calculation.

```typescript
const { metrics, loading, error, refreshMetrics } = usePerformanceMetrics();

// Usage
return (
  <div>
    <MetricCard title="Win Rate" value={`${metrics?.winRate}%`} />
    <MetricCard title="Profit Factor" value={metrics?.profitFactor} />
  </div>
);
```

**useDashboardData()**

Hook for dashboard overview data.

```typescript
const { data, loading, error, refresh } = useDashboardData();

// Returns combined dashboard data
interface DashboardData {
  recentTrades: Trade[];
  performanceMetrics: PerformanceMetrics;
  performanceChart: PerformanceDataPoint[];
  newsEvents: NewsItem[];
}
```

### Utility Hooks

**useLocalStorage<T>(key: string, initialValue: T)**

Hook for localStorage management with TypeScript support.

```typescript
const [theme, setTheme] = useLocalStorage('theme', 'f1');
const [preferences, setPreferences] = useLocalStorage('preferences', {
  showAdvancedMetrics: false,
  defaultTimeframe: '1h',
});
```

**useDebounce<T>(value: T, delay: number)**

Hook for debouncing values.

```typescript
const [searchTerm, setSearchTerm] = useState('');
const debouncedSearchTerm = useDebounce(searchTerm, 300);

useEffect(() => {
  if (debouncedSearchTerm) {
    // Perform search
    searchTrades(debouncedSearchTerm);
  }
}, [debouncedSearchTerm]);
```

## 🎨 Theme API

### Theme Provider

**ThemeProvider**

Provides theme context to the application.

```typescript
import { ThemeProvider, f1Theme } from '@adhd-trading-dashboard/shared';

function App() {
  return (
    <ThemeProvider initialTheme={f1Theme}>
      <Dashboard />
    </ThemeProvider>
  );
}
```

**useTheme()**

Hook for accessing theme values.

```typescript
const theme = useTheme();

const StyledComponent = styled.div`
  color: ${theme.colors.text};
  background: ${theme.colors.background};
  padding: ${theme.spacing.md};
`;
```

### Theme Tokens

**Colors**

```typescript
const colors = {
  primary: '#e10600', // F1 red
  background: '#1a1f2c', // Dark background
  surface: '#252a37', // Card background
  text: '#ffffff', // Primary text
  textSecondary: '#aaaaaa', // Secondary text
  success: '#4caf50', // Green
  danger: '#f44336', // Red
  warning: '#ff9800', // Orange
  info: '#2196f3', // Blue
};
```

**Spacing**

```typescript
const spacing = {
  xs: '0.25rem', // 4px
  sm: '0.5rem', // 8px
  md: '1rem', // 16px
  lg: '1.5rem', // 24px
  xl: '2rem', // 32px
  xxl: '3rem', // 48px
};
```

## 🔧 Utility Functions

### Data Formatting

**formatCurrency(amount: number, currency?: string): string**

Formats numbers as currency.

```typescript
formatCurrency(1234.56); // "$1,234.56"
formatCurrency(1234.56, 'EUR'); // "€1,234.56"
```

**formatPercentage(value: number, decimals?: number): string**

Formats numbers as percentages.

```typescript
formatPercentage(0.1234); // "12.34%"
formatPercentage(0.1234, 1); // "12.3%"
```

**formatDate(date: string | Date, format?: string): string**

Formats dates consistently.

```typescript
formatDate('2024-01-15'); // "Jan 15, 2024"
formatDate(new Date(), 'short'); // "1/15/24"
```

### Data Validation

**validateTrade(trade: Partial<Trade>): ValidationResult**

Validates trade data.

```typescript
const result = validateTrade({
  symbol: 'AAPL',
  entryPrice: 150.0,
  quantity: 100,
});

if (!result.isValid) {
  console.log('Validation errors:', result.errors);
}
```

---

**Related Documentation:**

- [Development Guide](./DEVELOPMENT.md) - Development workflow and best practices
- [Theme System](./THEME_SYSTEM.md) - Design system and styling guide
- [Architecture Guide](./ARCHITECTURE.md) - System design and structure
