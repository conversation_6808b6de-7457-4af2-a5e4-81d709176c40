# Development Guide

> **🔧 Complete development workflow and best practices for the ADHD Trading Dashboard**

This guide covers everything you need to know about developing with the ADHD Trading Dashboard, from code organization to testing strategies and deployment workflows.

## 🏗️ Development Environment Setup

### IDE Configuration

**Recommended: Visual Studio Code**

Install these essential extensions:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "styled-components.vscode-styled-components",
    "ms-playwright.playwright",
    "vitest.explorer"
  ]
}
```

**VS Code Settings** (`.vscode/settings.json`):

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  }
}
```

### Environment Variables

Create a `.env.local` file in the dashboard package:

```bash
# packages/dashboard/.env.local
VITE_APP_NAME=ADHD Trading Dashboard
VITE_APP_VERSION=1.0.0
VITE_API_BASE_URL=http://localhost:3000/api
VITE_ENABLE_MOCK_API=true
VITE_ENABLE_STORYBOOK=true
```

## 📁 Code Organization

### Package Structure

The monorepo follows a clear separation of concerns:

```
packages/
├── shared/                 # Foundation layer
│   ├── src/
│   │   ├── components/     # Atomic design components
│   │   │   ├── atoms/      # Basic elements (Button, Input)
│   │   │   ├── molecules/  # Composite components (Card, FormField)
│   │   │   └── organisms/  # Complex components (DataTable, Chart)
│   │   ├── hooks/          # Reusable React hooks
│   │   ├── theme/          # Design system and theming
│   │   ├── utils/          # Pure utility functions
│   │   ├── types/          # TypeScript type definitions
│   │   └── services/       # Business logic services
│   └── dist/               # Built package output
└── dashboard/              # Application layer
    ├── src/
    │   ├── features/       # Feature-based organization
    │   │   ├── trade-analysis/
    │   │   ├── trade-journal/
    │   │   └── daily-guide/
    │   ├── pages/          # Route-level components
    │   ├── layouts/        # Application layouts
    │   └── routes/         # Route definitions
    └── dist/               # Built application output
```

### Feature Organization

Each feature follows a consistent structure:

```
features/feature-name/
├── components/             # Feature-specific components
│   ├── FeatureComponent.tsx
│   ├── FeatureForm.tsx
│   └── index.ts
├── hooks/                  # Feature-specific hooks
│   ├── useFeatureData.ts
│   ├── useFeatureActions.ts
│   └── index.ts
├── services/               # Feature business logic
│   ├── featureService.ts
│   └── index.ts
├── types/                  # Feature-specific types
│   ├── feature.types.ts
│   └── index.ts
├── utils/                  # Feature utilities
│   ├── featureUtils.ts
│   └── index.ts
├── __tests__/              # Feature tests
│   ├── components/
│   ├── hooks/
│   └── services/
├── FeatureName.tsx         # Main feature component
└── index.ts                # Feature exports
```

## 🎨 Component Development

### Atomic Design Principles

**Atoms** - Basic building blocks:

```tsx
// packages/shared/src/components/atoms/Button.tsx
import styled from 'styled-components';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  children: React.ReactNode;
  onClick?: () => void;
}

const StyledButton = styled.button<ButtonProps>`
  /* Theme-based styling */
  background-color: ${({ theme, variant }) =>
    variant === 'primary' ? theme.colors.primary : theme.colors.secondary};
  padding: ${({ theme, size }) => (size === 'large' ? theme.spacing.lg : theme.spacing.md)};
`;

export const Button: React.FC<ButtonProps> = ({ children, ...props }) => {
  return <StyledButton {...props}>{children}</StyledButton>;
};
```

**Molecules** - Combinations of atoms:

```tsx
// packages/shared/src/components/molecules/FormField.tsx
import { Input } from '../atoms/Input';
import { Label } from '../atoms/Label';

interface FormFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export const FormField: React.FC<FormFieldProps> = ({ label, value, onChange, error }) => {
  return (
    <div>
      <Label>{label}</Label>
      <Input value={value} onChange={(e) => onChange(e.target.value)} error={!!error} />
      {error && <span className="error">{error}</span>}
    </div>
  );
};
```

**Organisms** - Complex components:

```tsx
// packages/shared/src/components/organisms/DataTable.tsx
import { Table } from '../molecules/Table';
import { Pagination } from '../molecules/Pagination';

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  pagination?: PaginationConfig;
}

export const DataTable = <T,>({ data, columns, pagination }: DataTableProps<T>) => {
  // Complex table logic with sorting, filtering, pagination
  return (
    <div>
      <Table data={data} columns={columns} />
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
```

### Component Best Practices

1. **Use TypeScript interfaces for props**:

   ```tsx
   interface ComponentProps {
     required: string;
     optional?: number;
     children?: React.ReactNode;
   }
   ```

2. **Export components from index files**:

   ```tsx
   // components/index.ts
   export { Button } from './atoms/Button';
   export { FormField } from './molecules/FormField';
   export { DataTable } from './organisms/DataTable';
   ```

3. **Use theme tokens consistently**:
   ```tsx
   const StyledComponent = styled.div`
     color: ${({ theme }) => theme.colors.text};
     padding: ${({ theme }) => theme.spacing.md};
     font-size: ${({ theme }) => theme.fontSizes.body};
   `;
   ```

## 🪝 Custom Hooks Development

### Hook Patterns

**Data Fetching Hook**:

```tsx
// packages/shared/src/hooks/useApiQuery.ts
import { useState, useEffect } from 'react';

interface UseApiQueryResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useApiQuery<T>(endpoint: string, options?: RequestInit): UseApiQueryResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(endpoint, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [endpoint]);

  return { data, loading, error, refetch: fetchData };
}
```

**State Management Hook**:

```tsx
// packages/shared/src/hooks/useLocalStorage.ts
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}
```

## 🎨 Theme System Development

### Theme Structure

The theme system uses design tokens for consistency:

```tsx
// packages/shared/src/theme/tokens.ts
export const spacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  xxl: '3rem',
} as const;

export const colors = {
  primary: '#e10600', // F1 red
  background: '#1a1f2c', // Dark background
  surface: '#252a37', // Card background
  text: '#ffffff', // Primary text
  textSecondary: '#aaaaaa', // Secondary text
  success: '#4caf50', // Green
  danger: '#f44336', // Red
  warning: '#ff9800', // Orange
  info: '#2196f3', // Blue
} as const;
```

### Theme Usage

```tsx
// Using theme in styled-components
const StyledCard = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.card};
`;

// Using theme in components
const MyComponent = () => {
  const theme = useTheme();

  return <div style={{ color: theme.colors.text }}>Content</div>;
};
```

## 🧪 Testing Strategy

### Testing Philosophy

The ADHD Trading Dashboard follows a comprehensive testing strategy:

1. **Unit Tests** - Test individual components and functions
2. **Integration Tests** - Test feature workflows and component interactions
3. **End-to-End Tests** - Test complete user journeys
4. **Visual Tests** - Test component appearance and behavior

### Unit Testing with Vitest

**Component Testing**:

```tsx
// packages/shared/src/components/atoms/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import { Button } from '../Button';
import { f1Theme } from '../../../theme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={f1Theme}>{component}</ThemeProvider>);
};

describe('Button', () => {
  it('renders with correct text', () => {
    renderWithTheme(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = vi.fn();
    renderWithTheme(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies correct variant styling', () => {
    renderWithTheme(<Button variant="primary">Primary</Button>);
    const button = screen.getByText('Primary');
    expect(button).toHaveStyle(`background-color: ${f1Theme.colors.primary}`);
  });
});
```

**Hook Testing**:

```tsx
// packages/shared/src/hooks/__tests__/useLocalStorage.test.ts
import { renderHook, act } from '@testing-library/react';
import { useLocalStorage } from '../useLocalStorage';

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('returns initial value when no stored value exists', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));
    expect(result.current[0]).toBe('initial');
  });

  it('updates localStorage when value changes', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    act(() => {
      result.current[1]('updated');
    });

    expect(result.current[0]).toBe('updated');
    expect(localStorage.getItem('test-key')).toBe('"updated"');
  });
});
```

### Integration Testing

**Feature Testing**:

```tsx
// packages/dashboard/src/features/trade-journal/__tests__/TradeJournal.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TradeJournal } from '../TradeJournal';
import { TestProviders } from '../../../test-utils/TestProviders';

const renderTradeJournal = () => {
  return render(
    <TestProviders>
      <TradeJournal />
    </TestProviders>
  );
};

describe('TradeJournal', () => {
  it('displays trade list', async () => {
    renderTradeJournal();

    await waitFor(() => {
      expect(screen.getByText('Recent Trades')).toBeInTheDocument();
    });
  });

  it('allows adding new trade', async () => {
    renderTradeJournal();

    fireEvent.click(screen.getByText('Add Trade'));

    await waitFor(() => {
      expect(screen.getByText('New Trade')).toBeInTheDocument();
    });
  });
});
```

### End-to-End Testing with Playwright

**User Journey Tests**:

```typescript
// e2e/trade-workflows.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Trade Journal Workflow', () => {
  test('user can create and view a trade', async ({ page }) => {
    await page.goto('/');

    // Navigate to trade journal
    await page.click('[data-testid="nav-trade-journal"]');
    await expect(page).toHaveURL('/trade-journal');

    // Add new trade
    await page.click('[data-testid="add-trade-button"]');
    await page.fill('[data-testid="symbol-input"]', 'AAPL');
    await page.fill('[data-testid="entry-price-input"]', '150.00');
    await page.click('[data-testid="save-trade-button"]');

    // Verify trade appears in list
    await expect(page.locator('[data-testid="trade-list"]')).toContainText('AAPL');
  });

  test('user can filter trades by symbol', async ({ page }) => {
    await page.goto('/trade-journal');

    // Apply filter
    await page.fill('[data-testid="symbol-filter"]', 'AAPL');

    // Verify filtered results
    const tradeRows = page.locator('[data-testid="trade-row"]');
    await expect(tradeRows).toHaveCount(1);
    await expect(tradeRows.first()).toContainText('AAPL');
  });
});
```

### Visual Testing with Storybook

**Component Stories**:

```tsx
// packages/shared/src/components/atoms/Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Atoms/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const AllSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
      <Button size="small">Small</Button>
      <Button size="medium">Medium</Button>
      <Button size="large">Large</Button>
    </div>
  ),
};
```

### Test Configuration

**Vitest Configuration** (`vitest.config.ts`):

```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    include: ['packages/**/*.{test,spec}.{ts,tsx}'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['**/node_modules/**', '**/dist/**'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
});
```

**Test Utilities**:

```tsx
// packages/dashboard/src/test-utils/TestProviders.tsx
import React from 'react';
import { ThemeProvider } from 'styled-components';
import { BrowserRouter } from 'react-router-dom';
import { f1Theme } from '@adhd-trading-dashboard/shared';

interface TestProvidersProps {
  children: React.ReactNode;
}

export const TestProviders: React.FC<TestProvidersProps> = ({ children }) => {
  return (
    <BrowserRouter>
      <ThemeProvider theme={f1Theme}>{children}</ThemeProvider>
    </BrowserRouter>
  );
};
```

## 🔧 Code Quality Tools

### ESLint Configuration

**Root ESLint Config** (`.eslintrc.js`):

```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ['react', '@typescript-eslint', 'jsx-a11y'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'jsx-a11y/anchor-is-valid': 'off',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};
```

### Prettier Configuration

**Prettier Config** (`.prettierrc`):

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "always",
  "endOfLine": "lf"
}
```

### TypeScript Configuration

**Root TypeScript Config** (`tsconfig.json`):

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@adhd-trading-dashboard/shared": ["./packages/shared/src"],
      "@adhd-trading-dashboard/shared/*": ["./packages/shared/src/*"]
    }
  },
  "include": ["packages/*/src"],
  "references": [{ "path": "./packages/shared" }, { "path": "./packages/dashboard" }]
}
```

## 🔄 Git Workflow

### Branching Strategy

We follow a **Feature Branch Workflow** with these conventions:

**Branch Naming**:

- `feature/feature-name` - New features
- `fix/bug-description` - Bug fixes
- `docs/documentation-update` - Documentation changes
- `refactor/component-name` - Code refactoring
- `test/test-description` - Test improvements

**Example**:

```bash
# Create feature branch
git checkout -b feature/trade-analysis-charts

# Work on feature
git add .
git commit -m "feat: add performance chart component"

# Push and create PR
git push origin feature/trade-analysis-charts
```

### Commit Message Convention

We use **Conventional Commits** for clear commit history:

**Format**: `type(scope): description`

**Types**:

- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes (formatting, etc.)
- `refactor` - Code refactoring
- `test` - Adding or updating tests
- `chore` - Maintenance tasks

**Examples**:

```bash
feat(trade-journal): add trade filtering functionality
fix(button): resolve styling issue in dark theme
docs(api): update API reference documentation
test(hooks): add tests for useLocalStorage hook
refactor(components): extract common button styles
```

### Pull Request Process

1. **Create Feature Branch**:

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes and Test**:

   ```bash
   # Make your changes
   yarn test
   yarn type-check
   yarn lint
   ```

3. **Commit Changes**:

   ```bash
   git add .
   git commit -m "feat(scope): your feature description"
   ```

4. **Push and Create PR**:

   ```bash
   git push origin feature/your-feature-name
   # Create PR through GitHub interface
   ```

5. **PR Requirements**:
   - All tests pass
   - Code review approved
   - No TypeScript errors
   - Documentation updated if needed

## 🚀 Deployment Workflow

### Development Deployment

**Local Development**:

```bash
# Start development server
yarn start

# Build for development
yarn build:dev
```

**Staging Deployment**:

```bash
# Build production bundle
yarn build

# Deploy to staging
yarn deploy:staging
```

### Production Deployment

**Static Site Deployment**:

```bash
# Build production bundle
yarn build

# Deploy to GitHub Pages
yarn deploy:gh-pages

# Deploy to Netlify/Vercel
yarn build
# Upload packages/dashboard/dist/ to hosting platform

# Deploy to custom server
yarn build && rsync -av packages/dashboard/dist/ user@server:/path/
```

## 🐛 Debugging Techniques

### React DevTools

Install and use React DevTools for debugging:

```bash
# Install globally
yarn global add react-devtools

# Start React DevTools
react-devtools
```

**Usage**:

- Inspect component props and state
- Profile component performance
- Debug context values
- Trace component updates

### Browser DevTools

**Console Debugging**:

```tsx
// Add debug logs
console.log('Component props:', props);
console.table(data);
console.group('Trade Analysis');
console.log('Trades:', trades);
console.groupEnd();
```

**Storage Debugging**:

- Monitor IndexedDB operations in Application tab
- Check local storage and session storage
- Verify data persistence and retrieval
- Debug storage quota issues

### TypeScript Debugging

**Type Checking**:

```bash
# Check types in watch mode
yarn type-check:watch

# Verbose type checking
yarn type-check --verbose
```

**Common Type Issues**:

```tsx
// Use type assertions carefully
const element = document.getElementById('root') as HTMLElement;

// Prefer type guards
function isString(value: unknown): value is string {
  return typeof value === 'string';
}

// Use proper typing for event handlers
const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
  // Handle click
};
```

## 📋 Contributing Guidelines

### Code Standards

1. **TypeScript First**:

   - Use TypeScript for all new code
   - Define proper interfaces and types
   - Avoid `any` type unless absolutely necessary

2. **Component Guidelines**:

   - Use functional components with hooks
   - Follow atomic design principles
   - Export components from index files

3. **Styling Guidelines**:

   - Use styled-components with theme tokens
   - Follow BEM naming for CSS classes when needed
   - Ensure responsive design

4. **Testing Requirements**:
   - Write tests for new components and functions
   - Maintain test coverage above 80%
   - Include integration tests for features

### Documentation Standards

1. **Code Documentation**:

   ```tsx
   /**
    * Button component with multiple variants and sizes
    *
    * @param variant - Button style variant
    * @param size - Button size
    * @param children - Button content
    * @param onClick - Click handler
    */
   export const Button: React.FC<ButtonProps> = ({ ... }) => {
     // Implementation
   };
   ```

2. **README Updates**:

   - Update package READMEs for new features
   - Include usage examples
   - Document breaking changes

3. **Storybook Documentation**:
   - Create stories for new components
   - Include all component variants
   - Add interactive controls

### Performance Guidelines

1. **Bundle Size**:

   - Keep bundle sizes optimized
   - Use dynamic imports for large features
   - Monitor bundle analysis reports

2. **Runtime Performance**:

   - Use React.memo for expensive components
   - Implement proper key props for lists
   - Avoid unnecessary re-renders

3. **Loading Performance**:
   - Implement loading states
   - Use skeleton screens
   - Optimize images and assets

## 🔧 Development Tools

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "styled-components.vscode-styled-components",
    "ms-playwright.playwright",
    "vitest.explorer",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
```

### Useful Scripts

Add these to your shell profile for faster development:

```bash
# Quick commands
alias yt="yarn test"
alias yb="yarn build"
alias ys="yarn start"
alias ytc="yarn type-check"

# Project navigation
alias cdshared="cd packages/shared"
alias cddash="cd packages/dashboard"
alias cddocs="cd docs"
```

## 🏁 Ready to Develop!

You now have everything you need to contribute effectively to the ADHD Trading Dashboard. Remember:

- **Follow the architecture** - Use the established patterns
- **Test your code** - Maintain high test coverage
- **Document changes** - Keep documentation up to date
- **Ask questions** - Don't hesitate to create issues or discussions

The Formula 1-inspired development experience is designed for speed and precision, just like the dashboard itself!

---

**Next Steps**:

- [Build System Guide](./technical/BUILD.md) - Deep dive into build processes
- [API Reference](./API_REFERENCE.md) - Complete API documentation
- [Theme System](./THEME_SYSTEM.md) - Design system and styling guide
