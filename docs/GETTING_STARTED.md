# Getting Started

> **⚡ Complete setup guide for the ADHD Trading Dashboard**

Welcome to the ADHD Trading Dashboard! This guide will help you get up and running with the development environment, whether you're a new contributor or setting up the project for the first time.

## 🎯 Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software

- **Node.js v18 LTS** (recommended)

  - Download from [nodejs.org](https://nodejs.org/)
  - Verify installation: `node --version` (should show v18.x.x)
  - Why v18? Better compatibility with our dependencies and build tools

- **Yarn Package Manager** (latest stable)

  - Install globally: `npm install -g yarn`
  - Verify installation: `yarn --version`
  - Why Yarn? Optimized for monorepo workspaces and faster installs

- **Git** (for version control)
  - Download from [git-scm.com](https://git-scm.com/)
  - Verify installation: `git --version`

### Recommended Tools

- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint
  - styled-components

## 🚀 Quick Start

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/HeinekenBottle/adhd-trading-dashboard-lib.git

# Navigate to the project directory
cd adhd-trading-dashboard-lib
```

### 2. Install Dependencies

```bash
# Install all dependencies for the monorepo
yarn install

# This will install dependencies for both packages:
# - packages/shared
# - packages/dashboard
```

### 3. Build the Project

```bash
# Build all packages in dependency order
yarn build

# This builds:
# 1. shared package first (foundation)
# 2. dashboard package second (depends on shared)
```

### 4. Start Development Server

```bash
# Start the development server
yarn start

# Alternative command
yarn dev

# This will:
# - Start Vite dev server on http://localhost:3000
# - Enable hot module replacement (HMR)
# - Open your browser automatically
```

🎉 **Success!** You should now see the ADHD Trading Dashboard running at `http://localhost:3000`

## 📁 Project Structure Overview

Understanding the project structure will help you navigate the codebase:

```
📦 adhd-trading-dashboard-lib/
├── 📁 packages/
│   ├── 📁 shared/              # Foundation package
│   │   ├── src/
│   │   │   ├── components/     # UI components (atoms, molecules, organisms)
│   │   │   ├── theme/          # F1-inspired design system
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   ├── utils/          # Utility functions
│   │   │   └── types/          # TypeScript type definitions
│   │   └── package.json
│   └── 📁 dashboard/           # Main application
│       ├── src/
│       │   ├── features/       # Trading features (analysis, journal, guide)
│       │   ├── pages/          # Route-level components
│       │   ├── layouts/        # Application layouts
│       │   └── routes/         # Route definitions
│       └── package.json
├── 📁 docs/                   # 📚 Documentation hub
├── 📁 scripts/                # Build and deployment scripts
├── 📁 code-health/            # Code quality tools
└── package.json               # Root package configuration
```

## 🔧 Development Commands

Here are the essential commands you'll use during development:

### Development Server

```bash
# Start development server (dashboard)
yarn start
yarn dev

# Start with specific port
yarn start --port 3001
```

### Building

```bash
# Build all packages
yarn build

# Build individual packages
yarn build:shared
yarn build:dashboard

# Development build (faster, no optimization)
yarn build:dev

# Clean build (removes old artifacts first)
yarn build:clean
```

### Type Checking

```bash
# Check TypeScript types
yarn type-check

# Watch mode for continuous type checking
yarn type-check:watch
```

### Testing

```bash
# Run all tests
yarn test

# Watch mode for continuous testing
yarn test:watch

# Run tests with coverage report
yarn test:coverage

# Run end-to-end tests
yarn test:e2e

# Run E2E tests with UI
yarn test:e2e:ui
```

### Code Quality

```bash
# Lint code
yarn lint

# Lint and auto-fix issues
yarn lint --fix

# Code health analysis
yarn health:analyze
```

### Storybook (Component Development)

```bash
# Start Storybook development server
yarn storybook

# Build Storybook for production
yarn build-storybook
```

## 🎨 First Steps with the Dashboard

### Exploring the Interface

Once the development server is running, you'll see:

1. **Main Dashboard** - Overview of trading performance
2. **Trade Journal** - Record and review trades
3. **Trade Analysis** - Analyze patterns and performance
4. **Daily Guide** - Daily planning and market overview

### Formula 1 Theme

The dashboard uses a Formula 1-inspired design system:

- **Colors**: Red (#e10600), black, white primary palette
- **Typography**: Clean, technical fonts for data readability
- **Layout**: High-performance, racing-inspired interface
- **Animations**: Fast, precise transitions

### Sample Data

The development environment includes mock data for:

- Sample trades with various outcomes
- Performance metrics and charts
- Market news and events
- Trading setups and analysis

## 🛠️ Development Workflow

### Making Changes

1. **Choose the right package**:

   - `packages/shared/` - For reusable components, utilities, theme
   - `packages/dashboard/` - For trading features and application logic

2. **Follow the architecture**:

   - Use atomic design principles for components
   - Keep features isolated in their own directories
   - Import from shared package for consistency

3. **Test your changes**:

   ```bash
   # Run tests
   yarn test

   # Check types
   yarn type-check

   # Lint code
   yarn lint
   ```

### Hot Module Replacement

The development server supports HMR, so changes will be reflected immediately:

- **Component changes** - Instant updates without losing state
- **Style changes** - Immediate visual updates
- **Type changes** - Automatic type checking in the background

## 🔍 Troubleshooting

### Common Setup Issues

**Issue: `yarn install` fails**

```bash
# Clear yarn cache
yarn cache clean

# Remove node_modules and reinstall
rm -rf node_modules packages/*/node_modules
yarn install
```

**Issue: TypeScript errors during build**

```bash
# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo

# Rebuild with clean slate
yarn clean && yarn build
```

**Issue: Port 3000 already in use**

```bash
# Kill process using port 3000
lsof -ti:3000 | xargs kill -9

# Or start on different port
yarn start --port 3001
```

**Issue: Storybook won't start**

```bash
# Clear Storybook cache
yarn storybook --no-manager-cache

# Or rebuild Storybook
rm -rf packages/shared/.storybook-static
yarn build-storybook
```

### Getting Help

If you encounter issues:

1. **Check the logs** - Look for error messages in the terminal
2. **Verify prerequisites** - Ensure Node.js v18 and Yarn are installed
3. **Clean and rebuild** - Try `yarn clean && yarn install && yarn build`
4. **Check documentation** - Browse the [docs/](../README.md) directory
5. **Create an issue** - If problems persist, create a GitHub issue

## 📚 Next Steps

Now that you have the project running, explore these resources:

### Learn the Architecture

- **[System Architecture](./ARCHITECTURE.md)** - Understand the monorepo structure
- **[Shared Package](./packages/SHARED.md)** - Learn about reusable components
- **[Dashboard Package](./packages/DASHBOARD.md)** - Explore trading features

### Development Guides

- **[Development Workflow](./DEVELOPMENT.md)** - Best practices and workflows
- **[Build System](./technical/BUILD.md)** - Build processes and deployment
- **[API Reference](./API_REFERENCE.md)** - Complete API documentation

### Component Development

- **[Theme System](./THEME_SYSTEM.md)** - Design tokens and styling
- **Storybook** - Run `yarn storybook` to explore components
- **Testing** - Learn about our testing strategy

## 🏁 Ready to Race!

You're now ready to start developing with the ADHD Trading Dashboard! The Formula 1-inspired interface is designed for high performance, just like the development experience.

**Happy coding!** 🏎️💨

---

**Need help?** Check the [Development Guide](./DEVELOPMENT.md) or create an issue on GitHub.
