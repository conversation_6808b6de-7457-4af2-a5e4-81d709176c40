# Shared Package Documentation

> **Foundation layer for all reusable components, utilities, and theming**

## Overview

The `shared` package provides the building blocks for the entire ADHD Trading Dashboard system. It contains atomic UI components, the API abstraction layer, theming, state management utilities, and common hooks. All features and applications in the monorepo depend on this package for consistency and maintainability.

## 📦 Structure

```
src/
├── api/         # API abstraction layer (clients, hooks, types)
├── components/  # UI components (atoms, molecules, organisms)
├── hooks/       # Custom React hooks
├── state/       # State management utilities
├── theme/       # Design system and theming
├── utils/       # Pure utility functions
```

## 🧩 Components

- **Atoms**: Basic UI elements (Button, Input, Select)
- **Molecules**: Combinations of atoms (Card, FormField)
- **Organisms**: Complex UI components (DataTable, Chart)

## 🔌 API Layer

- **Types**: Type definitions for API responses
- **Clients**: API client implementations (GAS, mock)
- **Hooks**: Custom hooks for API calls

## 🎨 Theme System

- **Tokens**: Design tokens (colors, spacing, typography)
- **Variants**: Theme variants (F1, light)
- **ThemeProvider**: Context provider for theme switching

## 🪄 Usage Examples

### Components

```tsx
import { Button, Input, Card } from '@adhd-trading-dashboard/shared';

<Card title="My Card">
  <Input value={value} onChange={setValue} />
  <Button onClick={handleClick}>Submit</Button>
</Card>;
```

### API

```tsx
import { useDashboardData } from '@adhd-trading-dashboard/shared';
const { data, isLoading, error } = useDashboardData();
```

### Theme

```tsx
import { ThemeProvider, useTheme } from '@adhd-trading-dashboard/shared';
<ThemeProvider initialTheme="f1">
  <App />
</ThemeProvider>;
```

## 🏆 Best Practices

- Use only exported components/hooks from this package in features/apps
- Extend, don’t duplicate: add new atoms/molecules here for reuse
- Keep API and theme logic centralized for consistency

---

For more details, see the [Architecture Guide](../ARCHITECTURE.md) and [Theme System](../THEME_SYSTEM.md).
