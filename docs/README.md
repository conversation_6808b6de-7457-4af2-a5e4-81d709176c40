# ADHD Trading Dashboard Documentation

> **📚 Complete Documentation Hub for the ADHD Trading Dashboard**

Welcome to the centralized documentation system. All project information, guides, and references are organized here for easy navigation and maintenance.

## 🚀 Quick Navigation

### Getting Started

- **[⚡ Getting Started](./GETTING_STARTED.md)** - Setup, installation, and first steps
- **[🏗️ Architecture Overview](./ARCHITECTURE.md)** - System design and structure
- **[📋 Project Overview](./PROJECT_OVERVIEW.md)** - What this project is and does

### Development

- **[🔧 Development Guide](./DEVELOPMENT.md)** - Development workflow and tools
- **[📦 Package Documentation](./packages/)** - Individual package guides
  - [Shared Package](./packages/SHARED.md)
  - [Dashboard Package](./packages/DASHBOARD.md)

### Technical Guides

- **[🏗️ Build System](./technical/BUILD.md)** - Build processes and configuration
- **[📄 API Reference](./API_REFERENCE.md)** - Complete API documentation
- **[🎨 Theme System](./THEME_SYSTEM.md)** - Design tokens and styling guide

### Migration & History

- **[📝 Documentation Migration](./migration/DOCUMENTATION_MIGRATION.md)** - How docs were reorganized
- **[📋 Legacy Documentation](./migration/LEGACY_DOCS.md)** - Archive of old documentation

## 📁 Documentation Structure

```
docs/
├── README.md                    # This file - main navigation hub
├── PROJECT_OVERVIEW.md          # High-level project description
├── ARCHITECTURE.md              # System architecture and design
├── GETTING_STARTED.md           # Setup and installation guide
├── DEVELOPMENT.md               # Development workflow and tools
├── API_REFERENCE.md             # Complete API documentation
├── THEME_SYSTEM.md              # Design system and theming
├── packages/                    # Package-specific documentation
│   ├── SHARED.md               # Shared package documentation
│   └── DASHBOARD.md            # Dashboard package documentation
├── technical/                   # Technical implementation guides
│   └── BUILD.md                # Build system documentation
└── migration/                   # Migration and historical docs
    ├── DOCUMENTATION_MIGRATION.md
    └── LEGACY_DOCS.md
```

## 🎯 Documentation Principles

This documentation system follows these principles:

1. **Single Source of Truth** - Each topic has one authoritative location
2. **Clear Navigation** - Easy to find what you're looking for
3. **Up-to-Date** - Documentation reflects current codebase state
4. **Practical** - Focuses on actionable information
5. **Organized** - Logical structure with consistent formatting

## 🔄 Documentation Maintenance

### How to Update Documentation

1. **Find the Right File** - Use this navigation to locate the correct document
2. **Update Content** - Make changes directly in the relevant markdown file
3. **Update Links** - Ensure cross-references remain accurate
4. **Test Examples** - Verify code examples work with current codebase

### Adding New Documentation

1. Determine the appropriate location in the structure above
2. Create the new file following our [documentation standards](./DOCUMENTATION_STANDARDS.md)
3. Add navigation links to this README.md file
4. Update any related cross-references

## 📖 How to Use This Documentation

- **New to the project?** Start with [Project Overview](./PROJECT_OVERVIEW.md) then [Getting Started](./GETTING_STARTED.md)
- **Setting up development?** Go to [Development Guide](./DEVELOPMENT.md)
- **Understanding the system?** Check [Architecture](./ARCHITECTURE.md)
- **Looking for specific package info?** Browse [packages/](./packages/) directory
- **Need API details?** See [API Reference](./API_REFERENCE.md)

---

**Last Updated:** May 25, 2025  
**Maintained By:** Development Team  
**Questions?** Check existing docs first, then create an issue or PR
