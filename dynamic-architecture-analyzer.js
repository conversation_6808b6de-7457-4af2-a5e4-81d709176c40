#!/usr/bin/env node

/**
 * Dynamic Architecture Analyzer (ES Module Version)
 * Discovers and analyzes codebase structure without hardcoded assumptions
 */

import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';
import ts from 'typescript';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DynamicArchitectureAnalyzer {
  constructor(options = {}) {
    this.rootDir = options.rootDir || process.cwd();
    this.verbose = options.verbose || false;
    this.excludePatterns = options.excludePatterns || [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**',
      '**/.git/**',
    ];

    this.analysis = {
      projectStructure: {},
      components: [],
      hooks: [],
      utilities: [],
      services: [],
      types: [],
      tests: [],
      configs: [],
      dependencies: new Map(),
      metrics: {},
      recommendations: [],
    };
  }

  log(message, level = 'info') {
    if (level === 'verbose' && !this.verbose) return;
    const timestamp = new Date().toISOString();
    console.log(`[Analyzer] ${message}`);
  }

  /**
   * Main analysis entry point
   */
  async analyze() {
    this.log('🔍 Starting dynamic codebase analysis...');

    try {
      // 1. Discover project structure
      await this.discoverProjectStructure();

      // 2. Find and categorize all source files
      await this.discoverSourceFiles();

      // 3. Analyze dependencies and imports
      await this.analyzeDependencies();

      // 4. Calculate metrics
      this.calculateMetrics();

      // 5. Generate recommendations
      this.generateRecommendations();

      // 6. Output results
      await this.generateReport();

      this.log('✅ Analysis complete!');
      return this.analysis;
    } catch (error) {
      this.log(`❌ Analysis failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Discover project structure by finding package.json files
   */
  async discoverProjectStructure() {
    this.log('📁 Discovering project structure...');

    const packageJsonFiles = await glob('**/package.json', {
      cwd: this.rootDir,
      ignore: this.excludePatterns,
    });

    for (const packagePath of packageJsonFiles) {
      const fullPath = path.join(this.rootDir, packagePath);
      const packageDir = path.dirname(fullPath);

      try {
        const content = await fs.readFile(fullPath, 'utf8');
        const packageJson = JSON.parse(content);

        const projectInfo = {
          name: packageJson.name || path.basename(packageDir),
          version: packageJson.version,
          path: packageDir,
          type: this.detectProjectType(packageJson),
          dependencies: Object.keys(packageJson.dependencies || {}),
          devDependencies: Object.keys(packageJson.devDependencies || {}),
          scripts: Object.keys(packageJson.scripts || {}),
          isRoot: packageDir === this.rootDir,
          isModule: packageJson.type === 'module',
        };

        this.analysis.projectStructure[packageDir] = projectInfo;
        this.log(`📦 Found project: ${projectInfo.name} (${projectInfo.type})`, 'verbose');
      } catch (error) {
        this.log(`⚠️  Failed to parse ${packagePath}: ${error.message}`, 'verbose');
      }
    }
  }

  /**
   * Detect project type based on dependencies and structure
   */
  detectProjectType(packageJson) {
    const deps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies,
    };

    if (deps.react) return 'react';
    if (deps.vue) return 'vue';
    if (deps.angular) return 'angular';
    if (deps.svelte) return 'svelte';
    if (deps.express || deps.fastify) return 'server';
    if (packageJson.type === 'module') return 'esm-package';

    return 'unknown';
  }

  /**
   * Discover and categorize all source files
   */
  async discoverSourceFiles() {
    this.log('🔍 Discovering source files...');

    // Find all potential source files
    const sourceFiles = await glob('**/*.{ts,tsx,js,jsx,vue,svelte}', {
      cwd: this.rootDir,
      ignore: this.excludePatterns,
    });

    // Categorize each file
    for (const filePath of sourceFiles) {
      const fullPath = path.join(this.rootDir, filePath);
      const category = await this.categorizeFile(fullPath, filePath);

      if (category) {
        this.analysis[category].push({
          path: filePath,
          fullPath,
          ...(await this.analyzeFile(fullPath, filePath)),
        });
      }
    }

    // Also find config and test files
    await this.discoverConfigFiles();
    await this.discoverTestFiles();
  }

  /**
   * Categorize file based on content and patterns
   */
  async categorizeFile(fullPath, relativePath) {
    try {
      const content = await fs.readFile(fullPath, 'utf8');
      const fileName = path.basename(relativePath);
      const dirName = path.dirname(relativePath);

      // Skip test files (handle separately)
      if (this.isTestFile(relativePath, content)) {
        return null; // Will be handled in discoverTestFiles
      }

      // Categorize by content patterns
      if (this.isReactComponent(content)) {
        return 'components';
      }

      if (this.isReactHook(content, fileName)) {
        return 'hooks';
      }

      if (this.isServiceFile(content, relativePath)) {
        return 'services';
      }

      if (this.isUtilityFile(content, relativePath)) {
        return 'utilities';
      }

      if (this.isTypeDefinition(content, fileName)) {
        return 'types';
      }

      // If no specific category, still analyze as general source
      return 'utilities'; // Default category
    } catch (error) {
      this.log(`⚠️  Failed to categorize ${relativePath}: ${error.message}`, 'verbose');
      return null;
    }
  }

  /**
   * Pattern detection methods
   */
  isReactComponent(content) {
    return (
      /export\s+(default\s+)?(function|const|class)\s+\w+.*React\.FC|React\.Component|JSX\.Element/.test(
        content
      ) ||
      /return\s*\(\s*<\w+/.test(content) ||
      /return\s+<\w+/.test(content)
    );
  }

  isReactHook(content, fileName) {
    return (
      fileName.startsWith('use') &&
      fileName.match(/\.(ts|js)x?$/) &&
      /export\s+(const|function)\s+use\w+/.test(content)
    );
  }

  isServiceFile(content, filePath) {
    return (
      /api|service|client/i.test(filePath) ||
      /(fetch|axios|http)\s*\(/.test(content) ||
      /export.*api|export.*service|export.*client/i.test(content)
    );
  }

  isUtilityFile(content, filePath) {
    return (
      /utils?|helpers?|lib|common/i.test(filePath) ||
      (/export\s+(function|const)/.test(content) &&
        !/React|Component|Hook/.test(content) &&
        !/api|service/i.test(content))
    );
  }

  isTypeDefinition(content, fileName) {
    return (
      fileName.includes('.d.ts') ||
      fileName.includes('types') ||
      /export\s+(interface|type|enum)/.test(content)
    );
  }

  isTestFile(filePath, content) {
    return (
      /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(filePath) ||
      /__(tests|specs)__/.test(filePath) ||
      /(describe|it|test)\s*\(/.test(content)
    );
  }

  /**
   * Analyze individual file for detailed metrics
   */
  async analyzeFile(fullPath, relativePath) {
    try {
      const content = await fs.readFile(fullPath, 'utf8');
      const stats = await fs.stat(fullPath);

      return {
        size: stats.size,
        lines: content.split('\n').length,
        complexity: this.calculateCyclomaticComplexity(content),
        imports: this.extractImports(content),
        exports: this.extractExports(content),
        dependencies: this.extractDependencies(content),
        lastModified: stats.mtime,
      };
    } catch (error) {
      this.log(`⚠️  Failed to analyze ${relativePath}: ${error.message}`, 'verbose');
      return {};
    }
  }

  /**
   * Calculate cyclomatic complexity
   */
  calculateCyclomaticComplexity(content) {
    // Simple complexity calculation based on control flow keywords
    const complexityPatterns = [
      /\bif\s*\(/g,
      /\belse\s+if\s*\(/g,
      /\bwhile\s*\(/g,
      /\bfor\s*\(/g,
      /\bswitch\s*\(/g,
      /\bcase\s+/g,
      /\bcatch\s*\(/g,
      /\?\s*.*\s*:/g, // ternary operator
      /&&|\|\|/g, // logical operators
    ];

    let complexity = 1; // Base complexity
    for (const pattern of complexityPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  /**
   * Extract import statements (both CommonJS and ES modules)
   */
  extractImports(content) {
    const imports = [];

    // ES module imports
    const esImportRegex =
      /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g;

    // CommonJS require
    const cjsRequireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;

    let match;

    // Extract ES imports
    while ((match = esImportRegex.exec(content)) !== null) {
      imports.push({
        statement: match[0],
        path: match[1],
        type: 'esm',
        isRelative: match[1].startsWith('.'),
        isExternal: !match[1].startsWith('.') && !match[1].startsWith('/'),
      });
    }

    // Extract CommonJS requires
    while ((match = cjsRequireRegex.exec(content)) !== null) {
      imports.push({
        statement: match[0],
        path: match[1],
        type: 'cjs',
        isRelative: match[1].startsWith('.'),
        isExternal: !match[1].startsWith('.') && !match[1].startsWith('/'),
      });
    }

    return imports;
  }

  /**
   * Extract export statements (both CommonJS and ES modules)
   */
  extractExports(content) {
    const exports = [];

    // ES module exports
    const exportPatterns = [
      /export\s+(default\s+)?(function|const|class|interface|type|enum)\s+(\w+)/g,
      /export\s*\{\s*([^}]+)\s*\}/g,
      /export\s+\*\s+from\s+['"`]([^'"`]+)['"`]/g,
    ];

    // CommonJS exports
    const cjsExportRegex = /module\.exports\s*=|exports\.\w+\s*=/g;

    for (const pattern of exportPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        exports.push({
          statement: match[0],
          name: match[3] || match[1] || 'namespace',
          type: match[1] ? 'default' : 'named',
          module: 'esm',
        });
      }
    }

    // Check for CommonJS exports
    if (cjsExportRegex.test(content)) {
      exports.push({
        statement: 'module.exports',
        name: 'default',
        type: 'cjs',
        module: 'cjs',
      });
    }

    return exports;
  }

  /**
   * Extract dependencies from imports
   */
  extractDependencies(content) {
    const dependencies = new Set();
    const imports = this.extractImports(content);

    for (const imp of imports) {
      if (imp.isExternal) {
        // Extract package name (handle scoped packages)
        const packageName = imp.path.startsWith('@')
          ? imp.path.split('/').slice(0, 2).join('/')
          : imp.path.split('/')[0];
        dependencies.add(packageName);
      }
    }

    return Array.from(dependencies);
  }

  /**
   * Discover configuration files
   */
  async discoverConfigFiles() {
    this.log('⚙️  Discovering configuration files...');

    const configPatterns = [
      '**/*.config.{js,ts,json,mjs}',
      '**/.eslintrc*',
      '**/tsconfig*.json',
      '**/babel.config.*',
      '**/vite.config.*',
      '**/webpack.config.*',
      '**/jest.config.*',
      '**/vitest.config.*',
    ];

    for (const pattern of configPatterns) {
      const files = await glob(pattern, {
        cwd: this.rootDir,
        ignore: this.excludePatterns,
      });

      for (const filePath of files) {
        this.analysis.configs.push({
          path: filePath,
          type: this.detectConfigType(filePath),
          fullPath: path.join(this.rootDir, filePath),
        });
      }
    }
  }

  /**
   * Discover test files
   */
  async discoverTestFiles() {
    this.log('🧪 Discovering test files...');

    const testPatterns = [
      '**/*.{test,spec}.{ts,tsx,js,jsx}',
      '**/__tests__/**/*.{ts,tsx,js,jsx}',
      '**/__specs__/**/*.{ts,tsx,js,jsx}',
    ];

    for (const pattern of testPatterns) {
      const files = await glob(pattern, {
        cwd: this.rootDir,
        ignore: this.excludePatterns,
      });

      for (const filePath of files) {
        this.analysis.tests.push({
          path: filePath,
          fullPath: path.join(this.rootDir, filePath),
          ...(await this.analyzeFile(path.join(this.rootDir, filePath), filePath)),
        });
      }
    }
  }

  /**
   * Detect configuration file type
   */
  detectConfigType(filePath) {
    const fileName = path.basename(filePath);

    if (fileName.includes('eslint')) return 'eslint';
    if (fileName.includes('typescript') || fileName.includes('tsconfig')) return 'typescript';
    if (fileName.includes('babel')) return 'babel';
    if (fileName.includes('vite')) return 'vite';
    if (fileName.includes('webpack')) return 'webpack';
    if (fileName.includes('jest')) return 'jest';
    if (fileName.includes('vitest')) return 'vitest';
    if (fileName.includes('package.json')) return 'npm';

    return 'unknown';
  }

  /**
   * Analyze dependencies across the codebase
   */
  async analyzeDependencies() {
    this.log('🔗 Analyzing dependencies...');

    const allFiles = [
      ...this.analysis.components,
      ...this.analysis.hooks,
      ...this.analysis.utilities,
      ...this.analysis.services,
    ];

    // Build dependency graph
    for (const file of allFiles) {
      for (const imp of file.imports || []) {
        if (imp.isRelative) {
          // Track internal dependencies
          const resolvedPath = this.resolveRelativePath(file.path, imp.path);
          if (!this.analysis.dependencies.has(file.path)) {
            this.analysis.dependencies.set(file.path, []);
          }
          this.analysis.dependencies.get(file.path).push(resolvedPath);
        }
      }
    }
  }

  /**
   * Resolve relative import paths
   */
  resolveRelativePath(fromPath, importPath) {
    const fromDir = path.dirname(fromPath);
    return path.normalize(path.join(fromDir, importPath));
  }

  /**
   * Calculate various metrics
   */
  calculateMetrics() {
    this.log('📊 Calculating metrics...');

    const allFiles = [
      ...this.analysis.components,
      ...this.analysis.hooks,
      ...this.analysis.utilities,
      ...this.analysis.services,
    ];

    this.analysis.metrics = {
      totalFiles: allFiles.length,
      totalLines: allFiles.reduce((sum, f) => sum + (f.lines || 0), 0),
      averageComplexity:
        allFiles.reduce((sum, f) => sum + (f.complexity || 0), 0) / allFiles.length || 0,
      largestFiles: allFiles.sort((a, b) => (b.lines || 0) - (a.lines || 0)).slice(0, 10),
      mostComplexFiles: allFiles
        .sort((a, b) => (b.complexity || 0) - (a.complexity || 0))
        .slice(0, 10),
      testCoverage: this.calculateTestCoverage(),
      moduleTypes: this.analyzeModuleTypes(),
      categoryBreakdown: {
        components: this.analysis.components.length,
        hooks: this.analysis.hooks.length,
        utilities: this.analysis.utilities.length,
        services: this.analysis.services.length,
        tests: this.analysis.tests.length,
      },
    };
  }

  /**
   * Analyze module types (ESM vs CommonJS)
   */
  analyzeModuleTypes() {
    const allFiles = [
      ...this.analysis.components,
      ...this.analysis.hooks,
      ...this.analysis.utilities,
      ...this.analysis.services,
    ];

    const moduleTypes = {
      esm: 0,
      cjs: 0,
      mixed: 0,
    };

    for (const file of allFiles) {
      const imports = file.imports || [];
      const hasESM = imports.some((imp) => imp.type === 'esm');
      const hasCJS = imports.some((imp) => imp.type === 'cjs');

      if (hasESM && hasCJS) {
        moduleTypes.mixed++;
      } else if (hasESM) {
        moduleTypes.esm++;
      } else if (hasCJS) {
        moduleTypes.cjs++;
      }
    }

    return moduleTypes;
  }

  /**
   * Calculate test coverage estimate
   */
  calculateTestCoverage() {
    const sourceFiles =
      this.analysis.components.length +
      this.analysis.hooks.length +
      this.analysis.utilities.length +
      this.analysis.services.length;

    const testFiles = this.analysis.tests.length;

    return sourceFiles > 0 ? Math.round((testFiles / sourceFiles) * 100) : 0;
  }

  /**
   * Generate architectural recommendations
   */
  generateRecommendations() {
    this.log('💡 Generating recommendations...');

    const recommendations = [];
    const { metrics } = this.analysis;

    // Check for large files
    if (metrics.largestFiles.some((f) => f.lines > 300)) {
      recommendations.push({
        type: 'refactoring',
        priority: 'high',
        title: 'Split large files',
        description: 'Several files exceed 300 lines and should be split into smaller modules',
        files: metrics.largestFiles.filter((f) => f.lines > 300).map((f) => f.path),
      });
    }

    // Check for high complexity
    if (metrics.averageComplexity > 15) {
      recommendations.push({
        type: 'refactoring',
        priority: 'medium',
        title: 'Reduce complexity',
        description:
          'Average cyclomatic complexity is high. Consider breaking down complex functions.',
        files: metrics.mostComplexFiles.filter((f) => f.complexity > 20).map((f) => f.path),
      });
    }

    // Check test coverage
    if (metrics.testCoverage < 50) {
      recommendations.push({
        type: 'testing',
        priority: 'high',
        title: 'Increase test coverage',
        description: `Test coverage is ${metrics.testCoverage}%. Aim for at least 70%.`,
      });
    }

    // Check for module type consistency
    if (metrics.moduleTypes.mixed > 0) {
      recommendations.push({
        type: 'architecture',
        priority: 'medium',
        title: 'Standardize module types',
        description: `${metrics.moduleTypes.mixed} files use mixed ESM/CommonJS. Consider standardizing to ESM.`,
      });
    }

    // Check for proper folder structure
    if (this.analysis.components.length > 20 && !this.hasProperFolderStructure()) {
      recommendations.push({
        type: 'architecture',
        priority: 'medium',
        title: 'Organize component structure',
        description:
          'Consider organizing components into feature-based folders for better maintainability',
      });
    }

    this.analysis.recommendations = recommendations;
  }

  /**
   * Check if proper folder structure exists
   */
  hasProperFolderStructure() {
    const componentPaths = this.analysis.components.map((c) => c.path);
    const hasFeatureFolders = componentPaths.some((p) => p.includes('/features/'));
    const hasUIFolders = componentPaths.some(
      (p) => p.includes('/ui/') || p.includes('/components/ui/')
    );

    return hasFeatureFolders || hasUIFolders;
  }

  /**
   * Generate comprehensive report
   */
  async generateReport() {
    const reportPath = path.join(this.rootDir, 'architecture-analysis.json');
    const readableReportPath = path.join(this.rootDir, 'architecture-analysis.md');

    // Generate JSON report
    await fs.writeFile(reportPath, JSON.stringify(this.analysis, null, 2));

    // Generate readable Markdown report
    const markdownReport = this.generateMarkdownReport();
    await fs.writeFile(readableReportPath, markdownReport);

    this.log(`📋 Generated reports:`);
    this.log(`  - JSON: ${reportPath}`);
    this.log(`  - Markdown: ${readableReportPath}`);
  }

  /**
   * Generate markdown report
   */
  generateMarkdownReport() {
    const { metrics, recommendations, projectStructure } = this.analysis;

    return `# Architecture Analysis Report

Generated: ${new Date().toISOString()}

## Project Overview

${Object.entries(projectStructure)
  .map(
    ([path, info]) => `
### ${info.name}
- **Type**: ${info.type}
- **Module Type**: ${info.isModule ? 'ES Module' : 'CommonJS'}
- **Path**: ${path}
- **Version**: ${info.version || 'N/A'}
- **Dependencies**: ${info.dependencies.length}
- **Scripts**: ${info.scripts.join(', ')}
`
  )
  .join('')}

## Metrics Summary

| Metric | Value |
|--------|-------|
| Total Files | ${metrics.totalFiles} |
| Total Lines | ${metrics.totalLines.toLocaleString()} |
| Average Complexity | ${metrics.averageComplexity.toFixed(2)} |
| Test Coverage | ${metrics.testCoverage}% |

## Module Types

| Type | Count |
|------|-------|
| ES Modules | ${metrics.moduleTypes.esm} |
| CommonJS | ${metrics.moduleTypes.cjs} |
| Mixed | ${metrics.moduleTypes.mixed} |

## File Breakdown

| Category | Count |
|----------|-------|
| Components | ${metrics.categoryBreakdown.components} |
| Hooks | ${metrics.categoryBreakdown.hooks} |
| Utilities | ${metrics.categoryBreakdown.utilities} |
| Services | ${metrics.categoryBreakdown.services} |
| Tests | ${metrics.categoryBreakdown.tests} |

## Largest Files

${metrics.largestFiles
  .slice(0, 5)
  .map((f) => `- **${f.path}**: ${f.lines} lines`)
  .join('\n')}

## Most Complex Files

${metrics.mostComplexFiles
  .slice(0, 5)
  .map((f) => `- **${f.path}**: Complexity ${f.complexity}`)
  .join('\n')}

## Recommendations

${recommendations
  .map(
    (r) => `
### ${r.title} (${r.priority} priority)
${r.description}
${r.files ? `\n**Affected files:**\n${r.files.map((f) => `- ${f}`).join('\n')}` : ''}
`
  )
  .join('')}

## Next Steps

1. Review high-priority recommendations
2. Consider running migration scripts for architectural improvements
3. Add tests for uncovered areas
4. Monitor complexity metrics over time
5. Standardize module types if mixed usage detected
`;
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const options = {
    verbose: args.includes('--verbose'),
    rootDir: args.find((arg) => arg.startsWith('--root='))?.split('=')[1] || process.cwd(),
  };

  const analyzer = new DynamicArchitectureAnalyzer(options);
  analyzer.analyze().catch((error) => {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  });
}

export default DynamicArchitectureAnalyzer;
