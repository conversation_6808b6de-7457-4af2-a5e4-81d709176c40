# Fix Broken Import Statements

**ID**: `fix-broken-imports`  
**Priority**: critical  
**Estimated Time**: 15-30 minutes

## Context
These imports are causing build errors and need immediate attention

## Description  
Update import statements that reference non-existent files

## Files to Modify
- packages/dashboard/src/routes/routes.tsx
- packages/dashboard/src/routes/routes.tsx
- packages/dashboard/src/pages/TradeForm.tsx
- packages/dashboard/src/pages/TradeAnalysis.tsx
- packages/dashboard/src/pages/TradeAnalysis.tsx




## Instructions for AI

## Fix Broken Import Statements

### Context
These import statements may be referencing files that have moved or don't exist.

### Instructions for Augment AI:

For each file with broken imports:

1. **Analyze the import statement**:
   - Check if the referenced file exists at the specified path
   - If not, search for the file in the current codebase
   - Determine the correct relative path

2. **Update the import**:
   - Replace the old path with the correct path
   - Ensure the import syntax is correct (ESM format)
   - Maintain any named imports or aliases

3. **Verify the fix**:
   - Check that the imported item is actually exported from the target file
   - Update export statements if necessary

### Example:
```typescript
// Before (broken)
import { someUtil } from '../../../utils/someUtil';

// After (fixed)
import { someUtil } from '@/utils/helpers/someUtil';
```

### Files to fix:
- **packages/dashboard/src/routes/routes.tsx**: Fix import `../layouts/MainLayout` (Relative import that might be broken)
- **packages/dashboard/src/routes/routes.tsx**: Fix import `../components/molecules/LoadingScreen` (Relative import that might be broken)
- **packages/dashboard/src/pages/TradeForm.tsx**: Fix import `../features/trade-journal/TradeForm` (Relative import that might be broken)
- **packages/dashboard/src/pages/TradeAnalysis.tsx**: Fix import `../features/trade-analysis` (Relative import that might be broken)
- **packages/dashboard/src/pages/TradeAnalysis.tsx**: Fix import `../components/FeatureErrorBoundary` (Relative import that might be broken)
- **packages/dashboard/src/pages/Dashboard.tsx**: Fix import `../features/trading-dashboard` (Relative import that might be broken)
- **packages/shared/src/components/organisms/DataCard.tsx**: Fix import `../molecules/Card` (Relative import that might be broken)
- **packages/shared/src/components/organisms/DataCard.tsx**: Fix import `../atoms/LoadingPlaceholder` (Relative import that might be broken)
- **packages/shared/src/components/organisms/DataCard.tsx**: Fix import `../molecules/EmptyState` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTableRow.tsx**: Fix import `../../types/trading` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTableFilters.tsx**: Fix import `../atoms/Input` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTableFilters.tsx**: Fix import `../atoms/Select` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTableFilters.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTableFilters.tsx**: Fix import `../../types/trading` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTable.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTable.tsx**: Fix import `../../types/trading` (Relative import that might be broken)
- **packages/shared/src/components/molecules/TradeTable.example.tsx**: Fix import `../../services/tradeStorage` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Table.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Modal.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/EmptyState.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/atoms/Select.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/shared/src/components/atoms/Input.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/dashboard/src/features/trading-dashboard/components/SetupAnalysis.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trading-dashboard/components/PerformanceChart.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trading-dashboard/components/MetricsPanel.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/TradeList.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/TradeList.tsx**: Fix import `../hooks/useTradeList` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradesTable.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradesTable.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeDetail.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeDetail.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysisTable.tsx**: Fix import `../hooks/tradeAnalysisState` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysisSummary.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysisFilter.tsx**: Fix import `../state/tradeAnalysisState` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysisCharts.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysis.tsx**: Fix import `../hooks/tradeAnalysisState` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TradeAnalysis.tsx**: Fix import `../hooks/useTradeAnalysis` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TimePerformanceChart.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/TimePerformanceChart.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/PerformanceSummary.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/PerformanceSummary.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/FilterPanel.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/FilterPanel.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/CategoryPerformanceChart.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/components/CategoryPerformanceChart.tsx**: Fix import `../hooks/TradeAnalysisContext` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/context/DailyGuideContext.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/context/DailyGuideContext.tsx**: Fix import `../api/dailyGuideApi` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/TradingPlan.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/MarketSummary.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/MarketOverview.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/MarketNews.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/MarketIndicators.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/KeyLevels.tsx**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/SecondarySetupSelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/PrimarySetupSelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/ParentPDArraySelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/LiquiditySelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/FVGSelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-setup-classification/DOLTargetSelector.tsx**: Fix import `../../constants/setupClassification` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx**: Fix import `../../constants/patternQuality` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx**: Fix import `../../constants/patternQuality` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-list/TradeListRow.tsx**: Fix import `../TradeList` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-list/TradeListHeader.tsx**: Fix import `../TradeList` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalFilters.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormHeader.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLEffectivenessRating.tsx**: Fix import `../../constants/dolAnalysis` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLDetailedAnalysis.tsx**: Fix import `../../constants/dolAnalysis` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-dol-analysis/DOLContextSelector.tsx**: Fix import `../../constants/dolAnalysis` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/ui/SentimentBadge.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/components/ui/PriorityTag.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trading-dashboard/hooks/useTradingDashboard.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeValidation.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeSubmission.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeList.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeJournal.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeFilters.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/hooks/useTradeCalculations.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/hooks/useDailyGuide.ts**: Fix import `../state` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/hooks/useDailyGuide.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/scripts/manage-assets.js**: Fix import `../public/assets/generate-placeholder-assets` (Relative import that might be broken)
- **packages/shared/src/theme/variants/lightTheme.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/shared/src/theme/variants/lightTheme.ts**: Fix import `../tokens` (Relative import that might be broken)
- **packages/shared/src/theme/variants/f1Theme.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/shared/src/theme/variants/f1Theme.ts**: Fix import `../tokens` (Relative import that might be broken)
- **packages/shared/src/components/templates/DashboardTemplate.stories.tsx**: Fix import `../molecules/Card` (Relative import that might be broken)
- **packages/shared/src/components/templates/DashboardTemplate.stories.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/organisms/DataCard.stories.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/organisms/DataCard.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Modal.stories.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Modal.stories.tsx**: Fix import `../atoms/Input` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Modal.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Card.stories.tsx**: Fix import `../atoms/Button` (Relative import that might be broken)
- **packages/shared/src/components/molecules/Card.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/shared/src/components/atoms/Tag.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/shared/src/components/atoms/Badge.stories.tsx**: Fix import `../../theme/ThemeProvider` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/constants/setupClassification.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/constants/patternQuality.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalContent.tsx**: Fix import `../TradeList` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Fix import `../../hooks/useTradeValidation` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Fix import `../../hooks` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Fix import `../TimePicker` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Fix import `../SelectDropdown` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Fix import `../../hooks/useTradeValidation` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Fix import `../../hooks` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Fix import `../SelectDropdown` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Fix import `../trade-setup-classification/SetupClassificationSection` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx**: Fix import `../../hooks/useTradeValidation` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx**: Fix import `../../types` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx**: Fix import `../../hooks/useTradeValidation` (Relative import that might be broken)
- **packages/shared/src/services/tradeStorage.ts**: Fix import `../types/trading` (Relative import that might be broken)
- **packages/dashboard/src/features/trade-analysis/services/tradeAnalysisApi.ts**: Fix import `../types` (Relative import that might be broken)
- **packages/dashboard/src/features/daily-guide/api/dailyGuideApi.ts**: Fix import `../types` (Relative import that might be broken)


## Validation
Run `yarn build` to verify all imports resolve correctly

## Notes
- This is an automatically generated task based on codebase analysis
- Maintain existing functionality while implementing improvements
- Test thoroughly after making changes
- Update related documentation if necessary
