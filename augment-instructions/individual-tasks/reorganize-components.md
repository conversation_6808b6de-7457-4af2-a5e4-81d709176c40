# Reorganize Component Structure

**ID**: `reorganize-components`  
**Priority**: medium  
**Estimated Time**: 1-2 hours

## Context
Components should be organized by type (UI, feature, layout)

## Description  
Move components to appropriate categorical folders

## Files to Modify
- packages/shared/src/styled.d.ts
- packages/dashboard/src/TestApp.tsx
- packages/dashboard/src/SimpleApp.tsx
- packages/dashboard/src/MinimalApp.tsx
- packages/dashboard/src/App.tsx




## Instructions for AI

## Reorganize Component Structure

### Context
Components should be organized by their type and responsibility.

### Instructions for Augment AI:

1. **Categorize each component**:
   - **UI**: Reusable, presentational components (buttons, inputs, cards)
   - **Feature**: Business logic components tied to specific features
   - **Layout**: App structure (headers, sidebars, navigation)
   - **Pages**: Top-level route components

2. **Move to appropriate folders**:
   - `src/components/ui/` - Basic reusable components
   - `src/components/feature/` - Feature-specific components
   - `src/components/layout/` - Layout and navigation
   - `src/pages/` - Page components

3. **Update all imports**:
   - Find all files that import these components
   - Update import paths to new locations
   - Consider using path aliases (@/components/ui/Button)

4. **Create/update index files**:
   - Each category folder should export its components
   - Main components/index.ts should export all categories

### Organization example:
```
src/
├── components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── index.ts
│   ├── feature/
│   │   ├── TradingDashboard.tsx
│   │   ├── TradeForm.tsx
│   │   └── index.ts
│   └── layout/
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── index.ts
└── pages/
    ├── Dashboard.tsx
    └── Settings.tsx
```

### Components to reorganize:
- **packages/shared/src/styled.d.ts**: Move to appropriate category folder
- **packages/dashboard/src/TestApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/SimpleApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/MinimalApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/App.tsx**: Move to appropriate category folder
- **packages/shared/src/theme/ThemeProvider.tsx**: Move to appropriate category folder
- **packages/shared/src/state/createStoreContext.tsx**: Move to appropriate category folder
- **packages/shared/src/components/base.tsx**: Move to appropriate category folder


## Validation
Verify all imports still work after reorganization

## Notes
- This is an automatically generated task based on codebase analysis
- Maintain existing functionality while implementing improvements
- Test thoroughly after making changes
- Update related documentation if necessary
